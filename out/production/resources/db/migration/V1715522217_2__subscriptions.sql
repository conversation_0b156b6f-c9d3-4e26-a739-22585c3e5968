CREATE TABLE subscription_plans
(
    id       VARCHAR(255)     NOT NULL,
    price    DOUBLE PRECISION NOT NULL,
    currency VARCHAR(10)      NOT NULL,
    PRIMARY KEY (id)
);

CREATE TABLE subscriptions
(
    id                   VARCHAR(255) NOT NULL,
    user_id              VARCHAR(255) NOT NULL,
    subscription_plan_id VARCHAR(255) NOT NULL,
    created_on           DATE         NOT NULL,
    trial_end            DATE,
    current_period_start DATE         NOT NULL,
    current_period_end   DATE         NOT NULL,
    status               VARCHAR(50)  NOT NULL,
    FOREIGN KEY (subscription_plan_id) REFERENCES subscription_plans (id),
    PRIMARY KEY (id)
);