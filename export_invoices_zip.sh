#!/bin/bash

# Variables
BUCKET_NAME="mpg-invoices"
START_NUMBER=$1
END_NUMBER=$2
TARGET_DIR="/Users/<USER>/Documents/Invoices/SASHKO-MPG"

# Get the previous month in MM.YYYY format
PREVIOUS_MONTH=$(date -v-1m +%m.%Y)
NEW_DIR="$TARGET_DIR/$PREVIOUS_MONTH"

# Validate input
if ! [[ "$START_NUMBER" =~ ^[0-9]+$ ]] || ! [[ "$END_NUMBER" =~ ^[0-9]+$ ]]; then
    echo "Error: START_NUMBER and END_NUMBER must be valid integers."
    exit 1
fi

if [ "$START_NUMBER" -gt "$END_NUMBER" ]; then
    echo "Error: START_NUMBER must be less than or equal to END_NUMBER."
    exit 1
fi

# Create a new directory for the invoices
mkdir -p $NEW_DIR

# Download invoices by range
for (( i=$START_NUMBER; i<=$END_NUMBER; i++ ))
do
    # Format the invoice number with leading zeros to match the 10-digit format
    OBJECT_NAME="invoices/$(printf "%010d" $i).pdf"
    FILE_NAME="$(printf "%010d" $i).pdf"
    aws s3 cp s3://$BUCKET_NAME/$OBJECT_NAME $NEW_DIR/$FILE_NAME --profile mpg-prod
    if [ $? -eq 0 ]; then
        echo "Downloaded $FILE_NAME"
    else
        echo "Failed to download $FILE_NAME"
    fi
done