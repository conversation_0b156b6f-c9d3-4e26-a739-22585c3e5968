# 📦 storage-utils

Shared Kotlin-based utility library for handling storage-related functionality in the MyProgressGuru platform. Includes
support for AWS S3, JPA entities, QueryDSL, and domain utilities.

---

## 🛠 Build

> This project uses Kotlin, QueryDSL (with KAPT), and Java 17+. Due to internal Java compiler access restrictions
> introduced in JDK 17, we require an additional JVM argument during compilation.

### ✅ Prerequisites

- JDK 17 or higher
- Maven 3.6+
- Git credentials (for pushing to GitLab Maven repo)

### 🧩 DB Migration

```sql
CREATE TABLE shedlock
(
    name       VARCHAR(64),
    lock_until TIMESTAMP(3) NULL,
    locked_at  TIMESTAMP(3) NULL,
    locked_by  VA<PERSON>HA<PERSON>(255),
    PRIMARY KEY (name)
);

CREATE TABLE storage_objects
(
    id          VARCHAR(255) NOT NULL,
    key         VARCHAR(255) NOT NULL,
    url         VARCHAR(255) NOT NULL,
    is_assigned BOOLEAN      NOT NULL,
    is_uploaded BOOLEAN      NOT NULL,
    PRIMARY KEY (id)
);

CREATE TABLE smart_notes
(
    id           VARCHAR(255)             NOT NULL PRIMARY KEY,
    type         VARCHAR(255),
    created_on   TIMESTAMP WITH TIME ZONE NOT NULL,
    creator_id   VARCHAR(255)             NOT NULL,
    creator_name VARCHAR(255)             NOT NULL,
    content      TEXT,
    media_url    VARCHAR(255)
);
```

---

### 🔧 Deploy

```bash
MAVEN_OPTS="--add-exports=jdk.compiler/com.sun.tools.javac.main=ALL-UNNAMED" mvn clean deploy -DperformRelease=true