#!/bin/sh

start_time=$(date +%s)

export ENV=prod

export DOCKER_REGISTRY=registry.gitlab.com
export APP_NAME=rtc-service
export PROJECT_NAME=my-progress-guru/backend/$APP_NAME
export GIT_COMMIT=$(git log -1 --format=%h)
export IMAGE_NAME=$DOCKER_REGISTRY/$PROJECT_NAME:$ENV
export IMAGE_NAME_TAGGED=$IMAGE_NAME-$GIT_COMMIT
export SERVICE_NAME=mpg-"$ENV"_"$APP_NAME"

docker buildx build \
--push \
--platform linux/amd64,linux/arm64 \
--tag "$IMAGE_NAME" .

ssh <EMAIL> "docker pull $IMAGE_NAME && docker service update $SERVICE_NAME --image=$IMAGE_NAME --force --with-registry-auth"

end_time=$(date +%s)
elapsed_time=$((end_time - start_time))
minutes=$((elapsed_time / 60))
seconds=$((elapsed_time % 60))

echo "Building and deploying took $minutes minutes and $seconds seconds."