import { Module } from '@nestjs/common';
import { Gateway } from './websocket/gateway';
import { KeycloakService } from './services/keycloak.service';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { SqsModule } from '@ssut/nestjs-sqs';
import { SqsOptions } from '@ssut/nestjs-sqs/dist/sqs.types';
import { SQSClient } from '@aws-sdk/client-sqs';

@Module({
  imports: [
    HttpModule,
    ConfigModule.forRoot({ envFilePath: ['.env.local'], isGlobal: true }),
    SqsModule.registerAsync({
      inject: [ConfigService],
      useFactory: (configService: ConfigService): SqsOptions => ({
        consumers: [
          {
            name: 'ws_messages.fifo',
            queueUrl: configService.get('WS_MESSAGES_QUEUE_URL'),
            region: configService.get<string>('AWS_REGION'),
            sqs: new SQSClient({
              endpoint:
                configService.get<string>('ENVIRONMENT') === 'local'
                  ? 'http://localhost:4566'
                  : undefined,
            }),
          },
        ],
      }),
    }),
  ],
  controllers: [],
  providers: [Gateway, KeycloakService],
})
export class AppModule {}
