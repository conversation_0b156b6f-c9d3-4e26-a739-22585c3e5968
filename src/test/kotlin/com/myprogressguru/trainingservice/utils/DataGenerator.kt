package com.myprogressguru.trainingservice.utils

import com.myprogressguru.trainingservice.entity.Trainee
import com.myprogressguru.trainingservice.entity.Trainer
import com.myprogressguru.trainingservice.enumeration.Gender
import java.time.LocalDate

object DataGenerator {

    fun createTrainee(): Trainee = Trainee(
        id = "TraineeID",
        firstName = "Pesho",
        lastName = "Peshov",
        gender = Gender.MALE,
        heightInCm = 195,
        birthdate = LocalDate.now().minusYears(20),
        occupation = "Programmer",
        trainer = Trainer(id = "TrainerID")
    )
}