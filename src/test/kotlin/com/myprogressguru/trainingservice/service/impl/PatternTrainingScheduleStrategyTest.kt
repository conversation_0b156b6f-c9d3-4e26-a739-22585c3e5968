import com.myprogressguru.commonutils.service.DateService
import com.myprogressguru.trainingservice.entity.TrainingSchedule
import com.myprogressguru.trainingservice.enumeration.TrainingScheduleType
import com.myprogressguru.trainingservice.service.TrainingMapper
import com.myprogressguru.trainingservice.service.impl.PatternTrainingScheduleStrategy
import com.myprogressguru.trainingservice.utils.DataGenerator
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Spy
import org.mockito.junit.jupiter.MockitoExtension
import java.time.LocalDate
import java.time.ZonedDateTime
import java.util.stream.Stream

@ExtendWith(MockitoExtension::class)
class PatternTrainingScheduleStrategyTest {

    @Mock
    private lateinit var trainingMapper: TrainingMapper

    @Spy
    private lateinit var dateService: DateService

    @InjectMocks
    private lateinit var patternTrainingScheduleStrategy: PatternTrainingScheduleStrategy

    @ParameterizedTest(name = "Pattern {0}")
    @MethodSource("getNextWorkoutDateParams")
    fun `test getNextWorkoutDate`(
        params: TrainingScheduleTestParams
    ) {
        val trainingSchedule = TrainingSchedule(
            type = TrainingScheduleType.PATTERN,
            trainee = DataGenerator.createTrainee(),
            isActive = true,
            pattern = params.pattern,
            createdOn = ZonedDateTime.now()
        )

        val nextWorkoutDate =
            patternTrainingScheduleStrategy.getNextWorkoutDate(trainingSchedule, params.workoutHistory, params.today)

        assertEquals(params.expectedNextWorkoutDate, nextWorkoutDate)
    }

    companion object {
        @JvmStatic
        fun getNextWorkoutDateParams(): Stream<TrainingScheduleTestParams> {
            return Stream.of(
                TrainingScheduleTestParams(
                    today = LocalDate.of(2023, 4, 17),
                    workoutHistory = listOf(
                        LocalDate.of(2023, 4, 15),
                    ),
                    pattern = "1",
                    expectedNextWorkoutDate = LocalDate.of(2023, 4, 17)
                ),
                TrainingScheduleTestParams(
                    today = LocalDate.of(2023, 4, 17),
                    workoutHistory = listOf(
                        LocalDate.of(2023, 4, 17),
                    ),
                    pattern = "1",
                    expectedNextWorkoutDate = LocalDate.of(2023, 4, 18)
                ),
                TrainingScheduleTestParams(
                    today = LocalDate.of(2023, 4, 17),
                    workoutHistory = listOf(
                        LocalDate.of(2023, 4, 16),
                    ),
                    pattern = "10",
                    expectedNextWorkoutDate = LocalDate.of(2023, 4, 18)
                ),
                TrainingScheduleTestParams(
                    today = LocalDate.of(2023, 4, 17),
                    workoutHistory = listOf(
                        LocalDate.of(2023, 4, 15),
                        LocalDate.of(2023, 4, 16),
                    ),
                    pattern = "110",
                    expectedNextWorkoutDate = LocalDate.of(2023, 4, 18)
                ),
                TrainingScheduleTestParams(
                    today = LocalDate.of(2023, 4, 16),
                    workoutHistory = listOf(
                        LocalDate.of(2023, 4, 15),
                    ),
                    pattern = "110",
                    expectedNextWorkoutDate = LocalDate.of(2023, 4, 16)
                ),
                TrainingScheduleTestParams(
                    today = LocalDate.of(2023, 4, 16),
                    workoutHistory = listOf(),
                    pattern = "110",
                    expectedNextWorkoutDate = LocalDate.of(2023, 4, 16)
                ),
                TrainingScheduleTestParams(
                    today = LocalDate.of(2023, 4, 17),
                    workoutHistory = listOf(
                        LocalDate.of(2023, 4, 15),
                        LocalDate.of(2023, 4, 16),
                    ),
                    pattern = "1100",
                    expectedNextWorkoutDate = LocalDate.of(2023, 4, 19)
                ),
                TrainingScheduleTestParams(
                    description = "long time without training",
                    today = LocalDate.of(2023, 4, 22),
                    workoutHistory = listOf(
                        LocalDate.of(2023, 4, 8),
                        LocalDate.of(2023, 4, 9),
                    ),
                    pattern = "1100",
                    expectedNextWorkoutDate = LocalDate.of(2023, 4, 22)
                ),
                TrainingScheduleTestParams(
                    today = LocalDate.of(2023, 4, 23),
                    workoutHistory = listOf(
                        LocalDate.of(2023, 4, 9),
                        LocalDate.of(2023, 4, 22),
                    ),
                    pattern = "1100",
                    expectedNextWorkoutDate = LocalDate.of(2023, 4, 23)
                ),
                TrainingScheduleTestParams(
                    today = LocalDate.of(2023, 4, 18),
                    workoutHistory = listOf(
                        LocalDate.of(2023, 4, 15),
                        LocalDate.of(2023, 4, 16),
                        LocalDate.of(2023, 4, 18)
                    ),
                    pattern = "11010",
                    expectedNextWorkoutDate = LocalDate.of(2023, 4, 20)
                )
            )
        }
    }

    data class TrainingScheduleTestParams(
        val description: String = "",
        val today: LocalDate,
        val workoutHistory: List<LocalDate>,
        val pattern: String,
        val expectedNextWorkoutDate: LocalDate
    ) {
        override fun toString(): String {
            return "$pattern | today($today) | last workout(${workoutHistory.last()}) | expected($expectedNextWorkoutDate) | $description"
        }
    }
}