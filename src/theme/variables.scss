// Ionic Variables and Theming. For more info, please see:
// http://ionicframework.com/docs/theming/

@font-face {
  src: url('/assets/font/Bahnschrift.woff2') format('woff2-variations');
  font-family: 'Bahnschrift';
  font-style: normal;
}

@font-face {
  src: url('/assets/font/Bahnschrift Regular.ttf') format('truetype');
  font-family: 'Bahnschrift-Regular';
  font-style: normal;
}

@font-face {
  font-family: "Proxima Nova";
  src: url("/assets/font/ProximaNova-Regular.woff") format("woff"),
  url("/assets/font/ProximaNova-Regular.ttf") format("tff");
  font-weight: 400;
}

@font-face {
  font-family: "Proxima Nova";
  src: url("/assets/font/ProximaNova-Semibold.woff") format("woff"),
  url("/assets/font/ProximaNova-Semibold.ttf") format("tff");
  font-weight: 500;
}

@font-face {
  font-family: "Proxima Nova";
  src: url("/assets/font/ProximaNova-Bold.woff") format("woff"),
  url("/assets/font/ProximaNova-Bold.ttf") format("tff");
  font-weight: 600;
}

@font-face {
  font-family: "DINPro";
  src: url("/assets/font/DINPro-Bold.woff") format("woff"),
  url("/assets/font/DINPro-Bold.ttf") format("tff");
  font-weight: 500;
}

/** Ionic CSS Variables **/
:root {
  --ion-font-family: "Bahnschrift", "Bahnschrift-Regular";

  --ion-color-primary: #FF0F0F;
  --ion-color-primary-rgb: 255, 15, 15;
  --ion-color-primary-contrast: #ffffff;
  --ion-color-primary-contrast-rgb: 255, 255, 255;
  --ion-color-primary-shade: #b22f2f;
  --ion-color-primary-tint: #cf4949;

  --ion-color-secondary: #2665cf;
  --ion-color-secondary-rgb: 38, 101, 207;
  --ion-color-secondary-contrast: #ffffff;
  --ion-color-secondary-contrast-rgb: 255, 255, 255;
  --ion-color-secondary-shade: #2159b6;
  --ion-color-secondary-tint: #3c74d4;

  --ion-color-tertiary: #00ffe1;
  --ion-color-tertiary-rgb: 0, 255, 225;
  --ion-color-tertiary-contrast: #000000;
  --ion-color-tertiary-contrast-rgb: 0, 0, 0;
  --ion-color-tertiary-shade: #00e0c6;
  --ion-color-tertiary-tint: #1affe4;

  --ion-color-success: #2dd36f;
  --ion-color-success-rgb: 45, 211, 111;
  --ion-color-success-contrast: #ffffff;
  --ion-color-success-contrast-rgb: 255, 255, 255;
  --ion-color-success-shade: #28ba62;
  --ion-color-success-tint: #42d77d;

  --ion-color-warning: #ffc409;
  --ion-color-warning-rgb: 255, 196, 9;
  --ion-color-warning-contrast: #000000;
  --ion-color-warning-contrast-rgb: 0, 0, 0;
  --ion-color-warning-shade: #e0ac08;
  --ion-color-warning-tint: #ffca22;

  --ion-color-danger: #ff0022;
  --ion-color-danger-rgb: 255, 0, 34;
  --ion-color-danger-contrast: #ffffff;
  --ion-color-danger-contrast-rgb: 255, 255, 255;
  --ion-color-danger-shade: #e0001e;
  --ion-color-danger-tint: #ff1a38;

  --ion-color-dark: #222428;
  --ion-color-dark-rgb: 34, 36, 40;
  --ion-color-dark-contrast: #ffffff;
  --ion-color-dark-contrast-rgb: 255, 255, 255;
  --ion-color-dark-shade: #1e2023;
  --ion-color-dark-tint: #383a3e;

  --ion-color-medium: #92949c;
  --ion-color-medium-rgb: 146, 148, 156;
  --ion-color-medium-contrast: #ffffff;
  --ion-color-medium-contrast-rgb: 255, 255, 255;
  --ion-color-medium-shade: #808289;
  --ion-color-medium-tint: #9d9fa6;

  --ion-color-light: #f4f5f8;
  --ion-color-light-rgb: 244, 245, 248;
  --ion-color-light-contrast: #000000;
  --ion-color-light-contrast-rgb: 0, 0, 0;
  --ion-color-light-shade: #d7d8da;
  --ion-color-light-tint: #f5f6f9;

  --ion-background-color: rgba(255, 255, 255, 0.7);
  --ion-overlay-background-color: rgba(255, 255, 255);

  --plyr-color-main: var(--ion-color-primary);


  //MPG Custom Variables

  --typefaces-family-display: "Bahnschrift";
  --typefaces-weight-bold: "Bold";
  --typefaces-weight-semibold: "Semibold";
  --typefaces-weight-medium: "Medium";
  --typefaces-weight-regular: "Regular";
  --typefaces-family-text: "Bahnschrift";
  --typefaces-weight-light: "Light";
  --typefaces-size-3xl: 24px;
  --typefaces-size-2xl: 16px;
  --typefaces-size-xl: 12px;
  --typefaces-size-l: 10px;
  --typefaces-size-m: 8px;
  --typefaces-size-s: 6px;
  --typefaces-size-xs: 4px;
  --color-background-primary-black-1000: #000000;
  --color-background-primary-black-900: #020202;
  --color-accent-primary-red-900: #FF0F0F;
  --color-accent-primary-red-800: #FF0F1E;
  --color-accent-primary-red-700: #FF3340;
  --color-accent-primary-red-600: #FF4C58;
  --color-accent-secondary-blue-900: #006FE5;
  --color-accent-secondary-blue-600: #007BFF;
  --color-accent-secondary-purple-900: #A882DD;
  --color-background-primary-black-800: #070707;
  --color-background-primary-black-700: #0A0A0A;
  --color-background-primary-black-600: #0C0C0C;
  --color-background-primary-black-500: #0F0F0F;
  --color-background-primary-black-450: #141414;
  --color-background-primary-black-400: #1E1E1E;
  --color-background-primary-black-300: #4D4C4C;
  --color-background-primary-black-200: #666666;
  --color-background-secondary-white-900: #BFBFBF;
  --color-background-secondary-white-800: #D8D8D8;
  --color-background-secondary-white-700: #F2F2F2;
  --color-warning-main-yellow-600: #FFB333;
  --color-warning-alert-yellow-900: #FFA000;
  --color-warning-yellow-300: #FFB333;
  --color-success-main-green-900: #2CAF31;
  --color-success-confirmation-green-600: #1CFF27;
  --color-destructive-orange-900: #E65100;
  --color-destructive-orange-800: #FF5900;
  --radius-extra-large: 64px;
  --radius-large: 24px;
  --radius-medium: 16px;
  --radius-medium-small: 8px;
  --radius-small: 6px;
  --radius-extra_small: 4px;
  --radius-2extra-small: 2px;
  --radius-none: 0px;
  --spacing-4xl: 512px;
  --spacing-3xl: 318px;
  --spacing-2xl: 256px;
  --spacing-xl: 192px;
  --spacing-m: 128px;
  --spacing-l: 64px;
  --spacing-s: 24px;
  --spacing-xs: 16px;
  --spacing-2xs: 8px;
  --spacing-3xs: 4px;
  --color-background-secondary-white-600: #F4F4FF;
  --color-background-secondary-white-500: #FFFFFF;
  --strokes-stroke-triatary-button-nostring: 0.75px;
  --strokes-stroke-triatary-button-no-string-small: 0.25px;
  --yellow-700: #FFBF00;
  --green-500: #53FF45;
  --text-field-stroke-hover-blue-200: #007BFF;
}

//@media (prefers-color-scheme: dark) {
/*
 * Dark Colors
 * -------------------------------------------
 */

body.dark {
  --ion-color-primary: #FF0F0F;
  --ion-color-primary-rgb: 255, 15, 15;
  --ion-color-primary-contrast: #ffffff;
  --ion-color-primary-contrast-rgb: 255, 255, 255;
  --ion-color-primary-shade: #ea5555;
  --ion-color-primary-tint: #cf4949;

  --ion-color-secondary: #2665cf;
  --ion-color-secondary-rgb: 38, 101, 207;
  --ion-color-secondary-contrast: #ffffff;
  --ion-color-secondary-contrast-rgb: 255, 255, 255;
  --ion-color-secondary-shade: #2159b6;
  --ion-color-secondary-tint: #3c74d4;

  --ion-color-tertiary: #6a64ff;
  --ion-color-tertiary-rgb: 106, 100, 255;
  --ion-color-tertiary-contrast: #ffffff;
  --ion-color-tertiary-contrast-rgb: 255, 255, 255;
  --ion-color-tertiary-shade: #5d58e0;
  --ion-color-tertiary-tint: #7974ff;

  --ion-color-success: #2fdf75;
  --ion-color-success-rgb: 47, 223, 117;
  --ion-color-success-contrast: #000000;
  --ion-color-success-contrast-rgb: 0, 0, 0;
  --ion-color-success-shade: #29c467;
  --ion-color-success-tint: #44e283;

  --ion-color-warning: #ffd534;
  --ion-color-warning-rgb: 255, 213, 52;
  --ion-color-warning-contrast: #000000;
  --ion-color-warning-contrast-rgb: 0, 0, 0;
  --ion-color-warning-shade: #e0bb2e;
  --ion-color-warning-tint: #ffd948;

  --ion-color-danger: #ff4961;
  --ion-color-danger-rgb: 255, 73, 97;
  --ion-color-danger-contrast: #ffffff;
  --ion-color-danger-contrast-rgb: 255, 255, 255;
  --ion-color-danger-shade: #e04055;
  --ion-color-danger-tint: #ff5b71;

  --ion-color-dark: #222428;
  --ion-color-dark-rgb: 34, 36, 40;
  --ion-color-dark-contrast: #ffffff;
  --ion-color-dark-contrast-rgb: 255, 255, 255;
  --ion-color-dark-shade: #1e2023;
  --ion-color-dark-tint: #383a3e;

  --ion-color-medium: #989aa2;
  --ion-color-medium-rgb: 152, 154, 162;
  --ion-color-medium-contrast: #000000;
  --ion-color-medium-contrast-rgb: 0, 0, 0;
  --ion-color-medium-shade: #86888f;
  --ion-color-medium-tint: #a2a4ab;

  --ion-color-light: #222428;
  --ion-color-light-rgb: 34, 36, 40;
  --ion-color-light-contrast: #ffffff;
  --ion-color-light-contrast-rgb: 255, 255, 255;
  --ion-color-light-shade: #1e2023;
  --ion-color-light-tint: #383a3e;
}

/*
 * iOS Dark Theme
 * -------------------------------------------
 */

.ios body.dark {
  --ion-background-color: rgba(0, 0, 0, 0.7);
  --ion-background-color-rgb: 0, 0, 0;
  --ion-overlay-background-color: rgba(0, 0, 0);

  --ion-text-color: #ffffff;
  --ion-text-color-rgb: 255, 255, 255;

  --ion-color-step-50: #0d0d0d;
  --ion-color-step-100: #1a1a1a;
  --ion-color-step-150: #262626;
  --ion-color-step-200: #333333;
  --ion-color-step-250: #404040;
  --ion-color-step-300: #4d4d4d;
  --ion-color-step-350: #595959;
  --ion-color-step-400: #666666;
  --ion-color-step-450: #737373;
  --ion-color-step-500: #808080;
  --ion-color-step-550: #8c8c8c;
  --ion-color-step-600: #999999;
  --ion-color-step-650: #a6a6a6;
  --ion-color-step-700: #b3b3b3;
  --ion-color-step-750: #bfbfbf;
  --ion-color-step-800: #cccccc;
  --ion-color-step-850: #d9d9d9;
  --ion-color-step-900: #e6e6e6;
  --ion-color-step-950: #f2f2f2;

  --ion-item-background: rgba(0, 0, 0, 0.7);

  --ion-card-background: rgba(28, 28, 29, 0.7);
}

.ios body.dark ion-modal {
  --ion-background-color: var(--ion-color-step-100);
  --ion-toolbar-background: var(--ion-color-step-150);
  --ion-toolbar-border-color: var(--ion-color-step-250);
}

/*
 * Material Design Dark Theme
 * -------------------------------------------
 */

.md body.dark {
  --ion-background-color: rgba(18, 18, 18, 0.7);
  --ion-background-color-rgb: 18, 18, 18;

  --ion-text-color: #ffffff;
  --ion-text-color-rgb: 255, 255, 255;

  --ion-border-color: #222222;

  --ion-color-step-50: #1e1e1e;
  --ion-color-step-100: #2a2a2a;
  --ion-color-step-150: #363636;
  --ion-color-step-200: #414141;
  --ion-color-step-250: #4d4d4d;
  --ion-color-step-300: #595959;
  --ion-color-step-350: #656565;
  --ion-color-step-400: #717171;
  --ion-color-step-450: #7d7d7d;
  --ion-color-step-500: #898989;
  --ion-color-step-550: #949494;
  --ion-color-step-600: #a0a0a0;
  --ion-color-step-650: #acacac;
  --ion-color-step-700: #b8b8b8;
  --ion-color-step-750: #c4c4c4;
  --ion-color-step-800: #d0d0d0;
  --ion-color-step-850: #dbdbdb;
  --ion-color-step-900: #e7e7e7;
  --ion-color-step-950: #f3f3f3;

  --ion-item-background: rgba(30, 30, 30, 0.7);

  --ion-toolbar-background: rgba(31, 31, 31, 0.7);

  --ion-tab-bar-background: rgba(31, 31, 31, 0.7);

  --ion-card-background: rgba(30, 30, 30, 0.7);
}

//}
