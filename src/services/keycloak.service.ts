import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { map, Observable, of } from 'rxjs';

@Injectable()
export class KeycloakService {
  private readonly keycloakAddress =
    this.configService.get<string>('KEYCLOAK_ADDRESS');
  private readonly realm = this.configService.get<string>('KEYCLOAK_REALM');
  private readonly clientId =
    this.configService.get<string>('KEYCLOAK_CLIENT_ID');
  private readonly clientSecret = this.configService.get<string>(
    'KEYCLOAK_CLIENT_SECRET',
  );

  constructor(
    private readonly configService: ConfigService,
    private readonly http: HttpService,
  ) {}

  isValid(token: string): Observable<{ isValid: boolean; userId?: string }> {
    if (!token) {
      return of({ isValid: false });
    }

    return this.http
      .post<{ active: boolean; sub: string }>(
        `${this.keycloakAddress}/realms/${this.realm}/protocol/openid-connect/token/introspect`,
        {
          client_id: this.clientId,
          client_secret: this.clientSecret,
          token: token || '',
        },
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        },
      )
      .pipe(
        map((res) => {
          return { isValid: res.data.active, userId: res.data.sub };
        }),
      );
  }
}
