import { Server, Socket } from 'socket.io';
import {
  OnGatewayConnection,
  OnGatewayDisconnect,
  WebSocketGateway,
  WebSocketServer,
} from '@nestjs/websockets';
import { KeycloakService } from '../services/keycloak.service';
import { Logger } from '@nestjs/common';
import { SqsMessageHandler } from '@ssut/nestjs-sqs';

@WebSocketGateway({cors: {origin: '*'}})
export class Gateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  private readonly server: Server;
  private readonly userIdConnectionsMap: Record<string, Socket[]> = {};
  private readonly clientIdUserIdMap: Record<string, string> = {};

  private readonly logger: Logger = new Logger(Gateway.name);

  constructor(private readonly keycloakService: KeycloakService) {}

  handleConnection(client: Socket): void {
    const token = client.handshake.query.token as string;
    this.keycloakService.isValid(token).subscribe(({ isValid, userId }) => {
      if (!isValid) {
        this.logger.log(
          `Connection attempt failed. Invalid token: ${token || 'EMPTY'}.`,
        );
        client.disconnect();
        return;
      }

      if (!this.userIdConnectionsMap[userId]) {
        this.userIdConnectionsMap[userId] = [];
      }
      this.userIdConnectionsMap[userId] =
        this.userIdConnectionsMap[userId].concat(client);
      this.clientIdUserIdMap[client.id] = userId;
    });
  }

  handleDisconnect(client: Socket): void {
    const userId = this.clientIdUserIdMap[client.id];
    this.userIdConnectionsMap[userId] = this.userIdConnectionsMap[
      userId
    ].filter((c) => c.id !== client.id);
    delete this.clientIdUserIdMap[client.id];
  }

  @SqsMessageHandler('ws_messages.fifo', false)
  public async handleMessage(message: any) {
    const request: SendWebSocketMessageRequest = JSON.parse(message.Body);
    const { userId, action, payload } = request;

    const clients = this.userIdConnectionsMap[userId] || [];
    clients.forEach((c) => c.emit(action, payload));
  }
}

interface SendWebSocketMessageRequest {
  userId: string;
  action: string;
  payload: any;
}
