drop table nutrition_targets;

CREATE TABLE nutrition_plans
(
    id                          VARCHAR(255) PRIMARY KEY,
    trainee_id                  VARCHAR(255) NOT NULL,
    created_on                  DATE         NOT NULL,
    maintenance_weekly_calories INT          NOT NULL,
    target_weekly_calories      INT          NOT NULL,
    weekly_caloric_diff         INT          NOT NULL
);

CREATE TABLE nutrition_targets
(
    id                 VARCHAR(255) PRIMARY KEY,
    type               VARCHAR(255) NOT NULL,
    frequency_per_week INT          NOT NULL,
    nutrition_info_id  VARCHAR(255) NOT NULL,
    nutrition_plan_id  VARCHAR(255) NOT NULL,

    CONSTRAINT fk_nutrition_info
        FOREIGN KEY (nutrition_info_id) REFERENCES nutrition_info (id),

    CONSTRAINT fk_nutrition_plan
        FOREIGN KEY (nutrition_plan_id) REFERENCES nutrition_plans (id)
);