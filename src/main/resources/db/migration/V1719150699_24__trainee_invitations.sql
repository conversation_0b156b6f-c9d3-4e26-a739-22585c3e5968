create table trainee_invitations
(
    id           varchar(255) primary key,
    trainer_id   varchar(255)          not null,
    email        varchar(255),
    first_name   varchar(255),
    last_name    varchar(255),
    gender       varchar(50),
    height_in_cm int,
    birthdate    date,
    occupation   varchar(255),
    created_on   timestamp with time zone,
    filled       boolean default false not null,

    constraint fk_trainer
        foreign key (trainer_id) references trainers (id)
);