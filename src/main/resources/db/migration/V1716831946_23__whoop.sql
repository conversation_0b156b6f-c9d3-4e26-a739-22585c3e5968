create table integrations
(
    id                      varchar(255)             not null,
    external_user_id        varchar(255)             not null,
    created_on              date,
    type                    varchar(255)             not null,
    access_token            varchar(255)             not null,
    access_token_expires_at timestamp with time zone not null,
    refresh_token           varchar(255)             not null,
    trainee_id              varchar(255),
    primary key (id),
    constraint fk_trainee
        foreign key (trainee_id) references trainees
);

alter table integrations
    add constraint integrations_unique_idx unique (type, trainee_id, external_user_id);

create table whoop_records
(
    id         varchar(255) not null,
    date       date,
    sleep      jsonb,
    recovery   jsonb,
    trainee_id varchar(255),
    primary key (id),
    constraint fk_trainee
        foreign key (trainee_id) references trainees
);

alter table sleep_quality_records
    add column whoop_record_id varchar(255) unique,
    add
        constraint fk_whoop_record
            foreign key (whoop_record_id) references whoop_records;

alter table sleep_quality_records
    add constraint whoop_record_id_unique unique (whoop_record_id);