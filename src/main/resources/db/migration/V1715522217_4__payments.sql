CREATE TABLE payments
(
    id                VARCHAR(255),
    user_id           VARCHAR(255)             NOT NULL,
    card_pan          VARCHAR(255)             NOT NULL,
    card_expiration   VARCHAR(255)             NOT NULL,
    product_name      VARCHAR(255)             NOT NULL,
    ipc_trn_ref       VARCHAR(255)             NOT NULL,
    payment_reference VARCHAR(255)             NOT NULL,
    amount            DOUBLE PRECISION         NOT NULL,
    currency          VARCHAR(10)              NOT NULL,
    status            VARCHAR(255)             NOT NULL,
    status_msg        TEXT                     NOT NULL,
    created_on        TIMESTAMP WITH TIME ZONE NOT NULL,
    PRIMARY KEY (id)
);
