ALTER TABLE bodyweight_records
    ALTER COLUMN entered_on TYPE TIMESTAMP WITH TIME ZONE;

ALTER TABLE training_time_schedules
    ALTER COLUMN created_on TYPE TIMESTAMP WITH TIME ZONE;

ALTER TABLE fitness_states
    ALTER COLUMN created_on TYPE TIMESTAMP WITH TIME ZONE;

ALTER TABLE workout_records
    ALTER COLUMN created_on TYPE TIMESTAMP WITH TIME ZONE;

ALTER TABLE workout_records
    ALTER COLUMN started_on TYPE TIMESTAMP WITH TIME ZONE;

ALTER TABLE workout_records
    ALTER COLUMN ended_on TYPE TIMESTAMP WITH TIME ZONE;

ALTER TABLE training_schedules
    ALTER COLUMN created_on TYPE TIMESTAMP WITH TIME ZONE;

ALTER TABLE scheduled_rest_days
    ALTER COLUMN created_on TYPE TIMESTAMP WITH TIME ZONE;