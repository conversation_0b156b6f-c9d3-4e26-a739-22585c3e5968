create table billing_information
(
    id          varchar(255) not null,
    user_id     varchar(255),
    email       varchar(255) not null,
    first_name  varchar(255) not null,
    middle_name varchar(255),
    last_name   varchar(255) not null,
    city        varchar(255) not null,
    country     varchar(255) not null,
    address     varchar(255) not null,
    primary key (id)
);

create table invoices
(
    id                     varchar(255)             not null,
    date                   date                     not null,
    number                 bigint                   not null,
    product_name           varchar(255)             not null,
    amount                 double precision         not null,
    currency               varchar(10)              not null,
    payment_reference      varchar(255)             not null,
    payment_method         varchar(50)              not null,
    billing_information_id varchar(255)             not null,
    created_on             timestamp with time zone not null,
    is_submitted           boolean                  not null,
    pdf_generated          boolean                  not null default false,
    primary key (id),
    foreign key (billing_information_id) references billing_information (id)
);