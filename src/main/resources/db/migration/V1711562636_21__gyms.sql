create table gyms
(
    id         varchar(255) not null
        primary key,
    name       varchar(255) not null,
    trainee_id varchar(255)
        constraint fk_trainee
            references trainees
);

alter table if exists gyms
    add constraint gyms_unique_idx unique (name, trainee_id);

alter table workout_records
    add gym_id varchar(255);

alter table workout_records
    add constraint fk_gyms
        foreign key (gym_id) references gyms
            on delete set null;

alter table trainees
    add column default_gym_id varchar(255),
    add constraint fk_default_gym
        foreign key (default_gym_id) references gyms (id)
            on delete set null;