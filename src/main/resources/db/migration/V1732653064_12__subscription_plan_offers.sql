create table subscription_plan_offers
(
    id                   varchar(255)             not null,
    campaign_name        varchar(255)             not null,
    valid_from           timestamp with time zone not null,
    valid_to             timestamp with time zone not null,
    price                double precision         not null,
    currency             varchar(10)              not null,
    period               varchar(255)             not null,
    product_name         varchar(255)             not null,
    subscription_plan_id varchar(255)             not null,
    primary key (id),
    foreign key (subscription_plan_id) references subscription_plans (id)
);

create table purchased_subscription_plan_offers
(
    id                         varchar(255)             not null,
    user_id                    varchar(255)             not null,
    purchased_at               timestamp with time zone not null,
    subscription_plan_offer_id varchar(255)             not null,
    primary key (id),
    foreign key (subscription_plan_offer_id) references subscription_plan_offers (id)
);

alter table subscription_plans
    add period_type varchar(255) default 'MONTHLY' not null;

