CREATE TABLE nutrition_info
(
    id                  varchar(255) not null,
    calcium             float(53),
    calories            integer      not null,
    carbs               float(53)    not null,
    cholesterol         float(53),
    fat                 float(53)    not null,
    fiber               float(53),
    glycemic_index      varchar(255),
    iron                float(53),
    monounsaturated_fat float(53),
    polyunsaturated_fat float(53),
    potassium           float(53),
    protein             float(53)    not null,
    saturated_fat       float(53),
    sodium              float(53),
    sugar               float(53),
    trans_fat           float(53),
    vitamin_a           float(53),
    vitamin_b           float(53),
    vitamin_c           float(53),
    primary key (id)
);


INSERT INTO nutrition_info (id, calcium, calories, carbs, cholesterol, fat, fiber, glycemic_index, iron,
                            monounsaturated_fat, polyunsaturated_fat, potassium, protein, saturated_fat, sodium, sugar,
                            trans_fat, vitamin_a, vitamin_b, vitamin_c)
SELECT id,
       calcium,
       calories,
       carbs,
       cholesterol,
       fat,
       fiber,
       glycemic_index,
       iron,
       monounsaturated_fat,
       polyunsaturated_fat,
       potassium,
       protein,
       saturated_fat,
       sodium,
       sugar,
       trans_fat,
       vitamin_a,
       vitamin_b,
       vitamin_c
FROM food_servings;

ALTER TABLE food_servings
    DROP COLUMN calcium,
    DROP COLUMN calories,
    DROP COLUMN carbs,
    DROP COLUMN cholesterol,
    DROP COLUMN fat,
    DROP COLUMN fiber,
    DROP COLUMN glycemic_index,
    DROP COLUMN iron,
    DROP COLUMN monounsaturated_fat,
    DROP COLUMN polyunsaturated_fat,
    DROP COLUMN potassium,
    DROP COLUMN protein,
    DROP COLUMN saturated_fat,
    DROP COLUMN sodium,
    DROP COLUMN sugar,
    DROP COLUMN trans_fat,
    DROP COLUMN vitamin_a,
    DROP COLUMN vitamin_b,
    DROP COLUMN vitamin_c,
    ADD COLUMN nutrition_info_id VARCHAR(255);

UPDATE food_servings
SET nutrition_info_id = id;

ALTER TABLE food_servings
    ADD FOREIGN KEY (nutrition_info_id) REFERENCES nutrition_info (id);