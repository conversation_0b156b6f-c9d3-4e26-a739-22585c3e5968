create table food_records
(
    id                    varchar(255) not null,
    food_id               varchar(255),
    is_external           boolean      not null,
    metric_serving_amount float(53)    not null,
    metric_serving_unit   varchar(255) not null,
    name                  varchar(255) not null,
    nutrition_info_id     varchar(255),
    primary key (id)
);
create table food_records_meal_records
(
    meal_record_id varchar(255),
    food_record_id varchar(255) not null,
    primary key (food_record_id)
);
create table meal_diaries
(
    id                varchar(255) not null,
    date              date         not null,
    nutrition_info_id varchar(255),
    trainee_id        varchar(255) not null,
    primary key (id)
);
create table meal_records
(
    id                varchar(255) not null,
    order_number      integer      not null,
    time              time(6),
    diary_id          varchar(255),
    nutrition_info_id varchar(255),
    primary key (id)
);

alter table if exists food_records
    drop constraint if exists UK_ql7ycinwkturkc08v14ujwiys;
alter table if exists food_records
    add constraint UK_ql7ycinwkturkc08v14ujwiys unique (nutrition_info_id);
alter table if exists meal_diaries
    drop constraint if exists UK_i5byjg75jgcx5qscq9t538br6;
alter table if exists meal_diaries
    add constraint UK_i5byjg75jgcx5qscq9t538br6 unique (nutrition_info_id);
alter table if exists meal_records
    drop constraint if exists UK_a7ww051wgx92jv5688j8kx456;
alter table if exists meal_records
    add constraint UK_a7ww051wgx92jv5688j8kx456 unique (nutrition_info_id);
alter table if exists food_records
    add constraint FKgfmhsl6tj0ub2ngk4jsqvi3yo foreign key (nutrition_info_id) references nutrition_info;
alter table if exists food_records_meal_records
    add constraint FK7lyai67po0bgsq9ofkek271n4 foreign key (meal_record_id) references meal_records on delete cascade;
alter table if exists food_records_meal_records
    add constraint FKah4ldjwjxhpq0i9acv8ena5dv foreign key (food_record_id) references food_records;
alter table if exists meal_diaries
    add constraint FK8j1bdodsn5kcfja3wnuecovqi foreign key (nutrition_info_id) references nutrition_info;
alter table if exists meal_records
    add constraint FKeak71gb1rwrl53j55yahkyf2x foreign key (diary_id) references meal_diaries;
alter table if exists meal_records
    add constraint FKliy2b9bpnwy58n6r1we627u2q foreign key (nutrition_info_id) references nutrition_info;