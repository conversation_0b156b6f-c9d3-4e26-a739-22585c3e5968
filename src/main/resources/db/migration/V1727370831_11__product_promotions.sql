create table product_promotions
(
    id                  varchar(255)     not null,
    product_name        varchar(255)     not null,
    store_name          varchar(255)     not null,
    start_date          date             not null,
    end_date            date             not null,
    discount_price      double precision not null,
    regular_price       double precision not null,
    discount_percentage double precision not null,
    photo_url           varchar(255)     not null,
    creator_id          varchar(255)     not null,
    primary key (id)
);

create table shedlock
(
    name       varchar(64),
    lock_until timestamp(3) null,
    locked_at  timestamp(3) null,
    locked_by  varchar(255),
    primary key (name)
);

create table storage_objects
(
    id          varchar(255) not null,
    key         varchar(255) not null,
    url         varchar(255) not null,
    is_assigned boolean      not null,
    is_uploaded boolean      not null,
    primary key (id)
);