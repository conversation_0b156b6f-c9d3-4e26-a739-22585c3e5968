CREATE TABLE workout_exercise_set_records_smart_notes
(
    set_record_id VARCHAR(255) NOT NULL,
    smart_note_id VARCHAR(255) NOT NULL,
    PRIMARY KEY (set_record_id, smart_note_id),
    CONSTRAINT fk_set_record
        FOREIGN KEY (set_record_id) REFERENCES workout_exercise_set_records (id) ON DELETE CASCADE,
    CONSTRAINT fk_smart_note
        FOREIGN KEY (smart_note_id) REFERENCES smart_notes (id) ON DELETE CASCADE
);