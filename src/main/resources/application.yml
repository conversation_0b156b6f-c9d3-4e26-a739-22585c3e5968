spring:
  application:
    name: payments-service
  datasource:
    password: ${DB_PASSWORD:root}
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5433}/${DB_DATABASE:payments}
    username: ${DB_USERNAME:mpg_admin}
  cache:
    type: redis
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:Asdasd123@}
  jpa:
    hibernate:
      ddl-auto: validate
    properties:
      hibernate:
        default_schema: public
        dialect: org.hibernate.dialect.PostgreSQLDialect
        jdbc:
          lob:
            non_contextual_creation: true
    show-sql: true
  servlet:
    multipart:
      max-file-size: 400MB
      max-request-size: 400MB
  flyway:
    locations: ${FLYWAY_LOCATIONS:classpath:db/migration,classpath:db/dev}
    password: ${DB_PASSWORD:root}
    schemas: public
    user: ${DB_USERNAME:mpg_admin}
    enabled: true
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: ${KEYCLOAK_SERVER_URL:http://localhost:8080/}realms/${KEYCLOAK_REALM:myprogressguru}
      client:
        registration:
          keycloak:
            client-id: ${KEYCLOAK_RESOURCE:payments-service}
            client-secret: ${KEYCLOAK_SECRET}
        provider:
          keycloak:
            issuer-uri: ${KEYCLOAK_SERVER_URL:http://localhost:8080/}realms/${KEYCLOAK_REALM:myprogressguru}
    openfeign:
      lazy-attributes-resolution: true
  cloud:
    compatibility-verifier:
      enabled: false
server:
  port: 8085
  servlet:
    context-path: /api
keycloak:
  auth-server-url: ${KEYCLOAK_SERVER_URL:http://localhost:8080/}
  realm: ${KEYCLOAK_REALM:myprogressguru}
  resource: ${KEYCLOAK_RESOURCE:payments-service}
  credentials:
    secret: ${KEYCLOAK_SECRET:61vV2GEodF8RkLU76YcYXrS0bIAp4d98}
  bearer-only: true
mpg:
  admin-email: ${MPG_ADMIN_EMAIL:<EMAIL>}
  allowed-service-accounts: ${KEYCLOAK_ALLOWED_SERVICE_ACCOUNTS:training-service}
  frontend-url: ${FRONTEND_URL:http://localhost:8100}
  notifications-service:
    queue-url: ${NOTIFICATIONS_QUEUE_URL:http://localhost:4566/************/notifications.fifo}
  rtc-service:
    queue-url: ${WS_MESSAGES_QUEUE_URL:http://localhost:4566/************/ws_messages.fifo}
  payments-service:
    invoice-generation-request-queue-url: ${INVOICE_GENERATION_QUEUE_URL:https://sqs.eu-west-2.amazonaws.com/************/invoice_generation_request.fifo}
    invoices-bucket-name: ${INVOICES_BUCKET_NAME:mpg-dev-invoices}
  training-service:
    url: ${TRAINING_SERVICE_URL:http://localhost:8082}
management:
  endpoints:
    web:
      exposure:
        include: health,prometheus
aws:
  region: ${AWS_REGION:eu-west-2}
  ses:
    domain: ${AWS_SES_DOMAIN:dev.myprogressguru.com}
localstack:
  url: http://localhost:4566
mypos:
  checkout-url: ${MYPOS_CHECKOUT_API_URL:https://mypos.com/vmp/checkout-test}
  transactions-api-url: ${MYPOS_TRANSACTIONS_API_URL:https://transactions-api.mypos.com/v1.1}
  auth-api-url: ${MYPOS_AUTH_API_URL:https://auth-api.mypos.com}
  client-id: ${MYPOS_CLIENT_ID}
  client-secret: ${MYPOS_CLIENT_SECRET}
  sid: ${MYPOS_SID:************010}
  wallet-number: ${MYPOS_WALLET_NUMBER:61938166610}
  key-index: ${MYPOS_KEY_INDEX:1}
  public-key: ${MYPOS_PUBLIC_KEY:-----BEGIN PUBLIC KEY-----\nMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDC/lU5omOMgoaDkzGU1wCBv5pP\nsX//Zo8B8Vs8bQSWzZ3DStDiyNFV2ClthoFZJRhx0I73sNYUxZBBo5wie3+Qg524\nMXr7cgEgxrLfdmusXAHnRGnT9JxV4VM/nGGCICHIHw3F92VnjRBeZLHKsudpKQ9j\nu5xTQlfvOb0uHbiQTQIDAQAB\n-----END PUBLIC KEY-----}
******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
discord:
  bot-token: ${DISCORD_BOT_TOKEN}
  guild-id: ${DISCORD_GUILD_ID:1296752899795193886}
  client-id: ${DISCORD_CLIENT_ID}
  client-secret: ${DISCORD_CLIENT_SECRET}
  redirect-uri: ${DISCORD_REDIRECT_URI:http://localhost:8100/discord-callback}
  advanced-role-id: ${DISCORD_ADVANCED_ROLE_ID:1296755272307707964}
  premium-role-id: ${DISCORD_PREMIUM_ROLE_ID:1296755706808238109}