package com.myprogressguru.nutritionservice.web.payload.nutrition

import com.fasterxml.jackson.annotation.JsonInclude
import com.myprogressguru.nutritionservice.enumeration.NutritionPlanCalculationType

@JsonInclude(JsonInclude.Include.NON_NULL)
data class NutritionPlanCalculationResponse(

    val type: NutritionPlanCalculationType,

    val maintenanceWeeklyCalories: Int,

    val targetWeeklyCalories: Int,

    val weeklyCaloricDiff: Int,

    val targets: List<NutritionTargetResponse>

)