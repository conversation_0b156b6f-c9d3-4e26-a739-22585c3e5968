package com.myprogressguru.nutritionservice.web.payload.food

import com.myprogressguru.nutritionservice.web.payload.nutrition.NutritionInfoRequest
import jakarta.validation.Valid
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import java.io.Serializable

data class FoodRequest(

    @field:NotBlank
    val name: String?,

    val brandName: String?,

    @field:NotNull
    @field:Min(1)
    val metricServingAmount: Double?,

    @field:NotBlank
    val metricServingUnit: String?,

    @field:NotNull
    @field:Valid
    val nutritionInfo: NutritionInfoRequest?,

): Serializable