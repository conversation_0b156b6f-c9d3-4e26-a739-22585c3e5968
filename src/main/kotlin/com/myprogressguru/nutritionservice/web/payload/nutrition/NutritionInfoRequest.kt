package com.myprogressguru.nutritionservice.web.payload.nutrition

import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.PositiveOrZero

data class NutritionInfoRequest(

    @field:PositiveOrZero
    var calories: Int?,

    @field:NotNull
    @field:PositiveOrZero
    val protein: Double?,

    @field:NotNull
    @field:PositiveOrZero
    val carbs: Double?,

    @field:NotNull
    @field:PositiveOrZero
    val fat: Double?,

    val saturatedFat: Double?,

    val monounsaturatedFat: Double?,

    val polyunsaturatedFat: Double?,

    val transFat: Double?,

    val sugar: Double?,

    val fiber: Double?,

    val glycemicIndex: String?,

    val cholesterol: Double?,

    val sodium: Double?,

    val potassium: Double?,

    val iron: Double?,

    val calcium: Double?,

    val vitaminA: Double?,

    val vitaminB: Double?,

    val vitaminC: Double?,
)
