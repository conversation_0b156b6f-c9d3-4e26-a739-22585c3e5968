package com.myprogressguru.nutritionservice.web.controller


import com.myprogressguru.nutritionservice.service.FatSecretService
import com.myprogressguru.nutritionservice.web.payload.recipe.RecipeResponse
import com.myprogressguru.nutritionservice.web.payload.recipe.RecipeSearchResponse
import org.springframework.data.domain.Page
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/v1/recipes")
class RecipeController(
    private val fatSecretService: FatSecretService
) {

    @GetMapping
    fun search(
        @RequestParam(required = true) search: String,
        @RequestParam(defaultValue = "1") page: Int
    ): Page<RecipeSearchResponse> = fatSecretService.searchRecipe(search, page)

    @GetMapping("/{id}")
    fun get(
        @PathVariable id: Long
    ): RecipeResponse = fatSecretService.getRecipe(id)

}