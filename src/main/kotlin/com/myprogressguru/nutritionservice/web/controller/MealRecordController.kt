package com.myprogressguru.nutritionservice.web.controller


import com.myprogressguru.commonutils.annotation.DateRequestParam
import com.myprogressguru.nutritionservice.service.FileUploadService
import com.myprogressguru.nutritionservice.service.MealDiaryService
import com.myprogressguru.nutritionservice.service.MealRecordService
import com.myprogressguru.nutritionservice.web.payload.meal.*
import com.myprogressguru.storageutils.service.model.StorageObjectResponse
import com.myprogressguru.storageutils.web.payload.StorageObjectUploadUrlRequest
import jakarta.validation.Valid
import org.springframework.data.domain.Page
import org.springframework.http.HttpStatus
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*
import java.time.LocalDate

@RestController
@RequestMapping("/v1/meal-records")
class MealRecordController(
    private val mealRecordService: MealRecordService,
    private val mealDiaryService: MealDiaryService,
    private val fileUploadService: FileUploadService
) {

    @PreAuthorize("@keycloakService.hasAdvancedSubscriptionPlan()")
    @GetMapping("/search")
    fun search(
        @RequestParam(required = false, defaultValue = "1") page: Int,
        @ModelAttribute model: MealRecordSearchRequest
    ): Page<MealRecordResponse> = mealRecordService.search(page, model)

    @ResponseStatus(HttpStatus.CREATED)
    @PostMapping("/recognition/photos/upload-url")
    fun getRecognitionPhotoUrl(
        @Valid @RequestBody model: StorageObjectUploadUrlRequest,
    ): StorageObjectResponse = fileUploadService.getRecognitionPhotoUrl(model)

    @PreAuthorize("@keycloakService.isAdminOrTrainerOfOrTrainee(#traineeId)")
    @GetMapping("/latest")
    fun getHistory(
        @RequestParam(required = true) traineeId: String,
        @DateRequestParam(required = true, requestParamName = "maxDate") maxDate: LocalDate
    ): LatestMealRecordResponse? = mealRecordService.getLatest(traineeId, maxDate)


    @PreAuthorize("@keycloakService.hasAdvancedSubscriptionPlan()")
    @PostMapping("/{id}/copy")
    fun copy(
        @PathVariable id: String,
        @Valid @RequestBody model: MealRecordCopyRequest
    ): MealDiaryResponse = mealDiaryService.copyMealRecord(id, model)
}