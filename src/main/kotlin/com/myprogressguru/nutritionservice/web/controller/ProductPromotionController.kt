package com.myprogressguru.nutritionservice.web.controller


import com.myprogressguru.nutritionservice.service.FileUploadService
import com.myprogressguru.nutritionservice.service.ProductPromotionService
import com.myprogressguru.nutritionservice.web.payload.product.*
import com.myprogressguru.storageutils.service.model.StorageObjectResponse
import com.myprogressguru.storageutils.web.payload.StorageObjectUploadUrlRequest
import jakarta.validation.Valid
import org.springframework.http.HttpStatus
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/v1/product-promotions")
class ProductPromotionController(
    private val productPromotionService: ProductPromotionService,
    private val fileUploadService: FileUploadService
) {

    @GetMapping
    fun search(
        @RequestParam search: String?,
    ): List<ProductPromotionStoreResponse> = productPromotionService.search(
        ProductPromotionSearchRequest(
            search = search
        )
    )

    @ResponseStatus(HttpStatus.CREATED)
    @PostMapping
    fun create(
        @Valid @RequestBody model: ProductPromotionCreateRequest,
    ): ProductPromotionResponse = productPromotionService.create(model)

    @ResponseStatus(HttpStatus.CREATED)
    @PostMapping("/bulk")
    fun bulkCreate(
        @Valid @RequestBody model: ProductPromotionBulkCreateRequest,
    ): List<ProductPromotionResponse> = productPromotionService.bulkCreate(model)

    @ResponseStatus(HttpStatus.CREATED)
    @PostMapping("/ai/bulk")
    fun bulkAiCreate(
        @Valid @RequestBody model: ProductPromotionBulkAICreateRequest,
    ) = productPromotionService.bulkAICreate(model)

    @ResponseStatus(HttpStatus.NO_CONTENT)
    @PreAuthorize("@keycloakService.isModeratorOrUserOwnerOf(@productPromotionRepository, #id)")
    @PutMapping("/{id}")
    fun edit(
        @PathVariable id: String,
        @Valid @RequestBody model: ProductPromotionEditRequest
    ) = productPromotionService.edit(id, model)

    @PreAuthorize("@keycloakService.isModeratorOrUserOwnerOf(@productPromotionRepository, #id)")
    @DeleteMapping("/{id}")
    fun delete(@PathVariable id: String) = productPromotionService.delete(id)

    @ResponseStatus(HttpStatus.CREATED)
    @PostMapping("/photos/upload-url")
    fun getPhotoUploadUrl(
        @Valid @RequestBody model: StorageObjectUploadUrlRequest,
    ): StorageObjectResponse = fileUploadService.getProductPromotionsPhotoUploadUrl(model)
}