package com.myprogressguru.nutritionservice.web.controller


import com.myprogressguru.nutritionservice.service.FoodRecordService
import com.myprogressguru.nutritionservice.web.payload.food.FoodRecordResponse
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/v1/food-records")
class FoodRecordController(
    private val foodRecordService: FoodRecordService
) {

    @PreAuthorize("@keycloakService.isAdminOrTrainerOfOrTrainee(#traineeId)")
    @GetMapping
    fun getHistory(
        @RequestParam(required = true) traineeId: String,
        @RequestParam(required = false) search: String?
    ): List<FoodRecordResponse> = foodRecordService.getHistory(traineeId, search)
}