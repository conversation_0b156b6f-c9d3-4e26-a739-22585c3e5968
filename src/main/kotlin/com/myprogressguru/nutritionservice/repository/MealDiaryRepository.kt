package com.myprogressguru.nutritionservice.repository

import com.myprogressguru.nutritionservice.entity.MealDiary
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.querydsl.QuerydslPredicateExecutor
import java.time.LocalDate

interface MealDiaryRepository : JpaRepository<MealDiary, String>, QuerydslPredicateExecutor<MealDiary> {

    fun findTopByTraineeIdAndDate(traineeId: String, date: LocalDate): MealDiary?
    
    fun findAllByTraineeIdAndDateBetween(traineeId: String, startDate: LocalDate, endDate: LocalDate): List<MealDiary>
}