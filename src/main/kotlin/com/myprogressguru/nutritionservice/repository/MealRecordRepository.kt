package com.myprogressguru.nutritionservice.repository

import com.myprogressguru.nutritionservice.entity.MealRecord
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.querydsl.QuerydslPredicateExecutor
import java.time.LocalDate

interface MealRecordRepository : JpaRepository<MealRecord, String>, QuerydslPredicateExecutor<MealRecord> {

    @Query(
        """
       SELECT mr FROM MealRecord mr 
       WHERE mr.orderNumber = :orderNumber 
       AND mr.diary.date = :date 
       AND mr.diary.traineeId = :traineeId
       """
    )
    fun findTopByOrderNumberAndDateAndTraineeId(orderNumber: Int, date: LocalDate, traineeId: String): MealRecord?

    @Query(
        """
       SELECT mr FROM MealRecord mr 
       WHERE mr.diary.date < :maxDate 
       AND mr.diary.date > :startDate 
       AND mr.diary.traineeId = :traineeId
       ORDER BY mr.diary.date DESC, mr.time DESC
       """
    )
    fun findLatestByDateBeforeAndTraineeId(
        startDate: LocalDate,
        maxDate: LocalDate,
        traineeId: String,
        pageable: Pageable = PageRequest.of(0, 1)
    ): Page<MealRecord>

}