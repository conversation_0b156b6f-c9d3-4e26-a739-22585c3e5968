package com.myprogressguru.nutritionservice.repository

import com.myprogressguru.nutritionservice.entity.Brochure
import com.myprogressguru.nutritionservice.enumeration.BrochureStore
import org.springframework.data.jpa.repository.JpaRepository
import java.time.LocalDate

interface BrochureRepository : JpaRepository<Brochure, String> {

    fun existsByStoreAndStartDateAndEndDate(store: BrochureStore, startDate: LocalDate, endDate: LocalDate): Boolean
}