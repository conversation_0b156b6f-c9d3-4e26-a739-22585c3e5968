package com.myprogressguru.nutritionservice.entity


import com.myprogressguru.nutritionservice.entity.projection.NutritionInfoUpdatable
import jakarta.persistence.*
import java.time.LocalTime

@Entity
@Table(name = "meal_records")
data class MealRecord(

    @Column(nullable = false)
    var orderNumber: Int,

    @Column
    var time: LocalTime? = null,

    @OneToMany(mappedBy = "mealRecord", orphanRemoval = true)
    var foodRecords: MutableList<FoodRecord> = mutableListOf(),

    @OneToOne(cascade = [CascadeType.ALL], orphanRemoval = true)
    override var nutritionInfo: NutritionInfo = NutritionInfo(),

    @ManyToOne
    var diary: MealDiary

) : BaseEntity(), NutritionInfoUpdatable {
    override val nutritionSources: Collection<NutritionInfo>
        get() = foodRecords.map { it.nutritionInfo }
}
