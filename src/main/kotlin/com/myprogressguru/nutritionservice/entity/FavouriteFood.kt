package com.myprogressguru.nutritionservice.entity


import com.myprogressguru.nutritionservice.entity.projection.TraineeResource
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.ManyToOne
import jakarta.persistence.Table

@Entity
@Table(name = "favourite_foods")
data class FavouriteFood(

    @ManyToOne
    var food: Food,

    @Column(nullable = false)
    override var traineeId: String,

    ) : BaseEntity(), TraineeResource
