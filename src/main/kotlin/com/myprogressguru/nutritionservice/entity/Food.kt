package com.myprogressguru.nutritionservice.entity


import com.myprogressguru.nutritionservice.enumeration.FoodCategory
import jakarta.persistence.*

@Entity
@Table(
    name = "foods",
    indexes = [
        Index(
            name = "foods_name_idx",
            columnList = "name"
        )
    ]
)
data class Food(

    @Column(nullable = false)
    var name: String,

    @Column
    @Enumerated(EnumType.STRING)
    var category: FoodCategory? = null,

    @OneToMany(cascade = [CascadeType.ALL])
    @JoinTable(
        name = "foods_food_servings",
        joinColumns = [Join<PERSON><PERSON>umn(name = "food_id", referencedColumnName = "id")],
        inverseJoinColumns = [Join<PERSON>olumn(name = "food_serving_id", referencedColumnName = "id")]
    )
    var servings: List<FoodServing> = mutableListOf(),

    @Column
    val creatorId: String? = null,

    var externalFoodId: String? = null

) : BaseEntity()
