package com.myprogressguru.nutritionservice.entity


import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.ManyToOne
import jakarta.persistence.Table

@Entity
@Table(
    name = "food_barcodes",
)
data class FoodBarcode(

    @Column(nullable = false)
    var barcode: String,

    @ManyToOne
    var food: Food,

    @Column
    var creatorId: String?

) : BaseEntity()