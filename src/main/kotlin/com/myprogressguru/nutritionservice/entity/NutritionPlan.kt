package com.myprogressguru.nutritionservice.entity


import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.OneToMany
import jakarta.persistence.Table
import java.time.LocalDate

@Entity
@Table(name = "nutrition_plans")
data class NutritionPlan(

    @Column(nullable = false)
    var traineeId: String,

    @Column(nullable = false)
    var createdOn: LocalDate,

    @Column(nullable = false)
    var maintenanceWeeklyCalories: Int,

    @Column(nullable = false)
    var targetWeeklyCalories: Int,

    @Column(nullable = false)
    var weeklyCaloricDiff: Int,

    @OneToMany(mappedBy = "nutritionPlan")
    var targets: List<NutritionTarget>

) : BaseEntity()
