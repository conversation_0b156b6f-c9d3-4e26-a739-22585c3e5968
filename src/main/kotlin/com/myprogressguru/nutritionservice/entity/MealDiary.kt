package com.myprogressguru.nutritionservice.entity


import com.myprogressguru.nutritionservice.entity.projection.NutritionInfoUpdatable
import jakarta.persistence.*
import java.time.LocalDate

@Entity
@Table(name = "meal_diaries")
data class MealDiary(

    @Column(nullable = false)
    var date: LocalDate,

    @OneToMany(mappedBy = "diary", cascade = [CascadeType.ALL], orphanRemoval = true)
    @OrderBy("orderNumber")
    var mealRecords: MutableList<MealRecord> = mutableListOf(),

    @OneToOne(cascade = [CascadeType.ALL])
    override var nutritionInfo: NutritionInfo = NutritionInfo(),

    @Column(nullable = false)
    var traineeId: String

) : BaseEntity(), NutritionInfoUpdatable {
    override val nutritionSources: Collection<NutritionInfo>
        get() = mealRecords.map { it.nutritionInfo }
}
