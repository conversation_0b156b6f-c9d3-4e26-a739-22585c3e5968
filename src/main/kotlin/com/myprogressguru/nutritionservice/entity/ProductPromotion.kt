package com.myprogressguru.nutritionservice.entity


import com.myprogressguru.keycloakutils.service.model.UserIdResource
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.Table
import java.time.LocalDate

@Entity
@Table(name = "product_promotions")
data class ProductPromotion(

    @Column(nullable = false)
    var productName: String,

    @Column(nullable = false)
    var storeName: String,

    @Column(nullable = false)
    var startDate: LocalDate,

    @Column(nullable = false)
    var endDate: LocalDate,

    @Column
    var discountPrice: Double?,

    @Column
    var regularPrice: Double?,

    @Column
    var discountPercentage: Double?,

    @Column(nullable = false)
    var photoUrl: String,

    @Column(nullable = false)
    var creatorId: String

) : BaseEntity(), UserIdResource {

    override var userId: String
        get() = creatorId
        set(value) {
            creatorId = value
        }
}
