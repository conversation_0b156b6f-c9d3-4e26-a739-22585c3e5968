package com.myprogressguru.nutritionservice.entity


import com.myprogressguru.nutritionservice.enumeration.BrochureStore
import jakarta.persistence.*
import java.time.LocalDate

@Entity
@Table(name = "brochures")
data class Brochure(

    @Enumerated(EnumType.STRING)
    var store: BrochureStore,

    @Column(nullable = false)
    var startDate: LocalDate,

    @Column(nullable = false)
    var endDate: LocalDate

) : BaseEntity()
