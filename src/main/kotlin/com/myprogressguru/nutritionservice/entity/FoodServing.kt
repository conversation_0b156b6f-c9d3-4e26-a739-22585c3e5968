package com.myprogressguru.nutritionservice.entity


import jakarta.persistence.CascadeType
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.OneToOne
import jakarta.persistence.Table

@Entity
@Table(name = "food_servings")
data class FoodServing(

    @Column(nullable = false)
    var description: String,

    @Column(nullable = false)
    var metricServingAmount: Double,

    @Column(nullable = false)
    var metricServingUnit: String,

    @OneToOne(cascade = [CascadeType.ALL])
    var nutritionInfo: NutritionInfo

) : BaseEntity()
