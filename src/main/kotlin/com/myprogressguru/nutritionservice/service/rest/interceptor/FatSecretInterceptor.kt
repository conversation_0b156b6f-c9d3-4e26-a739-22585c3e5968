package com.myprogressguru.nutritionservice.service.rest.interceptor

import com.myprogressguru.commonutils.service.DateService
import com.myprogressguru.nutritionservice.service.rest.client.FatSecretOAuthClient
import com.myprogressguru.nutritionservice.service.rest.client.FatSecretToken
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.event.EventListener
import org.springframework.http.HttpRequest
import org.springframework.http.client.ClientHttpRequestExecution
import org.springframework.http.client.ClientHttpRequestInterceptor
import org.springframework.http.client.ClientHttpResponse
import org.springframework.stereotype.Component
import java.time.ZonedDateTime
import java.util.*

@Component
class FatSecretInterceptor(
    private val fatSecretOAuthClient: FatSecretOAuthClient,
    @Value("\${fatsecret.client-id}")
    private val clientId: String,
    @Value("\${fatsecret.client-secret}")
    private val clientSecret: String,
    private val dateService: DateService
) : ClientHttpRequestInterceptor {

    private lateinit var token: FatSecretToken
    private lateinit var tokenExpirationTime: ZonedDateTime

    override fun intercept(
        request: HttpRequest,
        body: ByteArray,
        execution: ClientHttpRequestExecution
    ): ClientHttpResponse {
        ensureValidToken()
        request.headers.add("Authorization", "${token.tokenType} ${token.accessToken}")

        return execution.execute(request, body)
    }

    @Synchronized
    private fun ensureValidToken() {
        if (::token.isInitialized && dateService.getCurrentDateTime().isBefore(tokenExpirationTime)) {
            return
        }

        generateToken()
    }

    @EventListener(ApplicationReadyEvent::class)
    private fun generateToken() {
        val auth = Base64.getEncoder().encodeToString("$clientId:$clientSecret".toByteArray())
        token = fatSecretOAuthClient.getToken("Basic $auth")
        tokenExpirationTime = dateService.getCurrentDateTime().plusSeconds(token.expiresIn - 1000)
    }
}