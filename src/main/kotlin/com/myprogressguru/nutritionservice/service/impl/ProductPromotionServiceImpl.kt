package com.myprogressguru.nutritionservice.service.impl

import com.myprogressguru.cloudutils.service.QueueService
import com.myprogressguru.commonutils.service.DateService
import com.myprogressguru.commonutils.util.numeric.roundTo
import com.myprogressguru.commonutils.util.serializer.fromJson
import com.myprogressguru.keycloakutils.service.KeycloakUtilsService
import com.myprogressguru.notificationsutils.service.NotificationsService
import com.myprogressguru.notificationsutils.service.WebSocketService
import com.myprogressguru.notificationsutils.service.model.SendWebSocketMessageRequest
import com.myprogressguru.nutritionservice.entity.ProductPromotion
import com.myprogressguru.nutritionservice.entity.QProductPromotion
import com.myprogressguru.nutritionservice.exception.InvalidProductPromotionException
import com.myprogressguru.nutritionservice.repository.ProductPromotionRepository
import com.myprogressguru.nutritionservice.service.NutritionMapper
import com.myprogressguru.nutritionservice.service.ProductPromotionService
import com.myprogressguru.nutritionservice.service.impl.notifications.CreatedProductPromotionNotification
import com.myprogressguru.nutritionservice.service.model.Discount
import com.myprogressguru.nutritionservice.service.model.ProductPromotionRecognitionRequest
import com.myprogressguru.nutritionservice.service.model.ProductPromotionRecognitionResponse
import com.myprogressguru.nutritionservice.web.payload.product.*
import com.myprogressguru.storageutils.service.StorageService
import io.awspring.cloud.sqs.annotation.SqsListener
import org.springframework.ai.chat.messages.SystemMessage
import org.springframework.ai.chat.messages.UserMessage
import org.springframework.ai.chat.model.ChatModel
import org.springframework.ai.content.Media
import org.springframework.ai.converter.BeanOutputConverter
import org.springframework.beans.factory.annotation.Value
import org.springframework.cache.annotation.CacheEvict
import org.springframework.cache.annotation.Cacheable
import org.springframework.data.domain.Sort
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.util.MimeType
import java.net.URI


@Service
class ProductPromotionServiceImpl(
    private val productPromotionRepository: ProductPromotionRepository,
    private val keycloakService: KeycloakUtilsService,
    private val storageService: StorageService,
    private val notificationsService: NotificationsService,
    private val dateService: DateService,
    private val nutritionMapper: NutritionMapper,
    private val chatModel: ChatModel,
    private val queueService: QueueService,
    @Value("\${mpg.nutrition-service.product-promotion-recognitions-queue-url}")
    private val productPromotionRecognitionsQueueUrl: String,
    private val webSocketService: WebSocketService
) : ProductPromotionService {

    companion object {
        private val PATH = QProductPromotion.productPromotion
    }

    @Cacheable(
        value = ["promotions:search"],
        key = "#model.toString()"
    )
    override fun search(model: ProductPromotionSearchRequest): List<ProductPromotionStoreResponse> {
        val (search) = model

        val currentDate = dateService.getCurrentDate()

        var predicate = PATH.endDate.goe(currentDate)
        if (search != null) {
            predicate = predicate.and(
                PATH.productName.containsIgnoreCase(search)
                    .or(PATH.storeName.containsIgnoreCase(search))
            )
        }

        return productPromotionRepository.findAll(
            predicate,
            Sort.by(
                Sort.Order.desc("endDate"),
                Sort.Order.desc("startDate"),
                Sort.Order.asc("productName")
            )
        ).groupBy { it.storeName }.map { (storeName, promotions) ->
            val activePromotions = promotions.filter { it.startDate <= currentDate && it.endDate >= currentDate }
            val upcomingPromotions = promotions.filter { it.startDate > currentDate }

            ProductPromotionStoreResponse(
                storeName = storeName,
                activePromotions = activePromotions.map(nutritionMapper::toProductPromotionResponse),
                upcomingPromotions = upcomingPromotions.map(nutritionMapper::toProductPromotionResponse)
            )
        }.sortedBy { it.storeName }
    }

    @CacheEvict(value = ["promotions:search"], allEntries = true)
    override fun create(model: ProductPromotionCreateRequest): ProductPromotionResponse {
        val creatorId = keycloakService.getRequesterId()!!

        val productPromotion = ProductPromotion(
            productName = model.productName!!,
            storeName = model.storeName!!,
            startDate = model.startDate!!,
            endDate = model.endDate!!,
            discountPrice = model.discountPrice,
            regularPrice = model.regularPrice,
            discountPercentage = getDiscountPercentage(model),
            photoUrl = storageService.getUrlAndAssign(model.photoId!!),
            creatorId = creatorId
        )

        productPromotionRepository.save(productPromotion)

        notificationsService.send(
            CreatedProductPromotionNotification(creatorId, productPromotion)
        )

        return nutritionMapper.toProductPromotionResponse(productPromotion)
    }

    @CacheEvict(value = ["promotions:search"], allEntries = true)
    override fun bulkCreate(model: ProductPromotionBulkCreateRequest): List<ProductPromotionResponse> =
        model.promotions.map { create(it) }

    @CacheEvict(value = ["promotions:search"], allEntries = true)
    override fun edit(id: String, model: ProductPromotionEditRequest) {
        val productPromotion = getProductPromotion(id)

        productPromotion.apply {
            productName = model.productName!!
            storeName = model.storeName!!
            startDate = model.startDate!!
            endDate = model.endDate!!
            discountPrice = model.discountPrice
            regularPrice = model.regularPrice
            discountPercentage = getDiscountPercentage(model)

            if (model.photoId != null) {
                storageService.deleteAllByUrl(photoUrl)
                photoUrl = storageService.getUrlAndAssign(model.photoId)
            }
        }

        productPromotionRepository.save(productPromotion)
    }

    @CacheEvict(value = ["promotions:search"], allEntries = true)
    override fun delete(id: String) {
        val productPromotion = getProductPromotion(id)

        storageService.deleteAllByUrl(productPromotion.photoUrl)

        productPromotionRepository.delete(productPromotion)
    }

    override fun bulkAICreate(model: ProductPromotionBulkAICreateRequest) {
        if (model.promotions.isEmpty()) {
            return
        }

        val requesterId = keycloakService.getRequesterId()!!

        val messages = model.promotions.map {
            ProductPromotionRecognitionRequest(
                index = it.index!!,
                photoUrl = it.photoUrl!!,
                requesterId = requesterId
            )
        }

        queueService.sendBatch(productPromotionRecognitionsQueueUrl, messages)
    }

    @SqsListener("product_promotion_recognitions.fifo")
    override fun handleProductPromotionRecognition(model: ProductPromotionRecognitionRequest) {
        val response = readPhoto(model.photoUrl)

        val payload = ProductPromotionAIResultResponse(
            index = model.index,
            productName = response.productName,
            discountPrice = response.discountPrice,
            regularPrice = response.regularPrice,
            discountPercentage = response.discountPercentage
        )

        webSocketService.send(
            SendWebSocketMessageRequest(
                userId = model.requesterId,
                payload = payload,
                action = "PRODUCT_PROMOTION_RECOGNITION"
            )
        )
    }

    private fun calculateDiscountPercentage(regularPrice: Double, discountPrice: Double) =
        (((regularPrice - discountPrice) / regularPrice) * 100).roundTo(2)

    private fun getProductPromotion(id: String) =
        productPromotionRepository.findByIdOrNull(id) ?: throw InvalidProductPromotionException(id)

    private fun getDiscountPercentage(discount: Discount): Double? =
        if (discount.discountPrice != null && discount.regularPrice != null) {
            calculateDiscountPercentage(discount.regularPrice!!, discount.discountPrice!!)
        } else if (discount.discountPrice != null && discount.discountPercentage == null) {
            null
        } else {
            discount.discountPercentage!!
        }

    private fun readPhoto(photoUrl: String): ProductPromotionRecognitionResponse {
        val converter = BeanOutputConverter(ProductPromotionRecognitionResponse::class.java)
        val format = converter.format

        val call = chatModel.call(
            SystemMessage("Ти си асистент, който извлича информация за промоции от изображения на брошури. Ако забележиш правописни грешки в имената на продуктите или описанията, ги коригирай преди да върнеш отговора."),
            UserMessage(
                "Това е снимка на промоция от брошура. Извади ми информацията в следния формат: $format", Media(
                    MimeType("image", "png"),
                    URI.create(photoUrl).toURL()
                )
            ),
        )

        return call.fromJson<ProductPromotionRecognitionResponse>()
    }
}