package com.myprogressguru.nutritionservice.service.impl.notifications

import com.myprogressguru.notificationsutils.service.impl.BaseTrainerNotification
import com.myprogressguru.notificationsutils.service.model.SendNotificationRequest
import com.myprogressguru.nutritionservice.entity.MealRecord
import com.myprogressguru.nutritionservice.enumeration.NotificationType
import java.time.LocalDate

class CreatedMealRecordNotification(
    traineeId: String,
    private val mealRecord: MealRecord,
    private val date: LocalDate
) : BaseTrainerNotification(traineeId) {

    override fun buildRequest(): SendNotificationRequest = SendNotificationRequest(
        userId = traineeTrainerId,
        type = NotificationType.CREATED_MEAL_RECORD.name,
        title = "$traineeFirstName $traineeLastName - New meal record!",
        body = "Meal ${mealRecord.orderNumber} (${mealRecord.time}) - $date",
        data = mapOf(
            "traineeId" to traineeId,
            "date" to date.toString(),
        )
    )
}