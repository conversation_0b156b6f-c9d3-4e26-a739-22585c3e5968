package com.myprogressguru.nutritionservice.service.impl

import com.myprogressguru.nutritionservice.enumeration.NutritionTargetType
import com.myprogressguru.nutritionservice.service.MacrosCalculator
import com.myprogressguru.nutritionservice.service.NutritionTargetCalculationStrategy
import com.myprogressguru.nutritionservice.service.model.NutritionPlanCalculationModel
import com.myprogressguru.nutritionservice.web.payload.nutrition.NutritionInfoResponse
import com.myprogressguru.nutritionservice.web.payload.nutrition.NutritionTargetResponse


abstract class BaseNutritionTargetCalculationStrategy(
    protected val macrosCalculator: MacrosCalculator
) : NutritionTargetCalculationStrategy {

    override fun calculate(
        model: NutritionPlanCalculationModel,
        nutritionTargetType: NutritionTargetType,
        frequencyPerWeek: Int
    ): NutritionTargetResponse {
        val protein = macrosCalculator.calculateProtein(model)
        val fat = macrosCalculator.calculateFat(model)

        val energyIntake = calculateEnergyIntake(model)

        val carbs = macrosCalculator.calculateCarbs(energyIntake, protein, fat)

        return NutritionTargetResponse(
            type = nutritionTargetType,
            nutritionInfo = NutritionInfoResponse(
                calories = energyIntake,
                protein = protein,
                fat = fat,
                carbs = carbs
            ),
            frequencyPerWeek = frequencyPerWeek
        )
    }

    protected abstract fun calculateEnergyIntake(model: NutritionPlanCalculationModel): Int
}