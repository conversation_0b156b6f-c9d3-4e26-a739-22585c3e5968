package com.myprogressguru.nutritionservice.service.impl

import com.myprogressguru.nutritionservice.service.MacrosCalculator
import com.myprogressguru.nutritionservice.service.model.NutritionPlanCalculationModel
import org.springframework.stereotype.Service


@Service
class GeneralNutritionTargetCalculationStrategy(
    macrosCalculator: MacrosCalculator
) : BaseNutritionTargetCalculationStrategy(macrosCalculator) {

    override fun calculateEnergyIntake(model: NutritionPlanCalculationModel): Int = model.averageTargetEnergyIntake
}