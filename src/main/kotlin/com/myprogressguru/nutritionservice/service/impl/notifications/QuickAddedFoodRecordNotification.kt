package com.myprogressguru.nutritionservice.service.impl.notifications

import com.myprogressguru.notificationsutils.service.impl.BaseTrainerNotification
import com.myprogressguru.notificationsutils.service.model.SendNotificationRequest
import com.myprogressguru.nutritionservice.entity.FoodRecord
import com.myprogressguru.nutritionservice.enumeration.NotificationType
import java.time.LocalDate

class QuickAddedFoodRecordNotification(
    traineeId: String,
    private val foodRecord: FoodRecord,
    private val date: LocalDate
) : BaseTrainerNotification(traineeId) {

    override fun buildRequest(): SendNotificationRequest = SendNotificationRequest(
        userId = traineeTrainerId,
        type = NotificationType.QUICK_ADDED_FOOD_RECORD.name,
        title = "$traineeFirstName $traineeLastName - Quick Added Food!",
        body = "${foodRecord.name} - ${foodRecord.nutritionInfo.protein}P | ${foodRecord.nutritionInfo.carbs}C | ${foodRecord.nutritionInfo.fat}F | ${foodRecord.nutritionInfo.calories} kcal",
        data = mapOf(
            "traineeId" to traineeId,
            "date" to date.toString(),
        )
    )
}