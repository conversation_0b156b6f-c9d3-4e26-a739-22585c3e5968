package com.myprogressguru.nutritionservice.service.impl.notifications

import com.myprogressguru.notificationsutils.service.impl.BaseTrainerNotification
import com.myprogressguru.notificationsutils.service.model.SendNotificationRequest
import com.myprogressguru.nutritionservice.entity.MealDiary
import com.myprogressguru.nutritionservice.enumeration.NotificationType
import java.time.LocalDate

class CreatedMealRecordsCopyNotification(
    traineeId: String,
    private val date: LocalDate,
    private val copyToDate: LocalDate,
    private val mealDiary: MealDiary
) : BaseTrainerNotification(traineeId) {

    override fun buildRequest(): SendNotificationRequest = SendNotificationRequest(
        userId = traineeTrainerId,
        type = NotificationType.CREATED_MEAL_RECORDS_COPY.name,
        title = "$traineeFirstName $traineeLastName - Copied all meal records from $date to $copyToDate!",
        body = "${mealDiary.mealRecords.size} meals - ${mealDiary.nutritionInfo.calories} kcal",
        data = mapOf(
            "traineeId" to traineeId,
            "date" to copyToDate.toString(),
        )
    )
}