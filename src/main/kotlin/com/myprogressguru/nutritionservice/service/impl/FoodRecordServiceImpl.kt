package com.myprogressguru.nutritionservice.service.impl

import com.myprogressguru.nutritionservice.repository.FoodRecordRepository
import com.myprogressguru.nutritionservice.service.FoodRecordService
import com.myprogressguru.nutritionservice.service.NutritionMapper
import com.myprogressguru.nutritionservice.web.payload.food.FoodRecordResponse
import org.springframework.stereotype.Service

@Service
class FoodRecordServiceImpl(
    private val foodRecordRepository: FoodRecordRepository,
    private val nutritionMapper: NutritionMapper
) : FoodRecordService {

    override fun getHistory(traineeId: String, search: String?): List<FoodRecordResponse> =
        nutritionMapper.toFoodRecordResponseList(foodRecordRepository.getHistory(traineeId, search))
}