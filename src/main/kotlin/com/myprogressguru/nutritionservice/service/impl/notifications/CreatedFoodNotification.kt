package com.myprogressguru.nutritionservice.service.impl.notifications

import com.myprogressguru.notificationsutils.service.impl.BaseTrainerNotification
import com.myprogressguru.notificationsutils.service.model.SendNotificationRequest
import com.myprogressguru.nutritionservice.entity.Food
import com.myprogressguru.nutritionservice.enumeration.NotificationType

class CreatedFoodNotification(
    creatorId: String,
    private val food: Food,
) : BaseTrainerNotification(creatorId) {

    override fun buildRequest(): SendNotificationRequest {
        val serving = food.servings[0]
        val nutritionInfo = serving.nutritionInfo

        return SendNotificationRequest(
            userId = traineeTrainerId,
            type = NotificationType.CREATED_FOOD.name,
            title = "$traineeFirstName $traineeLastName - Created Food!",
            body = "${food.name} - ${serving.description} - ${nutritionInfo.protein}P | ${nutritionInfo.carbs}C | ${nutritionInfo.fat}F",
            data = mapOf(
                "traineeId" to traineeId,
                "foodId" to food.id!!,
            ))
    }
}