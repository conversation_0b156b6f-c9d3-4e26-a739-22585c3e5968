package com.myprogressguru.nutritionservice.service.impl

import com.myprogressguru.nutritionservice.enumeration.NutritionPlanCalculationType
import com.myprogressguru.nutritionservice.enumeration.NutritionTargetType
import com.myprogressguru.nutritionservice.service.NutritionTargetCalculationStrategy
import com.myprogressguru.nutritionservice.service.model.NutritionPlanCalculationModel
import com.myprogressguru.nutritionservice.web.payload.nutrition.NutritionTargetResponse
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Service

@Service
class GeneralNutritionPlanCalculationStrategy(
    @Qualifier("generalNutritionTargetCalculationStrategy")
    private val nutritionTargetCalculationStrategy: NutritionTargetCalculationStrategy
) : BaseNutritionPlanCalculationStrategy(type = NutritionPlanCalculationType.GENERAL) {

    override fun calculateNutritionTargets(model: NutritionPlanCalculationModel): List<NutritionTargetResponse> =
        listOf(
            nutritionTargetCalculationStrategy.calculate(model, NutritionTargetType.SIMPLE, 7)
        )
}