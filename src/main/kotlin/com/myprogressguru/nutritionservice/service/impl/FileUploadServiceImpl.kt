package com.myprogressguru.nutritionservice.service.impl

import com.myprogressguru.commonutils.service.DateService
import com.myprogressguru.nutritionservice.service.FileUploadService
import com.myprogressguru.storageutils.service.StorageService
import com.myprogressguru.storageutils.service.model.StorageObjectResponse
import com.myprogressguru.storageutils.web.payload.StorageObjectUploadUrlRequest
import org.springframework.stereotype.Service
import java.util.*

@Service
class FileUploadServiceImpl(
    private val storageService: StorageService,
    private val dateService: DateService
) : FileUploadService {

    override fun getProductPromotionsPhotoUploadUrl(model: StorageObjectUploadUrlRequest): StorageObjectResponse {
        val key = "product-promotions/${dateService.getCurrentDate()}/${UUID.randomUUID()}.${model.fileExtension}"

        return storageService.getPresignedUploadUrl(key)
    }

    override fun getRecognitionPhotoUrl(model: StorageObjectUploadUrlRequest): StorageObjectResponse {
        val key =
            "meal-records/recognitions/${dateService.getCurrentDate()}/${UUID.randomUUID()}.${model.fileExtension}"

        return storageService.getPresignedUploadUrl(key)
    }
}