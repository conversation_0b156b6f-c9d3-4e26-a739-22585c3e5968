package com.myprogressguru.nutritionservice.service.impl.notifications

import com.myprogressguru.notificationsutils.service.impl.BaseTrainerNotification
import com.myprogressguru.notificationsutils.service.model.SendNotificationRequest
import com.myprogressguru.nutritionservice.entity.Food
import com.myprogressguru.nutritionservice.enumeration.NotificationType

class CreatedFoodBarcodeNotification(
    creatorId: String,
    private val food: Food,
    private val barcode: String
) : BaseTrainerNotification(creatorId) {

    override fun buildRequest(): SendNotificationRequest {

        return SendNotificationRequest(
            userId = traineeTrainerId,
            type = NotificationType.CREATED_FOOD_BARCODE.name,
            title = "$traineeFirstName $traineeLastName - Linked food to barcode!",
            body = "${food.name} - $barcode",
            data = mapOf(
                "traineeId" to traineeId,
                "foodId" to food.id!!,
            ))
    }
}