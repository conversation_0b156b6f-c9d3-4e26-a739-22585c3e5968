package com.myprogressguru.nutritionservice.service.impl

import com.myprogressguru.nutritionservice.service.NutritionMapper
import com.myprogressguru.nutritionservice.service.NutritionPlanCalculationStrategy
import com.myprogressguru.nutritionservice.service.NutritionService
import com.myprogressguru.nutritionservice.web.payload.nutrition.NutritionPlanCalculationRequest
import com.myprogressguru.nutritionservice.web.payload.nutrition.NutritionPlanCalculationResponse
import org.springframework.stereotype.Service


@Service
class NutritionServiceImpl(
    private val nutritionMapper: NutritionMapper,
    private val nutritionPlanCalculationStrategies: List<NutritionPlanCalculationStrategy>
) : NutritionService {

    override fun calculateNutritionPlans(model: NutritionPlanCalculationRequest): List<NutritionPlanCalculationResponse> {
        val calculationModel = nutritionMapper.toNutritionPlanCalculationModel(model)

        return nutritionPlanCalculationStrategies.map { it.calculate(calculationModel) }
    }
}