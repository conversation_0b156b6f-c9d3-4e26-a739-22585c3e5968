package com.myprogressguru.nutritionservice.service.impl

import com.myprogressguru.nutritionservice.enumeration.NutritionPlanCalculationType
import com.myprogressguru.nutritionservice.service.NutritionPlanCalculationStrategy
import com.myprogressguru.nutritionservice.service.model.NutritionPlanCalculationModel
import com.myprogressguru.nutritionservice.web.payload.nutrition.NutritionPlanCalculationResponse
import com.myprogressguru.nutritionservice.web.payload.nutrition.NutritionTargetResponse

abstract class BaseNutritionPlanCalculationStrategy(
    private val type: NutritionPlanCalculationType
) : NutritionPlanCalculationStrategy {

    abstract fun calculateNutritionTargets(model: NutritionPlanCalculationModel): List<NutritionTargetResponse>

    override fun calculate(
        model: NutritionPlanCalculationModel,
    ): NutritionPlanCalculationResponse {
        val nutritionTargets = calculateNutritionTargets(model)
        val maintenanceWeeklyCalories = model.maintenanceEnergyIntake * 7
        val targetWeeklyCalories =
            nutritionTargets.sumOf { it.nutritionInfo.calories * it.frequencyPerWeek }
        val weeklyCaloricDiff = targetWeeklyCalories - maintenanceWeeklyCalories

        return NutritionPlanCalculationResponse(
            type = type,
            maintenanceWeeklyCalories = maintenanceWeeklyCalories,
            targetWeeklyCalories = targetWeeklyCalories,
            weeklyCaloricDiff = weeklyCaloricDiff,
            targets = nutritionTargets
        )
    }
}