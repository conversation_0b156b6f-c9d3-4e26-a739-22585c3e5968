package com.myprogressguru.nutritionservice.service

import com.myprogressguru.nutritionservice.service.model.ProductPromotionRecognitionRequest
import com.myprogressguru.nutritionservice.web.payload.product.*

interface ProductPromotionService {

    fun search(model: ProductPromotionSearchRequest): List<ProductPromotionStoreResponse>

    fun create(model: ProductPromotionCreateRequest): ProductPromotionResponse

    fun bulkCreate(model: ProductPromotionBulkCreateRequest): List<ProductPromotionResponse>

    fun bulkAICreate(model: ProductPromotionBulkAICreateRequest)

    fun handleProductPromotionRecognition(model: ProductPromotionRecognitionRequest)

    fun edit(id: String, model: ProductPromotionEditRequest)

    fun delete(id: String)
}