package com.myprogressguru.nutritionservice.service.model

import com.myprogressguru.nutritionservice.enumeration.Gender
import com.myprogressguru.nutritionservice.enumeration.PhysicalActivityLevel

data class NutritionPlanCalculationModel(

    val bodyWeightInKg: Double,

    val bodyFatPercentage: Double,

    val physicalActivityLevel: PhysicalActivityLevel,

    val physicalActivityFactor: Double,

    val thermicEffectOfFoodFactor: Double,

    val trainingSessionDurationInMin: Int,

    val energyBalanceFactor: Double,

    val trainingDaysPerWeek: Int,

    val leanMassInKg: Double,

    val basalMetabolicRate: Int,

    val trainingEnergyExpenditure: Int,

    val restingEnergyExpenditure: Int,

    val energyExpenditureOnTrainingDays: Int,

    val maintenanceEnergyIntake: Int,

    val averageTargetEnergyIntake: Int,

    val gender: Gender

)