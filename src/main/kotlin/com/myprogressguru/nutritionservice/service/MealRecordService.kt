package com.myprogressguru.nutritionservice.service

import com.myprogressguru.nutritionservice.web.payload.meal.LatestMealRecordResponse
import com.myprogressguru.nutritionservice.web.payload.meal.MealRecordRecognitionResponse
import com.myprogressguru.nutritionservice.web.payload.meal.MealRecordResponse
import com.myprogressguru.nutritionservice.web.payload.meal.MealRecordSearchRequest
import org.springframework.data.domain.Page
import java.time.LocalDate

interface MealRecordService {

    fun search(page: Int, model: MealRecordSearchRequest): Page<MealRecordResponse>

    fun getLatest(traineeId: String, maxDate: LocalDate): LatestMealRecordResponse?

    fun recognise(photoUrl: String, language: String): MealRecordRecognitionResponse

}