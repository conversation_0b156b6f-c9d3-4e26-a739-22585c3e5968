package com.myprogressguru.nutritionservice.service

import com.myprogressguru.nutritionservice.entity.*
import com.myprogressguru.nutritionservice.service.model.NutritionPlanCalculationModel
import com.myprogressguru.nutritionservice.service.rest.client.*
import com.myprogressguru.nutritionservice.web.payload.food.*
import com.myprogressguru.nutritionservice.web.payload.meal.MealDiaryInfoResponse
import com.myprogressguru.nutritionservice.web.payload.meal.MealDiaryResponse
import com.myprogressguru.nutritionservice.web.payload.meal.MealRecordResponse
import com.myprogressguru.nutritionservice.web.payload.nutrition.NutritionInfoRequest
import com.myprogressguru.nutritionservice.web.payload.nutrition.NutritionInfoResponse
import com.myprogressguru.nutritionservice.web.payload.nutrition.NutritionPlanCalculationRequest
import com.myprogressguru.nutritionservice.web.payload.product.ProductPromotionResponse
import com.myprogressguru.nutritionservice.web.payload.recipe.RecipeResponse
import com.myprogressguru.nutritionservice.web.payload.recipe.RecipeSearchResponse
import com.myprogressguru.nutritionservice.web.payload.recipe.RecipesSearchResponse
import org.mapstruct.*

@JvmDefaultWithCompatibility
@Mapper(
    componentModel = "spring",
    injectionStrategy = InjectionStrategy.CONSTRUCTOR,
    unmappedTargetPolicy = ReportingPolicy.IGNORE
)
interface NutritionMapper {

    fun toNutritionPlanCalculationModel(model: NutritionPlanCalculationRequest): NutritionPlanCalculationModel

    fun toRecipesSearchResponse(model: FSRecipesSearch): RecipesSearchResponse

    fun toRecipesSearchResponseList(recipes: List<FSRecipeSearch>?): List<RecipeSearchResponse>?

    fun toRecipeResponse(model: FSRecipe): RecipeResponse

    @Mapping(target = "isExternal", expression = "java(food.getExternalFoodId() != null)")
    fun toFoodResponse(food: Food): FoodResponse

    @Mapping(target = "id", source = "foodId")
    @Mapping(target = "isExternal", expression = "java(true)")
    @Mapping(target = "name", expression = "java(getFoodName(food))")
    @Mapping(target = "servings", source = "servings.serving")
    fun toFoodResponse(food: FSFood): FoodResponse

    @Mapping(target = "description", source = "servingDescription")
    @Mapping(target = "nutritionInfo.calories", source = "calories")
    @Mapping(target = "nutritionInfo.protein", source = "protein")
    @Mapping(target = "nutritionInfo.carbs", source = "carbohydrate")
    @Mapping(target = "nutritionInfo.fat", source = "fat")
    @Mapping(target = "nutritionInfo.saturatedFat", source = "saturatedFat")
    @Mapping(target = "nutritionInfo.monounsaturatedFat", source = "monounsaturatedFat")
    @Mapping(target = "nutritionInfo.polyunsaturatedFat", source = "polyunsaturatedFat")
    @Mapping(target = "nutritionInfo.transFat", source = "transFat")
    @Mapping(target = "nutritionInfo.sugar", source = "sugar")
    @Mapping(target = "nutritionInfo.fiber", source = "fiber")
    @Mapping(target = "nutritionInfo.cholesterol", source = "cholesterol")
    @Mapping(target = "nutritionInfo.sodium", source = "sodium")
    @Mapping(target = "nutritionInfo.potassium", source = "potassium")
    @Mapping(target = "nutritionInfo.iron", source = "iron")
    @Mapping(target = "nutritionInfo.calcium", source = "calcium")
    @Mapping(target = "nutritionInfo.vitaminA", source = "vitaminA")
    @Mapping(target = "nutritionInfo.vitaminC", source = "vitaminC")
    fun toServingResponse(serving: FSServing): ServingResponse

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "name", expression = "java(getFoodName(food))")
    @Mapping(target = "servings", source = "servings.serving")
    fun toFood(food: FSFood): Food

    @Mapping(target = "description", source = "servingDescription")
    @Mapping(target = "nutritionInfo.calories", source = "calories")
    @Mapping(target = "nutritionInfo.protein", source = "protein")
    @Mapping(target = "nutritionInfo.carbs", source = "carbohydrate")
    @Mapping(target = "nutritionInfo.fat", source = "fat")
    @Mapping(target = "nutritionInfo.saturatedFat", source = "saturatedFat")
    @Mapping(target = "nutritionInfo.monounsaturatedFat", source = "monounsaturatedFat")
    @Mapping(target = "nutritionInfo.polyunsaturatedFat", source = "polyunsaturatedFat")
    @Mapping(target = "nutritionInfo.transFat", source = "transFat")
    @Mapping(target = "nutritionInfo.sugar", source = "sugar")
    @Mapping(target = "nutritionInfo.fiber", source = "fiber")
    @Mapping(target = "nutritionInfo.cholesterol", source = "cholesterol")
    @Mapping(target = "nutritionInfo.sodium", source = "sodium")
    @Mapping(target = "nutritionInfo.potassium", source = "potassium")
    @Mapping(target = "nutritionInfo.iron", source = "iron")
    @Mapping(target = "nutritionInfo.calcium", source = "calcium")
    @Mapping(target = "nutritionInfo.vitaminA", source = "vitaminA")
    @Mapping(target = "nutritionInfo.vitaminC", source = "vitaminC")
    fun toServing(serving: FSServing): FoodServing

    fun toFavouriteFoodResponse(favouriteFood: FavouriteFood): FavouriteFoodResponse

    fun toFavouriteFoodResponseList(favouriteFoods: List<FavouriteFood>): List<FavouriteFoodResponse>

    fun getFoodName(food: FSFood): String {
        if (food.brandName != null) {
            return "${food.brandName} - ${food.foodName}"
        }

        return food.foodName
    }

    fun toFoodResponseList(foods: List<FSFood>?): List<FoodResponse>?

    fun toFoodRecordResponse(foodRecord: FoodRecord): FoodRecordResponse

    fun toFoodRecordResponseList(history: List<FoodRecord>): List<FoodRecordResponse>

    fun toMealDiaryResponse(mealDiary: MealDiary): MealDiaryResponse

    fun toMealDiaryResponseList(mealDiaries: List<MealDiary>): List<MealDiaryResponse>

    fun toMealDiaryInfoResponse(mealDiary: MealDiary): MealDiaryInfoResponse

    fun toNutritionInfoResponse(nutritionInfo: NutritionInfo): NutritionInfoResponse

    fun toMealRecordResponse(mealRecord: MealRecord): MealRecordResponse

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "nutritionInfo", source = "model.nutritionInfo")
    @Mapping(target = "mealRecord", source = "mealRecord")
    fun toFoodRecord(model: FoodRecordRequest, mealRecord: MealRecord): FoodRecord

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "mealRecord", source = "mealRecord")
    @Mapping(target = "isRecognised", expression = "java(true)")
    @Mapping(target = "nutritionInfo", source = "model.nutritionInfo")
    fun toFoodRecord(model: FoodRecognitionResponse, mealRecord: MealRecord): FoodRecord

    fun map(model: FoodRecordRequest, @MappingTarget foodRecord: FoodRecord)

    fun toNutritionInfo(nutritionInfo: NutritionInfoRequest): NutritionInfo

    fun map(model: NutritionInfoRequest, @MappingTarget nutritionInfo: NutritionInfo)

    fun toProductPromotionResponse(productPromotion: ProductPromotion): ProductPromotionResponse
}