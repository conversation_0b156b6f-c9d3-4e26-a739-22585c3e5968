package com.myprogressguru.nutritionservice.service

import com.myprogressguru.nutritionservice.entity.FoodRecord
import com.myprogressguru.nutritionservice.entity.MealRecord
import com.myprogressguru.nutritionservice.entity.NutritionInfo
import org.mapstruct.*
import org.mapstruct.control.DeepClone

@JvmDefaultWithCompatibility
@Mapper(
    componentModel = "spring",
    injectionStrategy = InjectionStrategy.CONSTRUCTOR,
)
interface CloningService {

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "nutritionSources", ignore = true)
    @Mapping(target = "foodRecords", mappingControl = DeepClone::class)
    @Mapping(target = "nutritionInfo", mappingControl = DeepClone::class)
    fun clone(mealRecord: MealRecord): MealRecord

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "mealRecord", expression = "java(foodRecord.getMealRecord())")
    @Mapping(target = "nutritionInfo", mappingControl = DeepClone::class)
    fun clone(foodRecord: FoodRecord): FoodRecord

    @Mapping(target = "id", ignore = true)
    fun clone(nutritionInfo: NutritionInfo): NutritionInfo
}
