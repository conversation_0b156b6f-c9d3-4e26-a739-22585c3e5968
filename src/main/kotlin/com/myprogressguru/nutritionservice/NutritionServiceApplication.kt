package com.myprogressguru.nutritionservice


import com.myprogressguru.commonutils.annotation.MyProgressGuruApplication
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration
import org.springframework.boot.runApplication
import org.springframework.cache.annotation.EnableCaching
import org.springframework.cloud.openfeign.EnableFeignClients
import org.springframework.scheduling.annotation.EnableAsync

@EnableAsync
@EnableCaching
@EnableFeignClients(basePackages = ["com.myprogressguru"])
@MyProgressGuruApplication
@SpringBootApplication(exclude = [RedisRepositoriesAutoConfiguration::class])
class NutritionServiceApplication

fun main(args: Array<String>) {
    runApplication<NutritionServiceApplication>(*args)
}
