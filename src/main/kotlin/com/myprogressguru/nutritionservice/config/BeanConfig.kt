package com.myprogressguru.nutritionservice.config

import com.myprogressguru.cloudutils.config.CloudUtilsBeanConfig
import com.myprogressguru.commonutils.config.CommonUtilsBeanConfig
import com.myprogressguru.notificationsutils.config.NotificationsUtilsBeanConfig
import com.myprogressguru.nutritionservice.service.rest.client.FatSecretClient
import com.myprogressguru.nutritionservice.service.rest.client.FatSecretOAuthClient
import com.myprogressguru.nutritionservice.service.rest.interceptor.FatSecretInterceptor
import com.myprogressguru.storageutils.config.StorageUtilsBeanConfig
import org.springframework.ai.chat.client.ChatClient
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Import
import org.springframework.data.redis.cache.RedisCacheConfiguration
import org.springframework.web.client.RestClient
import org.springframework.web.client.support.RestClientAdapter
import org.springframework.web.service.invoker.HttpServiceProxyFactory
import java.time.Duration


@Import(value = [CommonUtilsBeanConfig::class, NotificationsUtilsBeanConfig::class, StorageUtilsBeanConfig::class, CloudUtilsBeanConfig::class])
@Configuration
class BeanConfig {

    @Bean
    fun cacheConfig(): RedisCacheConfiguration = RedisCacheConfiguration.defaultCacheConfig()
        .entryTtl(Duration.ofMinutes(15))

    @Bean
    fun fatSecretOAuthClient(): FatSecretOAuthClient {
        val client = RestClient.create("https://oauth.fatsecret.com")
        val factory = HttpServiceProxyFactory
            .builderFor(RestClientAdapter.create(client))
            .build()

        return factory.createClient(FatSecretOAuthClient::class.java)
    }

    @Bean
    fun fatSecretClient(fatSecretInterceptor: FatSecretInterceptor): FatSecretClient {
        val client = RestClient.builder()
            .baseUrl("https://platform.fatsecret.com/rest/server.api?format=json")
            .requestInterceptor(fatSecretInterceptor)
            .build()

        val factory = HttpServiceProxyFactory
            .builderFor(RestClientAdapter.create(client))
            .build()

        return factory.createClient(FatSecretClient::class.java)
    }

    @Bean
    fun chatClient(builder: ChatClient.Builder): ChatClient = builder.build()
}