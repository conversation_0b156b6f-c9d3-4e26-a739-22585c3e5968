package com.myprogressguru.paymentsservice.repository

import com.myprogressguru.paymentsservice.entity.Subscription
import com.myprogressguru.paymentsservice.enumeration.SubscriptionStatus
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import java.time.LocalDate

interface SubscriptionRepository : JpaRepository<Subscription, String> {

    fun findTopByUserId(userId: String): Subscription?

    fun existsByUserId(userId: String): Boolean

    @Query(
        """
        SELECT s
        FROM Subscription s
        WHERE (s.status IN (:statuses) AND s.currentPeriodEnd = :expirationDate) 
        OR (s.status = :freeTrialStatus AND s.trialEnd = :expirationDate)
    """
    )
    fun findAllExpired(
        @Param("expirationDate") expirationDate: LocalDate,
        @Param("statuses") statuses: List<SubscriptionStatus>,
        @Param("freeTrialStatus") freeTrialStatus: SubscriptionStatus
    ): List<Subscription>
}