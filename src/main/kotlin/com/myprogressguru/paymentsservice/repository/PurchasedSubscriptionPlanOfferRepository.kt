package com.myprogressguru.paymentsservice.repository

import com.myprogressguru.paymentsservice.entity.PurchasedSubscriptionPlanOffer
import org.springframework.data.jpa.repository.JpaRepository

interface PurchasedSubscriptionPlanOfferRepository : JpaRepository<PurchasedSubscriptionPlanOffer, String> {

    fun findAllByUserId(userId: String): List<PurchasedSubscriptionPlanOffer>

    fun existsByUserIdAndSubscriptionPlanOfferCampaignName(
        userId: String,
        campaignName: String
    ): <PERSON><PERSON><PERSON>
}