package com.myprogressguru.paymentsservice.repository

import com.myprogressguru.paymentsservice.entity.BillingInformation
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.querydsl.QuerydslPredicateExecutor

interface BillingInformationRepository : JpaRepository<BillingInformation, String>,
    QuerydslPredicateExecutor<BillingInformation> {

    fun existsByUserId(userId: String): Boolean

    fun findByUserId(userId: String): BillingInformation?
}