package com.myprogressguru.paymentsservice.repository

import com.myprogressguru.paymentsservice.entity.Invoice
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.querydsl.QuerydslPredicateExecutor

interface InvoiceRepository : JpaRepository<Invoice, String>, QuerydslPredicateExecutor<Invoice> {

    fun findFirstByOrderByNumberDesc(): Invoice?

    fun findAllByPaymentReferenceIn(paymentReferences: List<String>): List<Invoice>

    fun findAllByIdIn(ids: List<String>): List<Invoice>
}

