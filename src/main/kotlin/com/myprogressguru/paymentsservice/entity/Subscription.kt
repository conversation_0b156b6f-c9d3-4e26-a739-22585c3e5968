package com.myprogressguru.paymentsservice.entity

import com.myprogressguru.keycloakutils.service.model.UserIdResource
import com.myprogressguru.paymentsservice.enumeration.SubscriptionStatus
import jakarta.persistence.*
import java.time.LocalDate

@Entity
@Table(name = "subscriptions")
data class Subscription(

    @Column(nullable = false)
    override var userId: String,

    @ManyToOne
    var subscriptionPlan: SubscriptionPlan,

    @Column(nullable = false)
    var createdOn: LocalDate,

    @Column
    var trialEnd: LocalDate? = null,

    @Column
    var currentPeriodStart: LocalDate? = null,

    @Column
    var currentPeriodEnd: LocalDate? = null,

    @Enumerated(EnumType.STRING)
    var status: SubscriptionStatus

) : BaseEntity(), UserIdResource
