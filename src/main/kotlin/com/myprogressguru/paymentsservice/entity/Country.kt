package com.myprogressguru.paymentsservice.entity

import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.Id
import jakarta.persistence.Table

@Entity
@Table(name = "countries")
data class Country(

    @Id
    @Column(nullable = false, unique = true, updatable = false)
    var id: String,

    @Column(nullable = false)
    var name: String,

    @Column(nullable = false)
    var nameBg: String,

    )
