package com.myprogressguru.paymentsservice.entity

import com.myprogressguru.paymentsservice.enumeration.Currency
import jakarta.persistence.*
import java.time.ZonedDateTime

@Entity
@Table(name = "subscription_plan_offers")
data class SubscriptionPlanOffer(

    @Column(nullable = false)
    var campaignName: String,

    @Column(nullable = false)
    var validFrom: ZonedDateTime,

    @Column(nullable = false)
    var validTo: ZonedDateTime,

    @Column(nullable = false)
    var price: Double,

    @Enumerated(EnumType.STRING)
    var currency: Currency,

    @Column(nullable = false)
    var period: String,

    @Column(nullable = false)
    var productName: String,

    @ManyToOne
    var subscriptionPlan: SubscriptionPlan


) : BaseEntity()
