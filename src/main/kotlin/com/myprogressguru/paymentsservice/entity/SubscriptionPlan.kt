package com.myprogressguru.paymentsservice.entity

import com.myprogressguru.paymentsservice.enumeration.Currency
import com.myprogressguru.paymentsservice.enumeration.SubscriptionPlanPeriodType
import com.myprogressguru.paymentsservice.enumeration.SubscriptionPlanType
import jakarta.persistence.*

@Entity
@Table(name = "subscription_plans")
data class SubscriptionPlan(

    @Enumerated(EnumType.STRING)
    var type: SubscriptionPlanType,

    @Column(nullable = false)
    var price: Double,

    @Enumerated(EnumType.STRING)
    var currency: Currency,

    @Column(nullable = false)
    @get:JvmName("getIsDefault")
    @set:JvmName("setIsDefault")
    var isDefault: Boolean = false,

    @Column(nullable = false)
    @get:JvmName("getIsPublic")
    @set:JvmName("setIsPublic")
    var isPublic: Boolean = false,

    var productName: String?,

    @Enumerated(EnumType.STRING)
    var periodType: SubscriptionPlanPeriodType

) : BaseEntity()
