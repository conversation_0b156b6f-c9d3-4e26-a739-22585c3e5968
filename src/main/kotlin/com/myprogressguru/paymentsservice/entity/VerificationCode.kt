package com.myprogressguru.paymentsservice.entity

import com.myprogressguru.keycloakutils.service.model.UserIdResource
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.Table
import java.time.ZonedDateTime

@Entity
@Table(name = "verification_codes")
data class VerificationCode(

    @Column(nullable = false)
    override var userId: String,

    @Column(nullable = false)
    var expiresAt: ZonedDateTime,

    @Column(nullable = false)
    var code: String,

    @Column(nullable = false)
    var createdOn: ZonedDateTime

) : BaseEntity(), UserIdResource
