package com.myprogressguru.paymentsservice.entity

import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.ManyToOne
import jakarta.persistence.Table
import java.time.ZonedDateTime

@Entity
@Table(name = "purchased_subscription_plan_offers")
data class PurchasedSubscriptionPlanOffer(

    @Column(nullable = false)
    var userId: String,

    @Column(nullable = false)
    var purchasedAt: ZonedDateTime,

    @ManyToOne
    var subscriptionPlanOffer: SubscriptionPlanOffer


) : BaseEntity()
