package com.myprogressguru.paymentsservice.entity

import com.myprogressguru.keycloakutils.service.model.UserIdResource
import com.myprogressguru.paymentsservice.enumeration.Currency
import jakarta.persistence.*
import java.time.ZonedDateTime

@Entity
@Table(name = "payments")
data class Payment(

    @Column(nullable = false)
    override var userId: String,

    @Column(nullable = false, name = "card_pan")
    var cardPAN: String,

    @Column(nullable = false)
    var cardExpiration: String,

    @Column(nullable = false)
    var productName: String,

    @Column(nullable = false)
    var ipcTrnRef: String,

    @Column(nullable = false)
    var paymentReference: String,

    @Column(nullable = false)
    var amount: Double,

    @Enumerated(EnumType.STRING)
    var currency: Currency,

    @Column(nullable = false)
    var status: String,

    @Column(nullable = false)
    var statusMsg: String,

    @Column(nullable = false)
    var createdOn: ZonedDateTime

) : BaseEntity(), UserIdResource

