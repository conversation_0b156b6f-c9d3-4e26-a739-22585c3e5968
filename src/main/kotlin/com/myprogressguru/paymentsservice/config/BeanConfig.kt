package com.myprogressguru.paymentsservice.config

import com.myprogressguru.cloudutils.config.CloudUtilsBeanConfig
import com.myprogressguru.commonutils.config.CommonUtilsBeanConfig
import com.myprogressguru.notificationsutils.config.NotificationsUtilsBeanConfig
import com.myprogressguru.paymentsservice.config.properties.DiscordProperties
import com.myprogressguru.paymentsservice.config.properties.MyPOSProperties
import com.myprogressguru.paymentsservice.service.rest.client.DiscordClient
import com.myprogressguru.paymentsservice.service.rest.client.MyPOSAuthClient
import com.myprogressguru.paymentsservice.service.rest.client.MyPOSCheckoutClient
import com.myprogressguru.paymentsservice.service.rest.client.MyPOSTransactionsClient
import com.myprogressguru.paymentsservice.service.rest.interceptor.MyPOSInterceptor
import net.javacrumbs.shedlock.core.LockProvider
import net.javacrumbs.shedlock.provider.jdbctemplate.JdbcTemplateLockProvider
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Import
import org.springframework.web.client.RestClient
import org.springframework.web.client.support.RestClientAdapter
import org.springframework.web.service.invoker.HttpServiceProxyFactory
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.s3.presigner.S3Presigner
import java.security.KeyFactory
import java.security.PrivateKey
import java.security.PublicKey
import java.security.spec.PKCS8EncodedKeySpec
import java.security.spec.X509EncodedKeySpec
import java.util.*
import javax.sql.DataSource

@Import(value = [CommonUtilsBeanConfig::class, NotificationsUtilsBeanConfig::class, CloudUtilsBeanConfig::class])
@Configuration
@EnableConfigurationProperties(MyPOSProperties::class, DiscordProperties::class)
class BeanConfig {

    @Bean
    fun lockProvider(dataSource: DataSource): LockProvider = JdbcTemplateLockProvider(dataSource)

    @Bean
    fun publicKey(@Value("\${mypos.public-key}") myPOSPublicKey: String): PublicKey {
        val publicKey = myPOSPublicKey
            .replace("\\n", "\n")
            .replace("-----BEGIN PUBLIC KEY-----", "")
            .replace("-----END PUBLIC KEY-----", "")
            .replace("\\s".toRegex(), "")

        val encoded = Base64.getDecoder().decode(publicKey)
        val keySpec = X509EncodedKeySpec(encoded)
        val keyFactory = KeyFactory.getInstance("RSA")

        return keyFactory.generatePublic(keySpec)
    }

    @Bean
    fun privateKey(@Value("\${mypos.private-key}") myPOSPrivateKey: String): PrivateKey {
        val privateKeyStr = myPOSPrivateKey
            .replace("\\n", "\n")
            .replace("-----BEGIN PRIVATE KEY-----", "")
            .replace("-----END PRIVATE KEY-----", "")
            .replace("\\s".toRegex(), "")

        val encoded = Base64.getDecoder().decode(privateKeyStr)
        val keySpec = PKCS8EncodedKeySpec(encoded)
        val keyFactory = KeyFactory.getInstance("RSA")

        return keyFactory.generatePrivate(keySpec)
    }

    @Bean
    fun myPOSCheckoutClient(@Value("\${mypos.checkout-url}") myPOSCheckoutUrl: String): MyPOSCheckoutClient {
        val client = RestClient.create(myPOSCheckoutUrl)
        val factory = HttpServiceProxyFactory.builderFor(RestClientAdapter.create(client)).build()

        return factory.createClient(MyPOSCheckoutClient::class.java)
    }

    @Bean
    fun myPOSTransactionsClient(
        @Value("\${mypos.transactions-api-url}") myPOSTransactionsApiUrl: String,
        myPOSInterceptor: MyPOSInterceptor
    ): MyPOSTransactionsClient {
        val client = RestClient.builder().baseUrl(myPOSTransactionsApiUrl).requestInterceptor(myPOSInterceptor).build()
        val factory = HttpServiceProxyFactory.builderFor(RestClientAdapter.create(client)).build()

        return factory.createClient(MyPOSTransactionsClient::class.java)
    }

    @Bean
    fun myPOSAuthClient(@Value("\${mypos.auth-api-url}") myPOSAuthApiUrl: String): MyPOSAuthClient {
        val client = RestClient.create(myPOSAuthApiUrl)
        val factory = HttpServiceProxyFactory.builderFor(RestClientAdapter.create(client)).build()

        return factory.createClient(MyPOSAuthClient::class.java)
    }

    @Bean
    fun discordClient(): DiscordClient {
        val client = RestClient.create("https://discord.com/api")
        val factory = HttpServiceProxyFactory.builderFor(RestClientAdapter.create(client)).build()

        return factory.createClient(DiscordClient::class.java)
    }

    @Bean
    fun s3Presigner(@Value("\${aws.region}") region: String): S3Presigner =
        S3Presigner.builder().region(Region.of(region)).build()
}