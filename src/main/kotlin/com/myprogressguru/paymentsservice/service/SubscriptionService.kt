package com.myprogressguru.paymentsservice.service

import com.myprogressguru.paymentsservice.entity.Subscription
import com.myprogressguru.paymentsservice.entity.SubscriptionPlanOffer
import com.myprogressguru.paymentsservice.web.payload.subscription.SubscriptionCreateRequest
import com.myprogressguru.paymentsservice.web.payload.subscription.SubscriptionEditRequest
import com.myprogressguru.paymentsservice.web.payload.subscription.SubscriptionPublicEditRequest
import com.myprogressguru.paymentsservice.web.payload.subscription.SubscriptionResponse

interface SubscriptionService {

    fun get(userId: String): SubscriptionResponse?

    fun create(model: SubscriptionCreateRequest): SubscriptionResponse

    fun activateFreeTrial(userId: String): SubscriptionResponse

    fun edit(id: String, model: SubscriptionEditRequest)

    fun changeSubscriptionPlan(id: String, model: SubscriptionPublicEditRequest)

    fun delete(id: String)

    fun activate(id: String, verificationCode: String): SubscriptionResponse

    fun renew(id: String, verificationCode: String): SubscriptionResponse

    fun cancel(id: String): SubscriptionResponse

    fun activateOffer(userId: String, subscriptionPlanOffer: SubscriptionPlanOffer): Subscription

    fun runSubscriptionChecks()

}