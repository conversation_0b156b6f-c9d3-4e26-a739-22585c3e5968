package com.myprogressguru.paymentsservice.service.rest.client

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import org.springframework.http.MediaType.APPLICATION_FORM_URLENCODED_VALUE
import org.springframework.web.bind.annotation.RequestHeader
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.service.annotation.PostExchange

interface MyPOSAuthClient {

    @PostExchange("/oauth/token", contentType = APPLICATION_FORM_URLENCODED_VALUE)
    fun getToken(
        @RequestHeader("Authorization") authorization: String,
        @RequestParam body: Map<String, String> = mapOf(
            "grant_type" to "client_credentials",
        )
    ): MyPOSToken
}

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class MyPOSToken(
    val accessToken: String,
    val tokenType: String,
    val expiresIn: Long
)

