package com.myprogressguru.paymentsservice.service.rest.client

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.myprogressguru.paymentsservice.enumeration.Currency
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.service.annotation.GetExchange

interface MyPOSTransactionsClient {

    @GetExchange("/transactions")
    fun listTransactions(
        @RequestParam page: Int = 1,
        @RequestParam size: Int = 20,
        @RequestParam("from_date") fromDate: String = "2024-01-05",
        @RequestParam("transaction_types") transactionTypes: String = "006,013"
    ): TransactionsResponse
}

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class TransactionsResponse(
    val transactions: List<Transaction>,
    val pagination: Pagination
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class Transaction(
    val id: Long,
    val accountNumber: String,
    val ruid: String,
    val referenceNumberType: Int,
    val billingDescriptor: String,
    val pan: String,
    val description: String,
    val paymentReference: String,
    val transactionType: String,
    val transactionCurrency: Currency,
    val transactionAmount: Double,
    val originalCurrency: Currency,
    val originalAmount: Double,
    val date: String,
    val sign: String,
    val referenceNumber: String,
    val terminalId: String,
    val serialNumber: String
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class Pagination(
    val pageSize: Int,
    val page: Int,
    val total: Int
)


