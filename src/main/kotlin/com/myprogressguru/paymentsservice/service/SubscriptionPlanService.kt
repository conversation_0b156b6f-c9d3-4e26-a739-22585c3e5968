package com.myprogressguru.paymentsservice.service

import com.myprogressguru.paymentsservice.web.payload.subscription.*

interface SubscriptionPlanService {

    fun getAll(public: Boolean = false): List<SubscriptionPlanResponse>

    fun create(model: SubscriptionPlanRequest): SubscriptionPlanResponse

    fun setManual(model: ManualSubscriptionPlanRequest)

    fun getAllOffers(public: Boolean = false): List<SubscriptionPlanOfferResponse>

    fun activateOffer(id: String, code: String): PurchasedSubscriptionPlanOfferResponse

    fun getAllPurchasedOffers(userId: String): List<PurchasedSubscriptionPlanOfferResponse>
}