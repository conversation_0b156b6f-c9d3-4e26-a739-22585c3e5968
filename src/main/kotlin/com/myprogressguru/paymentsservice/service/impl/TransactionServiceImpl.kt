package com.myprogressguru.paymentsservice.service.impl

import com.myprogressguru.paymentsservice.service.InvoiceService
import com.myprogressguru.paymentsservice.service.PaymentsMapper
import com.myprogressguru.paymentsservice.service.TransactionService
import com.myprogressguru.paymentsservice.service.rest.client.MyPOSTransactionsClient
import com.myprogressguru.paymentsservice.web.payload.transaction.TransactionResponse
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.stereotype.Service

@Service
class TransactionServiceImpl(
    private val myPOSTransactionsClient: MyPOSTransactionsClient,
    private val invoiceService: InvoiceService,
    private val paymentsMapper: PaymentsMapper
) : TransactionService {

    override fun getAll(page: Int, size: Int): Page<TransactionResponse> {
        val transactionsResponse = myPOSTransactionsClient.listTransactions(page, size)

        val transactions = transactionsResponse.transactions
        val paymentReferences = transactions.map { it.paymentReference }

        val invoicesMap = invoiceService.getInvoicesMap(paymentReferences)

        return PageImpl(
            transactionsResponse.transactions.map {
                paymentsMapper.toTransactionResponse(it).apply { invoice = invoicesMap[paymentReference] }
            },
            PageRequest.of(page - 1, size),
            transactionsResponse.transactions.count().toLong()
        )
    }
}