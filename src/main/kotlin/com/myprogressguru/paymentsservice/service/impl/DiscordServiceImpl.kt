package com.myprogressguru.paymentsservice.service.impl

import com.myprogressguru.cloudutils.service.EmailService
import com.myprogressguru.commonutils.service.DateService
import com.myprogressguru.keycloakutils.enumeration.SubscriptionPlan
import com.myprogressguru.keycloakutils.service.KeycloakUtilsService
import com.myprogressguru.keycloakutils.service.UserService
import com.myprogressguru.paymentsservice.config.properties.DiscordProperties
import com.myprogressguru.paymentsservice.service.DiscordService
import com.myprogressguru.paymentsservice.service.rest.client.DiscordClient
import com.myprogressguru.paymentsservice.service.rest.client.UserToken
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service

@Service
class DiscordServiceImpl(
    private val userService: UserService,
    private val keycloakService: KeycloakUtilsService,
    private val discordClient: DiscordClient,
    private val discordProperties: DiscordProperties,
    private val emailService: EmailService,
    private val dateService: DateService,
    @Value("\${mpg.admin-email}") private val adminEmail: String,
) : DiscordService {

    private val logger = KotlinLogging.logger {}

    private val memberRoles = listOf(
        discordProperties.advancedRoleId,
        discordProperties.premiumRoleId
    )

    override fun joinGuild(code: String) {
        val accessToken = discordClient.getToken(
            mapOf(
                "client_id" to discordProperties.clientId,
                "client_secret" to discordProperties.clientSecret,
                "grant_type" to "authorization_code",
                "code" to code,
                "redirect_uri" to discordProperties.redirectUri
            )
        ).accessToken

        val user = keycloakService.getRequesterUserDetails()!!
        val userId = user.id
        val currentDiscordUserId = user.attributes["discordUserId"]

        val discordUser = discordClient.getUser("Bearer $accessToken")

        if (currentDiscordUserId != null && currentDiscordUserId != discordUser.id) {
            kickUser(currentDiscordUserId)
        }

        discordClient.addUserToGuild(
            botToken = "Bot ${discordProperties.botToken}",
            guildId = discordProperties.guildId,
            userId = discordUser.id,
            model = UserToken(
                accessToken,
            )
        )

        discordClient.addRoleToUser(
            botToken = "Bot ${discordProperties.botToken}",
            guildId = discordProperties.guildId,
            userId = discordUser.id,
            roleId = getRoleId(userId)
        )

        userService.addCustomAttribute(userId, "discordUserId", discordUser.id)

        emailService.sendEmail(
            to = adminEmail,
            subject = "New Discord Member",
            text = """
                Customer: ${user.firstName} ${user.lastName}
                Email: ${user.email}
                Date: ${dateService.getCurrentDate()}
            """.trimIndent()
        )
    }

    override fun handleSubscriptionPlanChange(userId: String) {
        val discordUserId = userService.getAccountData(userId).attributes["discordUserId"]
            ?: return

        val subscriptionPlan = userService.getSubscriptionPlan(userId)

        if (subscriptionPlan === SubscriptionPlan.ESSENTIALS) {
            kickUser(discordUserId)
            return
        }

        val member = try {
            discordClient.getMember(
                botToken = "Bot ${discordProperties.botToken}",
                guildId = discordProperties.guildId,
                userId = discordUserId
            )
        } catch (e: Exception) {
            return
        }

        member.roles.filter { it in memberRoles }.forEach {
            discordClient.removeRoleFromUser(
                botToken = "Bot ${discordProperties.botToken}",
                guildId = discordProperties.guildId,
                userId = discordUserId,
                roleId = it
            )
        }

        discordClient.addRoleToUser(
            botToken = "Bot ${discordProperties.botToken}",
            guildId = discordProperties.guildId,
            userId = discordUserId,
            roleId = getRoleId(userId)
        )

    }

    private fun getRoleId(userId: String): String {
        val subscriptionPlan = userService.getSubscriptionPlan(userId)

        if (subscriptionPlan == SubscriptionPlan.ADVANCED) {
            return discordProperties.advancedRoleId
        }

        return discordProperties.premiumRoleId
    }

    private fun kickUser(currentDiscordUserId: String) {
        try {
            discordClient.kickUserFromGuild(
                botToken = "Bot ${discordProperties.botToken}",
                guildId = discordProperties.guildId,
                userId = currentDiscordUserId
            )
        } catch (_: Exception) {
            logger.error { "Failed to kick user from Discord with id $currentDiscordUserId" }
        }
    }
}