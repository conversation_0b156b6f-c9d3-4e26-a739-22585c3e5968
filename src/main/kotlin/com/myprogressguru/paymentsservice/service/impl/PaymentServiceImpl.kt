package com.myprogressguru.paymentsservice.service.impl

import com.myprogressguru.paymentsservice.exception.UnsuccessfulPaymentException
import com.myprogressguru.paymentsservice.repository.PaymentRepository
import com.myprogressguru.paymentsservice.service.InvoiceService
import com.myprogressguru.paymentsservice.service.PaymentService
import com.myprogressguru.paymentsservice.service.PaymentsMapper
import com.myprogressguru.paymentsservice.web.payload.payment.PaymentRequest
import com.myprogressguru.paymentsservice.web.payload.payment.PaymentResponse
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.stereotype.Service
import kotlin.math.min

@Service
class PaymentServiceImpl(
    private val paymentRepository: PaymentRepository,
    private val paymentsMapper: PaymentsMapper,
    private val invoiceService: InvoiceService
) : PaymentService {

    override fun create(model: PaymentRequest): PaymentResponse {
        val payment = paymentRepository.save(paymentsMapper.toPayment(model))

        return paymentsMapper.toPaymentResponse(payment)
    }

    override fun getAll(page: Int, size: Int): Page<PaymentResponse> {
        val paymentsPage = paymentRepository.findAll(
            PageRequest.of(page - 1, min(size, 100), Sort.by("createdOn").descending())
        )

        val paymentReferences = paymentsPage.content.map { it.paymentReference }
        val invoicesMap = invoiceService.getInvoicesMap(paymentReferences)

        return paymentsPage.map {
            paymentsMapper.toPaymentResponse(it).apply { invoice = invoicesMap[it.paymentReference] }
        }
    }

    override fun isPaymentSuccessful(payment: PaymentResponse?): Boolean = payment != null && payment.isSuccessful()

    override fun throwIfPaymentUnsuccessful(payment: PaymentResponse?) {
        if (!isPaymentSuccessful(payment)) {
            val message = payment?.statusMsg ?: "No active stored card found."

            throw UnsuccessfulPaymentException("Unsuccessful payment: $message")
        }
    }
}