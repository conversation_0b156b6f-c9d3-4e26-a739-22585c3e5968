package com.myprogressguru.paymentsservice.service.impl

import com.myprogressguru.commonutils.service.DateService
import com.myprogressguru.keycloakutils.service.UserService
import com.myprogressguru.paymentsservice.entity.StoredCard
import com.myprogressguru.paymentsservice.exception.ActiveStoredCardDeleteNotAllowedException
import com.myprogressguru.paymentsservice.exception.InvalidStoredCardException
import com.myprogressguru.paymentsservice.repository.StoredCardRepository
import com.myprogressguru.paymentsservice.service.PaymentsMapper
import com.myprogressguru.paymentsservice.service.StoredCardService
import com.myprogressguru.paymentsservice.service.model.ActiveStoredCardResponse
import com.myprogressguru.paymentsservice.web.payload.card.StoredCardEditRequest
import com.myprogressguru.paymentsservice.web.payload.card.StoredCardInfoResponse
import jakarta.transaction.Transactional
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service

@Service
class StoredCardServiceImpl(
    private val storedCardRepository: StoredCardRepository,
    private val dateService: DateService,
    private val userService: UserService,
    private val paymentsMapper: PaymentsMapper
) : StoredCardService {

    override fun getAll(userId: String): List<StoredCardInfoResponse> =
        paymentsMapper.toStoredCardInfoResponseList(storedCardRepository.findAllByUserId(userId))

    override fun create(request: Map<String, String>) {
        val userEmail = request["CustomerEmail"]!!
        val userId = userService.getAccountDataByEmail(userEmail).id

        val storedCard = StoredCard(
            userId = userId,
            expiration = getExpiration(request),
            token = request["CardToken"]!!,
            pan = request["PAN"]!!,
            orderId = request["OrderID"]!!,
            myPOSNotifyPayload = request,
            createdOn = dateService.getCurrentDateTime(),
            isActive = !storedCardRepository.existsByUserId(userId)
        )

        storedCardRepository.save(storedCard)
    }

    @Transactional
    override fun edit(id: String, model: StoredCardEditRequest) {
        val storedCard = getStoredCard(id)

        storedCardRepository.deactivateCardsByUserId(storedCard.userId)

        storedCard.isActive = true

        storedCardRepository.save(storedCard)
    }

    @Transactional
    override fun delete(id: String) {
        val storedCard = getStoredCard(id)
        if (storedCard.isActive) {
            throw ActiveStoredCardDeleteNotAllowedException()
        }

        storedCardRepository.delete(storedCard)
    }

    @Transactional
    override fun rollbackCreation(orderId: String) = storedCardRepository.deleteByOrderId(orderId)

    override fun getActive(userId: String): ActiveStoredCardResponse? {
        val storedCard =
            storedCardRepository.findTopByUserIdAndIsActiveIsTrue(userId) ?: return null

        return paymentsMapper.toActiveStoredCardResponse(storedCard)
    }

    private fun getExpiration(request: Map<String, String>): String {
        val expiration = request["ExpDate"]!!

        val year = expiration.substring(0, 2)
        val month = expiration.substring(2, 4)

        return "$month/$year"
    }

    private fun getStoredCard(id: String) =
        storedCardRepository.findByIdOrNull(id) ?: throw InvalidStoredCardException(id)
}