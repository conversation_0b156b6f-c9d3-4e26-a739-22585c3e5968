package com.myprogressguru.paymentsservice.service.impl

import com.myprogressguru.commonutils.service.DateService
import com.myprogressguru.keycloakutils.service.KeycloakUtilsService
import com.myprogressguru.keycloakutils.service.UserService
import com.myprogressguru.paymentsservice.entity.PurchasedSubscriptionPlanOffer
import com.myprogressguru.paymentsservice.entity.QSubscriptionPlanOffer
import com.myprogressguru.paymentsservice.entity.SubscriptionPlan
import com.myprogressguru.paymentsservice.entity.SubscriptionPlanOffer
import com.myprogressguru.paymentsservice.exception.DuplicateCampaignSubscriptionPlanOfferException
import com.myprogressguru.paymentsservice.exception.ExpiredSubscriptionPlanOfferException
import com.myprogressguru.paymentsservice.exception.InvalidSubscriptionPlanOfferException
import com.myprogressguru.paymentsservice.exception.SubscriptionPlanAlreadyExistsException
import com.myprogressguru.paymentsservice.repository.PurchasedSubscriptionPlanOfferRepository
import com.myprogressguru.paymentsservice.repository.SubscriptionPlanOfferRepository
import com.myprogressguru.paymentsservice.repository.SubscriptionPlanRepository
import com.myprogressguru.paymentsservice.service.*
import com.myprogressguru.paymentsservice.service.model.ChargeUserRequest
import com.myprogressguru.paymentsservice.web.payload.subscription.*
import com.querydsl.core.types.dsl.Expressions
import org.springframework.data.domain.Sort
import org.springframework.stereotype.Service

@Service
class SubscriptionPlanServiceImpl(
    private val subscriptionPlanRepository: SubscriptionPlanRepository,
    private val subscriptionPlanOfferRepository: SubscriptionPlanOfferRepository,
    private val purchasedSubscriptionPlanOfferRepository: PurchasedSubscriptionPlanOfferRepository,
    private val paymentsMapper: PaymentsMapper,
    private val userService: UserService,
    private val discordService: DiscordService,
    private val keycloakService: KeycloakUtilsService,
    private val dateService: DateService,
    private val subscriptionService: SubscriptionService,
    private val myPOSService: MyPOSService,
    private val paymentService: PaymentService,
    private val verificationCodeService: VerificationCodeService
) : SubscriptionPlanService {

    companion object {
        private val OFFER_PATH = QSubscriptionPlanOffer.subscriptionPlanOffer
    }

    override fun getAll(public: Boolean): List<SubscriptionPlanResponse> {
        val isUserAdmin = keycloakService.isRequesterAdmin()

        val subscriptionPlans = if (public || !isUserAdmin) {
            subscriptionPlanRepository.findAllByIsPublicTrueOrderByPriceAscCurrencyAsc()
        } else {
            subscriptionPlanRepository.findAllByOrderByPriceAscCurrencyAsc()
        }

        return paymentsMapper.toSubscriptionPlanResponseList(subscriptionPlans)
    }

    override fun create(model: SubscriptionPlanRequest): SubscriptionPlanResponse {
        if (subscriptionPlanRepository.existsByPriceAndCurrency(model.price!!, model.currency!!)) {
            throw SubscriptionPlanAlreadyExistsException()
        }

        val subscriptionPlan = subscriptionPlanRepository.save(
            SubscriptionPlan(
                type = model.type!!,
                price = model.price,
                currency = model.currency,
                productName = model.productName,
                periodType = model.periodType!!
            )
        )

        return paymentsMapper.toSubscriptionPlanResponse(subscriptionPlan)
    }

    override fun setManual(model: ManualSubscriptionPlanRequest) {
        userService.addCustomAttribute(
            model.userId!!,
            "subscriptionPlan",
            model.type!!.name
        )

        discordService.handleSubscriptionPlanChange(model.userId)
    }

    override fun getAllOffers(public: Boolean): List<SubscriptionPlanOfferResponse> {
        val isUserAdmin = keycloakService.isRequesterAdmin()
        val now = dateService.getCurrentDateTime()
        var predicate = Expressions.TRUE.isTrue

        if (public || !isUserAdmin) {
            predicate = OFFER_PATH.validFrom.loe(now).and(OFFER_PATH.validTo.goe(now))
        }

        val subscriptionPlanOffers =
            subscriptionPlanOfferRepository.findAll(predicate, Sort.by(Sort.Order.desc("price")))

        return paymentsMapper.toSubscriptionPlanOfferResponseList(subscriptionPlanOffers)
    }

    override fun activateOffer(id: String, code: String): PurchasedSubscriptionPlanOfferResponse {
        val subscriptionPlanOffer = getSubscriptionPlanOffer(id)
        val userId = keycloakService.getRequesterId()!!

        verificationCodeService.verifyCode(userId, code)

        validateExpiration(subscriptionPlanOffer)
        validateCampaign(userId, subscriptionPlanOffer)

        val payment = myPOSService.chargeUser(
            ChargeUserRequest(
                userId = userId,
                amount = subscriptionPlanOffer.price,
                currency = subscriptionPlanOffer.currency,
                productName = subscriptionPlanOffer.productName
            )
        )

        paymentService.throwIfPaymentUnsuccessful(payment)

        subscriptionService.activateOffer(userId, subscriptionPlanOffer)

        val purchasedSubscriptionPlanOffer = purchasedSubscriptionPlanOfferRepository.save(
            PurchasedSubscriptionPlanOffer(
                userId = userId,
                subscriptionPlanOffer = subscriptionPlanOffer,
                purchasedAt = dateService.getCurrentDateTime(),
            )
        )

        purchasedSubscriptionPlanOfferRepository.save(purchasedSubscriptionPlanOffer)

        return paymentsMapper.toPurchasedSubscriptionPlanOfferResponse(purchasedSubscriptionPlanOffer)
    }

    private fun validateExpiration(subscriptionPlanOffer: SubscriptionPlanOffer) {
        val now = dateService.getCurrentDateTime()

        if (subscriptionPlanOffer.validFrom.isAfter(now) || subscriptionPlanOffer.validTo.isBefore(now)) {
            throw ExpiredSubscriptionPlanOfferException()
        }
    }

    override fun getAllPurchasedOffers(userId: String): List<PurchasedSubscriptionPlanOfferResponse> {
        val purchasedSubscriptionPlanOffers = purchasedSubscriptionPlanOfferRepository.findAllByUserId(userId)

        return paymentsMapper.toPurchasedSubscriptionPlanOfferResponseList(purchasedSubscriptionPlanOffers)
    }

    private fun validateCampaign(
        userId: String,
        subscriptionPlanOffer: SubscriptionPlanOffer
    ) {
        if (purchasedSubscriptionPlanOfferRepository.existsByUserIdAndSubscriptionPlanOfferCampaignName(
                userId,
                subscriptionPlanOffer.campaignName
            )
        ) {
            throw DuplicateCampaignSubscriptionPlanOfferException()
        }
    }

    private fun getSubscriptionPlanOffer(id: String): SubscriptionPlanOffer =
        subscriptionPlanOfferRepository.findById(id).orElseThrow { throw InvalidSubscriptionPlanOfferException(id) }
}