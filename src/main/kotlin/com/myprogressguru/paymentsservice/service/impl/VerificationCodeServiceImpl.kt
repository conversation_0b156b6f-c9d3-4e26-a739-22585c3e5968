package com.myprogressguru.paymentsservice.service.impl

import com.myprogressguru.cloudutils.service.EmailService
import com.myprogressguru.commonutils.service.DateService
import com.myprogressguru.keycloakutils.service.UserService
import com.myprogressguru.paymentsservice.entity.VerificationCode
import com.myprogressguru.paymentsservice.exception.ExpiredVerificationCodeException
import com.myprogressguru.paymentsservice.exception.InvalidVerificationCodeException
import com.myprogressguru.paymentsservice.repository.VerificationCodeRepository
import com.myprogressguru.paymentsservice.service.VerificationCodeService
import io.github.oshai.kotlinlogging.KotlinLogging
import jakarta.transaction.Transactional
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import kotlin.random.Random

@Service
class VerificationCodeServiceImpl(
    private val emailService: EmailService,
    private val verificationCodeRepository: VerificationCodeRepository,
    private val dateService: DateService,
    private val userService: UserService
) : VerificationCodeService {

    private val logger = KotlinLogging.logger {}

    override fun generateCode(userId: String) {
        val activeVerificationCode =
            verificationCodeRepository.findByUserIdAndExpiresAtAfter(userId, dateService.getCurrentDateTime())

        if (activeVerificationCode != null) {
            return
        }

        val verificationCode = VerificationCode(
            userId = userId,
            expiresAt = dateService.getCurrentDateTime().plusMinutes(5),
            code = generateCode(),
            createdOn = dateService.getCurrentDateTime()
        )
        verificationCodeRepository.save(verificationCode)

        val email = userService.getAccountData(userId).email
        emailService.sendEmail(
            email,
            "Verification code - ${verificationCode.code}",
            "Your verification code is: ${verificationCode.code}"
        )
    }

    override fun verifyCode(userId: String, code: String) {
        val verificationCode = verificationCodeRepository.findByUserIdAndCode(userId, code)
            ?: throw InvalidVerificationCodeException()

        if (verificationCode.expiresAt.isBefore(dateService.getCurrentDateTime())) {
            throw ExpiredVerificationCodeException()
        }

        verificationCodeRepository.delete(verificationCode)
    }

    @Transactional
    @Scheduled(cron = "0 0 13 * * *")
    @SchedulerLock(name = "delete-expired-tokens")
    override fun deleteExpiredTokens() {
        val expiredTokens = verificationCodeRepository.findAllByExpiresAtBefore(dateService.getCurrentDateTime())

        verificationCodeRepository.deleteAll(expiredTokens)

        logger.info { "Deleted ${expiredTokens.size} expired verification codes" }
    }

    private fun generateCode(): String {
        return Random.nextInt(100000, 999999).toString()
    }

}