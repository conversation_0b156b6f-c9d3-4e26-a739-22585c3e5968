package com.myprogressguru.paymentsservice.service.impl

import com.myprogressguru.cloudutils.service.EmailService
import com.myprogressguru.commonutils.service.DateService
import com.myprogressguru.keycloakutils.service.UserService
import com.myprogressguru.paymentsservice.config.properties.MyPOSProperties
import com.myprogressguru.paymentsservice.enumeration.Currency
import com.myprogressguru.paymentsservice.exception.InvalidSignatureException
import com.myprogressguru.paymentsservice.service.InvoiceService
import com.myprogressguru.paymentsservice.service.MyPOSService
import com.myprogressguru.paymentsservice.service.PaymentService
import com.myprogressguru.paymentsservice.service.StoredCardService
import com.myprogressguru.paymentsservice.service.model.ChargeUserRequest
import com.myprogressguru.paymentsservice.service.rest.client.MyPOSCheckoutClient
import com.myprogressguru.paymentsservice.web.payload.payment.PaymentRequest
import com.myprogressguru.paymentsservice.web.payload.payment.PaymentResponse
import io.awspring.cloud.sqs.annotation.SqsListener
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.messaging.handler.annotation.Payload
import org.springframework.stereotype.Service
import java.security.PrivateKey
import java.security.PublicKey
import java.security.Signature
import java.util.*

@Service
class MyPOSServiceImpl(
    private val publicKey: PublicKey,
    private val privateKey: PrivateKey,
    private val myPOSCheckoutClient: MyPOSCheckoutClient,
    private val storedCardService: StoredCardService,
    private val paymentService: PaymentService,
    private val myPOSProperties: MyPOSProperties,
    private val dateService: DateService,
    private val invoiceService: InvoiceService,
    private val userService: UserService,
    private val emailService: EmailService,
    @Value("\${mpg.admin-email}") private val adminEmail: String,
) : MyPOSService {

    private val logger = KotlinLogging.logger {}

    override fun chargeUser(model: ChargeUserRequest): PaymentResponse? {
        val storedCard = storedCardService.getActive(model.userId) ?: return null

        val request = mutableMapOf(
            "IPCmethod" to "IPCIAPurchase",
            "Version" to "1.4",
            "OrderID" to UUID.randomUUID().toString(),
            "SID" to myPOSProperties.sid,
            "WalletNumber" to myPOSProperties.walletNumber,
            "KeyIndex" to myPOSProperties.keyIndex,
            "CardToken" to storedCard.token,
            "CartItems" to "1",
            "Article_1" to model.productName,
            "Quantity_1" to "1",
            "Price_1" to "${model.amount}",
            "Amount_1" to "${model.amount}",
            "Currency_1" to model.currency.name,
            "Amount" to "${model.amount}",
            "Currency" to model.currency.name,
            "OutputFormat" to "JSON"
        )

        request["Signature"] = signRequest(request)

        val result = myPOSCheckoutClient.checkout(request)

        if (!isPayloadVerified(result)) {
            throw InvalidSignatureException()
        }

        val payment = try {
            paymentService.create(
                PaymentRequest(
                    userId = model.userId,
                    cardPAN = storedCard.pan,
                    cardExpiration = storedCard.expiration,
                    productName = model.productName,
                    ipcTrnRef = result["IPC_Trnref"]!!,
                    paymentReference = result["PaymentReference"]!!,
                    amount = result["Amount"]!!.toDouble(),
                    currency = Currency.valueOf(result["Currency"]!!),
                    status = result["Status"]!!,
                    statusMsg = result["StatusMsg"]!!,
                    createdOn = dateService.getCurrentDateTime()
                )
            )
        } catch (e: Exception) {
            logger.error { "Failed to create payment for userId ${model.userId} with myPOS response: $result" }

            val user = userService.getAccountData(model.userId)
            emailService.sendEmail(
                to = adminEmail,
                subject = "Failed MyPOS Payment",
                text = """
                Customer: ${user.firstName} ${user.lastName}
                Email: ${user.email}
                Date: ${dateService.getCurrentDate()}
                Product: ${model.productName}
                Price: ${model.amount} ${model.currency}
                Status: ${result["Status"]}
                SubStatus: ${result["SubStatus"]}
                Status Message: ${result["StatusMsg"]}
            """.trimIndent()
            )

            return null
        }

        if (payment.isSuccessful()) {
            invoiceService.create(payment)
        }

        return payment
    }


    @SqsListener("mypos_notify.fifo")
    override fun handleNotify(@Payload request: Map<String, String>) {
        if (!isPayloadVerified(request)) {
            return
        }

        val ipcMethod = request["IPCmethod"]

        if (ipcMethod == "IPCPurchaseNotify") {
            storedCardService.create(request)
        } else if (ipcMethod == "IPCPurchaseRollback") {
            storedCardService.rollbackCreation(request["OrderID"]!!)
        }
    }

    private fun isPayloadVerified(payload: Map<String, String>): Boolean {
        val signatureBase64 = payload["Signature"] ?: return false

        val data = Base64.getEncoder()
            .encode(payload.filterKeys { it != "Signature" }.values.joinToString("-").toByteArray())

        val signatureBytes = Base64.getDecoder().decode(signatureBase64)
        val signature = Signature.getInstance("SHA256withRSA")
        signature.initVerify(publicKey)
        signature.update(data)

        return try {
            signature.verify(signatureBytes)
        } catch (e: Exception) {
            false
        }
    }

    private fun signRequest(request: Map<String, String>): String {
        val data = Base64.getEncoder().encode(request.values.joinToString("-").toByteArray())

        val signature = Signature.getInstance("SHA256withRSA")
        signature.initSign(privateKey)
        signature.update(data)
        val digitalSignature = signature.sign()

        return Base64.getEncoder().encodeToString(digitalSignature)
    }
}
