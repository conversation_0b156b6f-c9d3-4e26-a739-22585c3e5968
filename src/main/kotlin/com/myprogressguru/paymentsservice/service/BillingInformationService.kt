package com.myprogressguru.paymentsservice.service

import com.myprogressguru.paymentsservice.web.payload.invoice.BillingInformationRequest
import com.myprogressguru.paymentsservice.web.payload.invoice.BillingInformationResponse
import org.springframework.data.domain.Page

interface BillingInformationService {

    fun get(userId: String): BillingInformationResponse?

    fun search(page: Int, size: Int, search: String?): Page<BillingInformationResponse>

    fun create(model: BillingInformationRequest): BillingInformationResponse

    fun edit(id: String, model: BillingInformationRequest)

}