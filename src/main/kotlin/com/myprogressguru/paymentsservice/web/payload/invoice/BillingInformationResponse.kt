package com.myprogressguru.paymentsservice.web.payload.invoice

import com.fasterxml.jackson.annotation.JsonInclude
import com.myprogressguru.paymentsservice.enumeration.BillingInformationType
import com.myprogressguru.paymentsservice.web.payload.country.CountryResponse

@JsonInclude(JsonInclude.Include.NON_NULL)
data class BillingInformationResponse(

    val id: String,

    val type: BillingInformationType,

    val userId: String?,

    val email: String,

    val firstName: String,

    val middleName: String?,

    val lastName: String,

    val city: String,

    val country: CountryResponse,

    val address: String,

    val postalCode: String,

    val phoneNumber: String,

    val companyName: String?,

    val unifiedIdCode: String?,

    val vatNumber: String?

)
