package com.myprogressguru.paymentsservice.web.payload.subscription

import com.myprogressguru.paymentsservice.enumeration.SubscriptionStatus
import java.time.LocalDate

data class SubscriptionResponse(

    val id: String,

    val userId: String,

    val subscriptionPlan: SubscriptionPlanResponse,

    val createdOn: LocalDate,

    val trialEnd: LocalDate?,

    val currentPeriodStart: LocalDate?,

    val currentPeriodEnd: LocalDate?,

    val status: SubscriptionStatus

)
