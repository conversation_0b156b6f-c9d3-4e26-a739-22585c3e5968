package com.myprogressguru.paymentsservice.web.payload.payment

import com.myprogressguru.paymentsservice.enumeration.Currency
import java.time.ZonedDateTime

data class PaymentRequest(

    val userId: String,

    val cardPAN: String,

    val cardExpiration: String,

    val productName: String,

    val ipcTrnRef: String,

    val paymentReference: String,

    val amount: Double,

    val currency: Currency,

    val status: String,

    val statusMsg: String,

    val createdOn: ZonedDateTime

)
