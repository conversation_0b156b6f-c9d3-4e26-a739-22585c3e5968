package com.myprogressguru.paymentsservice.web.controller

import com.myprogressguru.paymentsservice.service.VerificationCodeService
import com.myprogressguru.paymentsservice.web.payload.subscription.VerificationCodeCreateRequest
import jakarta.validation.Valid
import org.springframework.http.HttpStatus
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/v1/verification-codes")
class VerificationCodeController(
    private val verificationCodeService: VerificationCodeService
) {

    @ResponseStatus(HttpStatus.CREATED)
    @PreAuthorize("@keycloakService.requesterIdMatchesUserId(#model.userId)")
    @PostMapping
    fun create(
        @Valid @RequestBody model: VerificationCodeCreateRequest
    ) = verificationCodeService.generateCode(model.userId!!)
}