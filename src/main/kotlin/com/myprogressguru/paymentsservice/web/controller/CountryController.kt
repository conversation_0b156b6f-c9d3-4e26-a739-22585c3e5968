package com.myprogressguru.paymentsservice.web.controller

import com.myprogressguru.paymentsservice.service.CountryService
import com.myprogressguru.paymentsservice.web.payload.country.CountryResponse
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/v1/countries")
class CountryController(
    private val countryService: CountryService
) {

    @GetMapping
    fun getAll(
    ): List<CountryResponse> = countryService.getAll()

}