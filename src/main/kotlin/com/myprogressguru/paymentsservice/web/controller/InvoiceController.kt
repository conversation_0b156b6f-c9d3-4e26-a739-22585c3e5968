package com.myprogressguru.paymentsservice.web.controller

import com.myprogressguru.keycloakutils.constant.AuthConstants.Authorization.ADMIN
import com.myprogressguru.paymentsservice.service.InvoiceService
import com.myprogressguru.paymentsservice.web.payload.invoice.*
import jakarta.validation.Valid
import org.springframework.data.domain.Page
import org.springframework.http.HttpStatus
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

@RestController
@PreAuthorize(ADMIN)
@RequestMapping("/v1/invoices")
class InvoiceController(
    private val invoiceService: InvoiceService
) {

    @GetMapping
    fun getAll(
        @RequestParam(required = false, defaultValue = "1") page: Int,
        @RequestParam(required = false, defaultValue = "100") size: Int,
    ): Page<InvoiceResponse> = invoiceService.getAll(page, size)

    @ResponseStatus(HttpStatus.CREATED)
    @PostMapping
    fun create(
        @Valid @RequestBody model: InvoiceCreateRequest
    ): InvoiceResponse = invoiceService.create(model)

    @PatchMapping("/{id}")
    fun edit(
        @PathVariable id: String,
        @Valid @RequestBody model: InvoiceEditRequest
    ) = invoiceService.edit(id, model)

    @PostMapping("/{id}/pdf")
    fun generatePdf(@PathVariable id: String) = invoiceService.generatePdf(id)

    @GetMapping("/{id}/pdf")
    fun getPdf(@PathVariable id: String): InvoicePdfResponse = invoiceService.generatePdfUrl(id)

    @PostMapping("/batch-submit")
    fun batchSubmit(@Valid @RequestBody model: BatchOperationRequest) =
        invoiceService.batchSubmit(model)

    @PostMapping("/batch-pdfs")
    fun generateBatchPdfs(@Valid @RequestBody model: BatchOperationRequest) =
        invoiceService.generateBatchPdfs(model)
}