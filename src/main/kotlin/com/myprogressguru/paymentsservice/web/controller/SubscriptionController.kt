package com.myprogressguru.paymentsservice.web.controller

import com.myprogressguru.keycloakutils.constant.AuthConstants.Authorization.ADMIN
import com.myprogressguru.paymentsservice.service.SubscriptionService
import com.myprogressguru.paymentsservice.web.payload.subscription.*
import jakarta.validation.Valid
import org.springframework.http.HttpStatus
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/v1/subscriptions")
class SubscriptionController(
    private val subscriptionService: SubscriptionService
) {

    @PreAuthorize("@keycloakService.isAdminOrTrainerOfOrTrainee(#userId)")
    @GetMapping
    fun get(
        @RequestParam(required = true) userId: String
    ): SubscriptionResponse? = subscriptionService.get(userId)

    @ResponseStatus(HttpStatus.CREATED)
    @PreAuthorize(ADMIN)
    @PostMapping
    fun create(
        @Valid @RequestBody model: SubscriptionCreateRequest
    ): SubscriptionResponse = subscriptionService.create(model)

    @ResponseStatus(HttpStatus.CREATED)
    @PreAuthorize("@keycloakService.isRequesterAdminOrRequesterIdMatchesUserId(#model.userId)")
    @PostMapping("/free-trial")
    fun activateFreeTrial(
        @Valid @RequestBody model: SubscriptionFreeTrialCreateRequest
    ): SubscriptionResponse = subscriptionService.activateFreeTrial(model.userId!!)

    @PreAuthorize(ADMIN)
    @PatchMapping("/{id}")
    fun edit(
        @PathVariable id: String,
        @Valid @RequestBody model: SubscriptionEditRequest
    ) = subscriptionService.edit(id, model)

    @PreAuthorize("@keycloakService.isAdminOrUserOwnerOf(@subscriptionRepository, #id)")
    @PatchMapping("/{id}/subscription-plan")
    fun changeSubscriptionPlan(
        @PathVariable id: String,
        @Valid @RequestBody model: SubscriptionPublicEditRequest
    ) = subscriptionService.changeSubscriptionPlan(id, model)

    @PreAuthorize(ADMIN)
    @DeleteMapping("/{id}")
    fun delete(
        @PathVariable id: String,
    ) = subscriptionService.delete(id)

    @PreAuthorize("@keycloakService.isAdminOrTrainerOfOrUserOwnerOf(@subscriptionRepository, #id)")
    @PostMapping("/{id}/renew")
    fun renew(
        @PathVariable id: String,
        @Valid @RequestBody model: VerificationCodeRequest
    ): SubscriptionResponse = subscriptionService.renew(id, model.code!!)

    @PreAuthorize("@keycloakService.isAdminOrTrainerOfOrUserOwnerOf(@subscriptionRepository, #id)")
    @PostMapping("/{id}/cancel")
    fun cancel(
        @PathVariable id: String
    ): SubscriptionResponse = subscriptionService.cancel(id)

    @PreAuthorize("@keycloakService.isAdminOrTrainerOfOrUserOwnerOf(@subscriptionRepository, #id)")
    @PostMapping("/{id}/activate")
    fun activate(
        @PathVariable id: String,
        @Valid @RequestBody model: VerificationCodeRequest
    ): SubscriptionResponse = subscriptionService.activate(id, model.code!!)

}