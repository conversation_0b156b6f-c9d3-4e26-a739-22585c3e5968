package com.myprogressguru.paymentsservice.web.controller

import com.myprogressguru.paymentsservice.service.StoredCardService
import com.myprogressguru.paymentsservice.web.payload.card.StoredCardEditRequest
import com.myprogressguru.paymentsservice.web.payload.card.StoredCardInfoResponse
import jakarta.validation.Valid
import org.springframework.http.HttpStatus
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/v1/stored-cards")
class StoredCardController(
    private val storedCardService: StoredCardService
) {

    @PreAuthorize("@keycloakService.isAdminOrTrainerOfOrTrainee(#userId)")
    @GetMapping
    fun getAll(
        @RequestParam(required = true) userId: String
    ): List<StoredCardInfoResponse> = storedCardService.getAll(userId)

    @PreAuthorize("@keycloakService.isAdminOrTrainerOfOrUserOwnerOf(@storedCardRepository, #id)")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @PatchMapping("/{id}")
    fun edit(
        @PathVariable id: String,
        @Valid @RequestBody model: StoredCardEditRequest
    ) = storedCardService.edit(id, model)

    @PreAuthorize("@keycloakService.isAdminOrTrainerOfOrUserOwnerOf(@storedCardRepository, #id)")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @DeleteMapping("/{id}")
    fun delete(
        @PathVariable id: String
    ) = storedCardService.delete(id)

}