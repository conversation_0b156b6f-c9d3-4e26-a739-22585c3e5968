package com.myprogressguru.paymentsservice.web.controller

import com.myprogressguru.keycloakutils.constant.AuthConstants.Authorization.ADVANCED_PLAN
import com.myprogressguru.paymentsservice.service.DiscordService
import com.myprogressguru.paymentsservice.web.payload.discord.DiscordJoinRequest
import jakarta.validation.Valid
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/v1/discord")
class DiscordController(
    private val discordService: DiscordService
) {

    @PreAuthorize(ADVANCED_PLAN)
    @PostMapping("/join")
    fun join(
        @Valid @RequestBody model: DiscordJoinRequest
    ) = discordService.joinGuild(model.code!!)

}