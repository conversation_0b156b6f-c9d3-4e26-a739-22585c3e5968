package com.myprogressguru.paymentsservice.web.controller

import com.myprogressguru.keycloakutils.constant.AuthConstants.Authorization.ADMIN
import com.myprogressguru.paymentsservice.service.BillingInformationService
import com.myprogressguru.paymentsservice.web.payload.invoice.BillingInformationRequest
import com.myprogressguru.paymentsservice.web.payload.invoice.BillingInformationResponse
import jakarta.validation.Valid
import org.springframework.data.domain.Page
import org.springframework.http.HttpStatus
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/v1/billing-information")
class BillingInformationController(
    private val billingInformationService: BillingInformationService
) {

    @PreAuthorize("@keycloakService.isAdminOrTrainerOfOrTrainee(#userId)")
    @GetMapping
    fun get(
        @RequestParam(required = true) userId: String
    ): BillingInformationResponse? = billingInformationService.get(userId)

    @PreAuthorize(ADMIN)
    @GetMapping("/search")
    fun search(
        @RequestParam(required = false) search: String?,
        @RequestParam(required = false, defaultValue = "1") page: Int,
        @RequestParam(required = false, defaultValue = "10") size: Int,
    ): Page<BillingInformationResponse> = billingInformationService.search(page, size, search)

    @ResponseStatus(HttpStatus.CREATED)
    @PreAuthorize("@keycloakService.isAdminOrTrainerOfOrTrainee(#model.userId)")
    @PostMapping
    fun create(
        @Valid @RequestBody model: BillingInformationRequest
    ): BillingInformationResponse = billingInformationService.create(model)

    @ResponseStatus(HttpStatus.NO_CONTENT)
    @PreAuthorize("@keycloakService.isAdminOrTrainerOfOrOptionalUserOwnerOf(@billingInformationRepository, #id)")
    @PutMapping("/{id}")
    fun edit(
        @PathVariable id: String,
        @Valid @RequestBody model: BillingInformationRequest
    ) = billingInformationService.edit(id, model)

}