package com.myprogressguru.storageutils.repository

import com.myprogressguru.storageutils.entity.StorageObject
import org.springframework.data.jpa.repository.JpaRepository

interface StorageObjectRepository: JpaRepository<StorageObject, String> {

    fun findAllByIsAssignedIsFalseOrIsUploadedIsFalse(): List<StorageObject>

    fun findAllByUrl(url: String): List<StorageObject>

    fun findAllByUrlIn(urls: List<String>): List<StorageObject>
}