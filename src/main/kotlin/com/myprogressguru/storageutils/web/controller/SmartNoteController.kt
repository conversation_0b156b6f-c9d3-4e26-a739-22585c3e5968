package com.myprogressguru.storageutils.web.controller

import com.myprogressguru.storageutils.service.SmartNoteService
import com.myprogressguru.storageutils.service.model.StorageObjectResponse
import com.myprogressguru.storageutils.web.payload.StorageObjectUploadUrlRequest
import com.myprogressguru.storageutils.web.payload.notes.SmartNoteEditRequest
import jakarta.validation.Valid
import org.springframework.http.HttpStatus
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/v1/smart-notes")
class SmartNoteController(
    private val smartNoteService: SmartNoteService
) {

    @ResponseStatus(HttpStatus.NO_CONTENT)
    @PreAuthorize("@keycloakService.isAdminOrUserOwnerOf(@smartNoteRepository, #id)")
    @PutMapping("/{id}")
    fun edit(
        @PathVariable id: String,
        @Valid @RequestBody model: SmartNoteEditRequest
    ) = smartNoteService.edit(id, model)

    @ResponseStatus(HttpStatus.NO_CONTENT)
    @PreAuthorize("@keycloakService.isAdminOrUserOwnerOf(@smartNoteRepository, #id)")
    @DeleteMapping("/{id}")
    fun delete(
        @PathVariable id: String,
    ) = smartNoteService.delete(id)

    @ResponseStatus(HttpStatus.CREATED)
    @PostMapping("/media/upload-url")
    fun getUploadUrl(
        @Valid @RequestBody model: StorageObjectUploadUrlRequest,
    ): StorageObjectResponse = smartNoteService.getMediaUploadUrl(model)

}