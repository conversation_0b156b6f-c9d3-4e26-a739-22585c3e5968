package com.myprogressguru.storageutils.web.controller

import com.myprogressguru.storageutils.service.StorageService
import com.myprogressguru.storageutils.web.payload.StorageObjectUploadRequest
import jakarta.validation.Valid
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/v1/storage-objects")
class StorageObjectController(
    private val storageService: StorageService
) {

    @PatchMapping("/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun upload(@PathVariable id: String,
               @Valid @RequestBody model: StorageObjectUploadRequest
    ) = storageService.upload(id)
}