package com.myprogressguru.storageutils.service

import com.myprogressguru.storageutils.service.model.StorageObjectResponse

interface StorageService {

    fun getPresignedUploadUrl(key: String): StorageObjectResponse

    fun deleteIrrelevantObjects()

    fun upload(vararg ids: String)

    fun assign(vararg ids: String)

    fun getUrlAndAssign(id: String): String

    fun deleteAllByUrl(url: String)

    fun deleteAllByUrlIn(urls: List<String>)

}