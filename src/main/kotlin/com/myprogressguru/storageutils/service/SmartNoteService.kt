package com.myprogressguru.storageutils.service

import com.myprogressguru.storageutils.entity.SmartNote
import com.myprogressguru.storageutils.service.model.StorageObjectResponse
import com.myprogressguru.storageutils.web.payload.StorageObjectUploadUrlRequest
import com.myprogressguru.storageutils.web.payload.notes.SmartNoteCreateRequest
import com.myprogressguru.storageutils.web.payload.notes.SmartNoteEditRequest

interface SmartNoteService {

    fun create(model: SmartNoteCreateRequest): SmartNote

    fun edit(id: String, model: SmartNoteEditRequest)

    fun delete(id: String)

    fun getMediaUploadUrl(model: StorageObjectUploadUrlRequest): StorageObjectResponse
}