package com.myprogressguru.storageutils.service.impl

import com.myprogressguru.storageutils.entity.StorageObject
import com.myprogressguru.storageutils.exception.InvalidStorageObjectException
import com.myprogressguru.storageutils.repository.StorageObjectRepository
import com.myprogressguru.storageutils.service.StorageService
import com.myprogressguru.storageutils.service.model.StorageObjectResponse
import jakarta.transaction.Transactional
import mu.KotlinLogging
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.repository.findByIdOrNull
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.Delete
import software.amazon.awssdk.services.s3.model.DeleteObjectsRequest
import software.amazon.awssdk.services.s3.model.ObjectIdentifier
import software.amazon.awssdk.services.s3.model.PutObjectRequest
import software.amazon.awssdk.services.s3.presigner.S3Presigner
import software.amazon.awssdk.services.s3.presigner.model.PutObjectPresignRequest
import java.time.Duration

@Service
class SimpleStorageService(
    @Value("\${aws.cdn.bucket.name}")
    private val awsCdnBucketName: String,
    @Value("\${aws.cdn.bucket.domain}")
    private val awsCdnBucketDomain: String,
    private val s3Presigner: S3Presigner,
    private val s3Client: S3Client,
    private val storageObjectRepository: StorageObjectRepository
) : StorageService {

    private val logger = KotlinLogging.logger {}

    override fun getPresignedUploadUrl(key: String): StorageObjectResponse {
        val putObjectRequest = PutObjectRequest.builder()
            .bucket(awsCdnBucketName)
            .key(key)
            .build()

        val putObjectPresignRequest = PutObjectPresignRequest.builder()
            .putObjectRequest(putObjectRequest)
            .signatureDuration(Duration.ofMinutes(5))
            .build()
        val presignedPutObjectRequest = s3Presigner.presignPutObject(putObjectPresignRequest)

        val storageObject = storageObjectRepository.save(
            StorageObject(
                key = key,
                url = "$awsCdnBucketDomain/$key"
            )
        )

        return StorageObjectResponse(
            id = storageObject.id!!,
            url = storageObject.url,
            presignedUploadUrl = presignedPutObjectRequest.url().toString()
        )
    }

    @Transactional
    @Scheduled(cron = "0 0 13 * * *")
    @SchedulerLock(name = "delete-irrelevant-storage-objects")
    override fun deleteIrrelevantObjects() {
        logger.info { "Deleting irrelevant objects scheduled task started." }

        val irrelevantObjects =
            storageObjectRepository.findAllByIsAssignedIsFalseOrIsUploadedIsFalse()
        val uploadedCount = irrelevantObjects.count { it.isUploaded }
        logger.info { "${irrelevantObjects.size} irrelevant objects found ($uploadedCount uploaded)." }


        deleteAll(irrelevantObjects)
        logger.info { "Deleting irrelevant objects scheduled task finished." }
    }

    override fun upload(vararg ids: String) {
        val storageObjects = storageObjectRepository.findAllById(ids.toList())
        storageObjects.onEach { it.isUploaded = true }

        storageObjectRepository.saveAll(storageObjects)
    }

    override fun assign(vararg ids: String) {
        val storageObjects = storageObjectRepository.findAllById(ids.toList())
        storageObjects.onEach { it.isAssigned = true }

        storageObjectRepository.saveAll(storageObjects)
    }

    override fun getUrlAndAssign(id: String): String {
        val storageObject = storageObjectRepository.findByIdOrNull(id) ?: throw InvalidStorageObjectException(id)
        storageObject.isAssigned = true

        return storageObjectRepository.save(storageObject).url
    }

    @Transactional
    override fun deleteAllByUrl(url: String) {
        val storageObjects = storageObjectRepository.findAllByUrl(url)

        deleteAll(storageObjects)
    }

    @Transactional
    override fun deleteAllByUrlIn(urls: List<String>) {
        val storageObjects = storageObjectRepository.findAllByUrlIn(urls)

        deleteAll(storageObjects)
    }

    private fun deleteAll(storageObjects: List<StorageObject>) {
        val uploadedObjectsIdentifiers =
            storageObjects
                .filter { it.isUploaded }
                .map { ObjectIdentifier.builder().key(it.key).build() }
        if (uploadedObjectsIdentifiers.isNotEmpty()) {
            val deleteObjectsRequest = DeleteObjectsRequest.builder()
                .delete(Delete.builder().objects(uploadedObjectsIdentifiers).build())
                .bucket(awsCdnBucketName)
                .build()

            s3Client.deleteObjects(deleteObjectsRequest)
        }

        storageObjectRepository.deleteAll(storageObjects)
    }
}