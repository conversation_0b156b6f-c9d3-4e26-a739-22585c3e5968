package com.myprogressguru.storageutils.service.impl

import com.myprogressguru.commonutils.service.DateService
import com.myprogressguru.keycloakutils.service.KeycloakUtilsService
import com.myprogressguru.storageutils.entity.SmartNote
import com.myprogressguru.storageutils.enumeration.SmartNoteType
import com.myprogressguru.storageutils.exception.InvalidSmartNoteException
import com.myprogressguru.storageutils.exception.InvalidSmartNoteRequestException
import com.myprogressguru.storageutils.repository.SmartNoteRepository
import com.myprogressguru.storageutils.service.SmartNoteService
import com.myprogressguru.storageutils.service.StorageService
import com.myprogressguru.storageutils.service.model.StorageObjectResponse
import com.myprogressguru.storageutils.web.payload.StorageObjectUploadUrlRequest
import com.myprogressguru.storageutils.web.payload.notes.SmartNoteCreateRequest
import com.myprogressguru.storageutils.web.payload.notes.SmartNoteEditRequest
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import java.util.*

@Service
class SmartNoteServiceImpl(
    private val smartNoteRepository: SmartNoteRepository,
    private val dateService: DateService,
    private val storageService: StorageService,
    private val keycloakService: KeycloakUtilsService,
) : SmartNoteService {

    override fun create(model: SmartNoteCreateRequest): SmartNote {
        validate(model)

        val creator = keycloakService.getRequesterUserDetails()!!
        val creatorName = "${creator.firstName} ${creator.lastName}"

        val smartNote = if (model.type == SmartNoteType.TEXT) {
            SmartNote(
                type = SmartNoteType.TEXT,
                content = model.content!!,
                createdOn = dateService.getCurrentDateTime(),
                creatorId = creator.id,
                creatorName = creatorName,
            )
        } else {
            SmartNote(
                type = model.type!!,
                mediaUrl = storageService.getUrlAndAssign(model.mediaId!!),
                createdOn = dateService.getCurrentDateTime(),
                creatorId = creator.id,
                creatorName = creatorName,
            )
        }

        return smartNoteRepository.save(smartNote)
    }

    override fun edit(id: String, model: SmartNoteEditRequest) {
        val smartNote = getSmartNote(id)

        if (smartNote.type != SmartNoteType.TEXT) {
            throw InvalidSmartNoteRequestException("Only text type notes can be edited.")
        }

        smartNote.content = model.content

        smartNoteRepository.save(smartNote)
    }

    override fun delete(id: String) {
        val note = getSmartNote(id)

        if (note.mediaUrl != null) {
            storageService.deleteAllByUrl(note.mediaUrl!!)
        }

        smartNoteRepository.delete(note)
    }

    override fun getMediaUploadUrl(
        model: StorageObjectUploadUrlRequest
    ): StorageObjectResponse {
        val creatorId = keycloakService.getRequesterId()!!

        val key =
            "trainees/$creatorId/smart-notes/${dateService.getCurrentDate()}/${UUID.randomUUID()}.${model.fileExtension}"

        return storageService.getPresignedUploadUrl(key)
    }

    private fun getSmartNote(id: String) =
        smartNoteRepository.findByIdOrNull(id) ?: throw InvalidSmartNoteException(id)

    private fun validate(model: SmartNoteCreateRequest) {
        if (model.type == SmartNoteType.TEXT && model.content.isNullOrBlank()) {
            throw InvalidSmartNoteException("Content cannot be blank for text type.")
        }

        if (model.mediaId.isNullOrBlank()) {
            throw InvalidSmartNoteException("Media ID cannot be blank.")
        }
    }
}