package com.myprogressguru.storageutils.entity

import com.myprogressguru.keycloakutils.service.model.UserIdResource
import com.myprogressguru.storageutils.enumeration.SmartNoteType
import jakarta.persistence.*
import org.hibernate.annotations.GenericGenerator
import java.time.ZonedDateTime

@Entity(name = "smart_notes")
data class SmartNote(

    @Id
    @GeneratedValue(generator = "uuid-string")
    @GenericGenerator(name = "uuid-string", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(nullable = false, unique = true, updatable = false)
    var id: String? = null,

    @Enumerated(EnumType.STRING)
    var type: SmartNoteType,

    @Column(nullable = false)
    var createdOn: ZonedDateTime,

    @Column(nullable = false)
    var creatorId: String,

    @Column(nullable = false)
    var creatorName: String,

    @Column(columnDefinition = "TEXT")
    var content: String? = null,

    var mediaUrl: String? = null

) : UserIdResource {

    override var userId: String
        get() = creatorId
        set(value) {
            creatorId = value
        }
}