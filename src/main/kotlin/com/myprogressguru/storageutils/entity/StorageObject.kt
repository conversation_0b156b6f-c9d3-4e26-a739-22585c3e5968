package com.myprogressguru.storageutils.entity

import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.Id
import org.hibernate.annotations.GenericGenerator

@Entity(name = "storage_objects")
data class StorageObject(

    @Id
    @GeneratedValue(generator = "uuid-string")
    @GenericGenerator(name = "uuid-string", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(nullable = false, unique = true, updatable = false)
    var id: String? = null,

    @Column(nullable = false)
    var key: String,

    @Column(nullable = false)
    var url: String,

    @Column(nullable = false)
    @get:JvmName("getIsAssigned")
    @set:JvmName("setIsAssigned")
    var isAssigned: Boolean = false,

    @Column(nullable = false)
    @get:JvmName("getIsUploaded")
    @set:JvmName("setIsUploaded")
    var isUploaded: Boolean = false,
)