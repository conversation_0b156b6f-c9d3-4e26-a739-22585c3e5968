package com.myprogressguru.storageutils.config

import com.myprogressguru.keycloakutils.config.KeycloakConfig
import com.myprogressguru.storageutils.service.impl.SimpleStorageService
import com.myprogressguru.storageutils.service.impl.SmartNoteServiceImpl
import net.javacrumbs.shedlock.core.LockProvider
import net.javacrumbs.shedlock.provider.jdbctemplate.JdbcTemplateLockProvider
import net.javacrumbs.shedlock.spring.annotation.EnableSchedulerLock
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Import
import org.springframework.scheduling.annotation.EnableScheduling
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.presigner.S3Presigner
import javax.sql.DataSource


@EnableScheduling
@EnableSchedulerLock(defaultLockAtMostFor = "10m")
@Import(SimpleStorageService::class, SmartNoteServiceImpl::class, KeycloakConfig::class)
@Configuration
class StorageUtilsBeanConfig(
    @Value("\${aws.region}")
    private val awsRegion: String,
) {

    @Bean
    fun s3Presigner(): S3Presigner = S3Presigner.builder().region(Region.of(awsRegion)).build()

    @Bean
    fun S3Client(): S3Client = S3Client.builder().region(Region.of(awsRegion)).build()

    @Bean
    fun lockProvider(dataSource: DataSource): LockProvider = JdbcTemplateLockProvider(dataSource)
}