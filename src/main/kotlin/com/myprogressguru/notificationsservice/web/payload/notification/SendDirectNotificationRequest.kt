package com.myprogressguru.notificationsservice.web.payload.notification

import jakarta.validation.constraints.NotBlank

data class SendDirectNotificationRequest(

    val userIds: List<String>?,

    @field:NotBlank
    val title: String?,

    @field:NotBlank
    val body: String?,

    val photoId: String?,

    val soundId: String?,

    val showConfetti: Boolean?,

    val videoUrl: String?,

    val data: Map<String, String>?,

    val onlyPush: Boolean = false

)
