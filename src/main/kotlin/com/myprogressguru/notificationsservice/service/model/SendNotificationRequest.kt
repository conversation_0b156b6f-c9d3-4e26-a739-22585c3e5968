package com.myprogressguru.notificationsservice.service.model

import jakarta.validation.constraints.NotBlank

data class SendNotificationRequest(

    @field:NotBlank
    val userId: String?,

    @field:NotBlank
    val type: String?,

    @field:NotBlank
    val title: String?,

    @field:NotBlank
    val body: String?,

    val data: Map<String, String>? = null,

    val onlyPush: Boolean = false

)
