package com.myprogressguru.notificationsservice.service

import com.myprogressguru.storageutils.service.model.StorageObjectResponse
import com.myprogressguru.storageutils.web.payload.StorageObjectUploadUrlRequest

interface FileUploadService {

    fun getPhotoUploadUrl(model: StorageObjectUploadUrlRequest): StorageObjectResponse

    fun getSoundUploadUrl(model: StorageObjectUploadUrlRequest): StorageObjectResponse

}