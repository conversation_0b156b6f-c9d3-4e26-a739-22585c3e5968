package com.myprogressguru.notificationsservice.service

import com.myprogressguru.notificationsservice.service.model.SendNotificationRequest
import com.myprogressguru.notificationsservice.web.payload.notification.GetAllNotificationsRequest
import com.myprogressguru.notificationsservice.web.payload.notification.NotificationResponse
import com.myprogressguru.notificationsservice.web.payload.notification.SendDirectNotificationRequest
import org.springframework.data.domain.Page

interface NotificationService {

    fun handleMessage(request: SendNotificationRequest)

    fun getAll(model: GetAllNotificationsRequest): Page<NotificationResponse>

    fun read(id: String)

    fun readAll(userId: String)

    fun send(model: SendDirectNotificationRequest)

}