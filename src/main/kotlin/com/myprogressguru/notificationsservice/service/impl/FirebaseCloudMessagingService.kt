package com.myprogressguru.notificationsservice.service.impl

import com.google.firebase.messaging.*
import com.myprogressguru.notificationsservice.service.PushNotificationService
import com.myprogressguru.notificationsservice.service.TokenService
import com.myprogressguru.notificationsservice.service.model.SendNotificationRequest
import com.myprogressguru.notificationsservice.service.model.TokenModel
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Service

@Service
class FirebaseCloudMessagingService(
    private val firebaseMessaging: FirebaseMessaging,
    private val tokenService: TokenService
) : PushNotificationService {

    private val logger = KotlinLogging.logger {}

    override fun send(model: SendNotificationRequest) {
        val tokens = tokenService.getAll(model.userId!!)
        if (tokens.isEmpty()) {
            return
        }

        tokens.map { it to buildMessage(it.token, model) }
            .forEach {
                sendMessage(it.first, it.second)
            }
    }

    private fun sendMessage(token: TokenModel, message: Message) {
        try {
            firebaseMessaging.send(message)
        } catch (e: FirebaseMessagingException) {
            if (e.messagingErrorCode == MessagingErrorCode.INVALID_ARGUMENT) {
                logger.info { "Deleting token ${token.token} with userId ${token.userId} due to Google API Bad Request." }
                tokenService.delete(token.token)
                return
            }

            if (e.messagingErrorCode == MessagingErrorCode.UNREGISTERED) {
                logger.info { "Deleting token ${token.token} with userId ${token.userId} due to expired token." }
                tokenService.delete(token.token)
                return
            }

            logger.error { e.stackTraceToString() }
        }
    }

    private fun buildMessage(token: String, model: SendNotificationRequest) = Message.builder()
        .setToken(token)
        .setWebpushConfig(
            WebpushConfig.builder()
                .setNotification(
                    WebpushNotification.builder()
                        .setIcon("https://myprogressguru.com/assets/icon/favicon.png")
                        .setTitle(model.title)
                        .setBody(model.body).build()
                ).build()
        ).build()
}