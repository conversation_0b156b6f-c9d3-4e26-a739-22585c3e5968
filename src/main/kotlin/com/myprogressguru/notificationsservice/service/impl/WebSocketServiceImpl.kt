package com.myprogressguru.notificationsservice.service.impl

import com.myprogressguru.cloudutils.service.QueueService
import com.myprogressguru.notificationsservice.service.WebSocketService
import com.myprogressguru.notificationsservice.service.model.SendWebSocketMessageRequest
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service

@Service
class WebSocketServiceImpl(
    private val queueService: QueueService,
    @Value("\${mpg.rtc-service.queue-url}")
    private val wsMessagesQueueUrl: String
) : WebSocketService {

    override fun <T> send(model: SendWebSocketMessageRequest<T>) {
        queueService.send(wsMessagesQueueUrl, model)
    }
}