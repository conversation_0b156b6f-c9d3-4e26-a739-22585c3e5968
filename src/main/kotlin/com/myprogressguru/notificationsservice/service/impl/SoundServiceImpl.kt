package com.myprogressguru.notificationsservice.service.impl

import com.myprogressguru.notificationsservice.entity.Sound
import com.myprogressguru.notificationsservice.exception.DuplicateSoundNameException
import com.myprogressguru.notificationsservice.exception.InvalidSoundException
import com.myprogressguru.notificationsservice.repository.SoundRepository
import com.myprogressguru.notificationsservice.service.NotificationsMapper
import com.myprogressguru.notificationsservice.service.SoundService
import com.myprogressguru.notificationsservice.web.payload.sound.SoundCreateRequest
import com.myprogressguru.notificationsservice.web.payload.sound.SoundEditRequest
import com.myprogressguru.notificationsservice.web.payload.sound.SoundResponse
import com.myprogressguru.storageutils.service.StorageService
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service

@Service
class SoundServiceImpl(
    private val soundRepository: SoundRepository,
    private val storageService: StorageService,
    private val notificationsMapper: NotificationsMapper
) : SoundService {

    override fun create(model: SoundCreateRequest): SoundResponse {
        if (soundRepository.existsByName(model.name!!)) {
            throw DuplicateSoundNameException()
        }

        val sound = Sound(
            name = model.name,
            url = storageService.getUrlAndAssign(model.soundId!!)
        )
        soundRepository.save(sound)

        return notificationsMapper.toSoundResponse(sound)
    }

    override fun getAll(): List<SoundResponse> =
        notificationsMapper.toSoundResponseList(soundRepository.findAllByOrderByName())

    override fun delete(id: String) {
        val sound = getSound(id)

        storageService.deleteAllByUrl(sound.url)

        soundRepository.delete(sound)
    }

    override fun edit(id: String, model: SoundEditRequest) {
        val sound = getSound(id)
        sound.name = model.name!!

        soundRepository.save(sound)
    }

    override fun getUrl(id: String): String = getSound(id).url

    private fun getSound(id: String): Sound = soundRepository.findByIdOrNull(id) ?: throw InvalidSoundException(id)
}