package com.myprogressguru.trainingservice.service

import com.myprogressguru.trainingservice.entity.Trainee
import com.myprogressguru.trainingservice.entity.TrainingSchedule
import com.myprogressguru.trainingservice.web.payload.training.schedule.TrainingScheduleRequest
import com.myprogressguru.trainingservice.web.payload.training.schedule.TrainingScheduleResponse
import java.time.LocalDate

interface TrainingScheduleStrategy {

    fun create(model: TrainingScheduleRequest, trainee: Trainee): TrainingSchedule

    fun toTrainingScheduleResponse(trainingSchedule: TrainingSchedule): TrainingScheduleResponse

    fun getNextWorkoutDate(
        trainingSchedule: TrainingSchedule,
        workoutHistory: List<LocalDate>,
        currentDate: LocalDate
    ): LocalDate
}