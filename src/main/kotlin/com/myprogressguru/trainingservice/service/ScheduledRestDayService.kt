package com.myprogressguru.trainingservice.service

import com.myprogressguru.trainingservice.web.payload.training.schedule.RestDaysScheduleResponse
import com.myprogressguru.trainingservice.web.payload.training.schedule.ScheduledRestDayCreateRequest
import com.myprogressguru.trainingservice.web.payload.training.schedule.ScheduledRestDayEditRequest
import com.myprogressguru.trainingservice.web.payload.training.schedule.ScheduledRestDayResponse
import java.time.LocalDate

interface ScheduledRestDayService {

    fun create(model: ScheduledRestDayCreateRequest): ScheduledRestDayResponse

    fun delete(id: String)

    fun edit(id: String, model: ScheduledRestDayEditRequest)

    fun getSchedule(traineeId: String): RestDaysScheduleResponse

    fun getRestDays(traineeId: String): List<LocalDate>

    fun getScheduledRestDaysMap(traineeIds: List<String>, date: LocalDate): Map<String, ScheduledRestDayResponse>

}