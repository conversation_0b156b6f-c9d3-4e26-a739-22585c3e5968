package com.myprogressguru.trainingservice.service.rest.client

import com.fasterxml.jackson.annotation.JsonInclude
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.service.annotation.GetExchange
import java.io.Serializable
import java.time.LocalDate
import java.time.LocalTime

interface NutritionServiceClient {

    @GetExchange("/meal-diaries")
    fun getAllMealDiaries(
        @RequestParam(required = true) traineeId: String,
        @RequestParam(required = true) startDate: String,
        @RequestParam(required = true) endDate: String
    ): List<MealDiaryResponse>
}

data class MealDiaryResponse(

    val id: String,

    val date: LocalDate,

    val mealRecords: List<MealRecordResponse>,

    val nutritionInfo: NutritionInfoResponse

)

data class MealRecordResponse(

    val id: String,

    val orderNumber: Int,

    val time: LocalTime? = null,

    val foodRecords: List<FoodRecordResponse>,

    val nutritionInfo: NutritionInfoResponse

)

data class FoodRecordResponse(

    val id: String,

    val name: String,

    val foodId: String?,

    @get:JvmName("getIsExternal")
    val isExternal: Boolean = false,

    @get:JvmName("getIsQuickAdded")
    val isQuickAdded: Boolean = false,

    val metricServingAmount: Double,

    val metricServingUnit: String?,

    val nutritionInfo: NutritionInfoResponse

)

@JsonInclude(JsonInclude.Include.NON_NULL)
data class NutritionInfoResponse(

    val calories: Int,

    val protein: Double,

    val carbs: Double,

    val fat: Double,

    val saturatedFat: Double? = null,

    val monounsaturatedFat: Double? = null,

    val polyunsaturatedFat: Double? = null,

    val transFat: Double? = null,

    val sugar: Double? = null,

    val fiber: Double? = null,

    val glycemicIndex: String? = null,

    val cholesterol: Double? = null,

    val sodium: Double? = null,

    val potassium: Double? = null,

    val iron: Double? = null,

    val calcium: Double? = null,

    val vitaminA: Double? = null,

    val vitaminB: Double? = null,

    val vitaminC: Double? = null

) : Serializable