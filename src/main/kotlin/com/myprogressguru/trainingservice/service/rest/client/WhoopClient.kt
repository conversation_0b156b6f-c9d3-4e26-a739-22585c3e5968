package com.myprogressguru.trainingservice.service.rest.client

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import org.springframework.http.MediaType.APPLICATION_FORM_URLENCODED_VALUE
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestHeader
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.service.annotation.GetExchange
import org.springframework.web.service.annotation.PostExchange

interface WhoopClient {

    @PostExchange("/oauth/oauth2/token", contentType = APPLICATION_FORM_URLENCODED_VALUE)
    fun getToken(@RequestParam request: Map<String, *>): TokenResponse

    @GetExchange("/developer/v1/user/profile/basic")
    fun getUserProfile(@RequestHeader("Authorization") authorization: String): ProfileResponse

    @GetExchange("/developer/v1/activity/sleep/{sleepId}")
    fun getSleep(@PathVariable sleepId: String, @RequestHeader("Authorization") authorization: String): SleepResponse

    @GetExchange("/developer/v1/cycle/{cycleId}/recovery")
    fun getRecovery(
        @PathVariable cycleId: String,
        @RequestHeader("Authorization") authorization: String
    ): RecoveryResponse
}


@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class TokenResponse(
    val accessToken: String,
    val tokenType: String,
    val expiresIn: Int,
    val refreshToken: String,
    val scope: String
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class ProfileResponse(
    val userId: String,
    val email: String,
    val firstName: String,
    val lastName: String
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class SleepResponse(
    val id: Int,
    val userId: Int,
    val createdAt: String,
    val updatedAt: String,
    val start: String,
    val end: String,
    val timezoneOffset: String,
    val nap: Boolean,
    val scoreState: String,
    val score: SleepScore
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class SleepScore(
    val stageSummary: StageSummary,
    val sleepNeeded: SleepNeeded,
    val respiratoryRate: Double,
    val sleepPerformancePercentage: Double,
    val sleepConsistencyPercentage: Double,
    val sleepEfficiencyPercentage: Double
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class StageSummary(
    val totalInBedTimeMilli: Long,
    val totalAwakeTimeMilli: Long,
    val totalNoDataTimeMilli: Long,
    val totalLightSleepTimeMilli: Long,
    val totalSlowWaveSleepTimeMilli: Long,
    val totalRemSleepTimeMilli: Long,
    val sleepCycleCount: Int,
    val disturbanceCount: Int
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class SleepNeeded(
    val baselineMilli: Long,
    val needFromSleepDebtMilli: Long,
    val needFromRecentStrainMilli: Long,
    val needFromRecentNapMilli: Long
)


@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class RecoveryResponse(
    val cycleId: Int,
    val sleepId: Int,
    val userId: Int,
    val createdAt: String,
    val updatedAt: String,
    val scoreState: String,
    val score: RecoveryScore
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class RecoveryScore(
    val userCalibrating: Boolean,
    val recoveryScore: Double,
    val restingHeartRate: Int,
    val hrvRmssdMilli: Double,
    val spo2Percentage: Double,
    val skinTempCelsius: Double
)