package com.myprogressguru.trainingservice.service

import com.myprogressguru.keycloakutils.service.model.CreateUserModel
import com.myprogressguru.storageutils.entity.SmartNote
import com.myprogressguru.storageutils.web.payload.notes.SmartNoteResponse
import com.myprogressguru.trainingservice.entity.*
import com.myprogressguru.trainingservice.entity.projection.calculateScore
import com.myprogressguru.trainingservice.entity.projection.convertSetRecordsToText
import com.myprogressguru.trainingservice.web.payload.body.measurement.BodyMeasurementRecordPutRequest
import com.myprogressguru.trainingservice.web.payload.body.measurement.BodyMeasurementRecordResponse
import com.myprogressguru.trainingservice.web.payload.body.photo.BodyPhotoResponse
import com.myprogressguru.trainingservice.web.payload.course.CourseResponse
import com.myprogressguru.trainingservice.web.payload.creatine.CreatineRecordResponse
import com.myprogressguru.trainingservice.web.payload.exercise.ExerciseCreateRequest
import com.myprogressguru.trainingservice.web.payload.exercise.ExerciseResponse
import com.myprogressguru.trainingservice.web.payload.exercise.TrainingVolumeResponse
import com.myprogressguru.trainingservice.web.payload.gym.GymResponse
import com.myprogressguru.trainingservice.web.payload.integration.IntegrationResponse
import com.myprogressguru.trainingservice.web.payload.note.NoteResponse
import com.myprogressguru.trainingservice.web.payload.sleep.SleepQualityRecordResponse
import com.myprogressguru.trainingservice.web.payload.trainee.*
import com.myprogressguru.trainingservice.web.payload.trainee.group.TraineeGroupResponse
import com.myprogressguru.trainingservice.web.payload.training.schedule.ScheduledRestDayResponse
import com.myprogressguru.trainingservice.web.payload.training.schedule.TrainingScheduleResponse
import com.myprogressguru.trainingservice.web.payload.training.schedule.TrainingTimeScheduleResponse
import com.myprogressguru.trainingservice.web.payload.training.split.TrainingSplitResponse
import com.myprogressguru.trainingservice.web.payload.training.workout.*
import com.myprogressguru.trainingservice.web.payload.weight.BodyWeightRecordResponse
import org.mapstruct.*
import java.time.ZonedDateTime

@JvmDefaultWithCompatibility
@Mapper(
    componentModel = "spring",
    injectionStrategy = InjectionStrategy.CONSTRUCTOR,
    unmappedTargetPolicy = ReportingPolicy.IGNORE
)
interface TrainingMapper {

    fun toCreateUserModel(model: TraineeCreateRequest, trainerId: String?): CreateUserModel

    @Mapping(target = "id", source = "id")
    @Mapping(target = "trainer", source = "trainer")
    fun toTrainee(
        model: TraineeCreateRequest,
        id: String,
        trainer: Trainer,
        groups: List<TraineeGroup>
    ): Trainee

    @Mapping(target = "id", ignore = true)
    fun toFitnessState(
        model: FitnessStateCreateRequest,
        trainee: Trainee,
        createdOn: ZonedDateTime
    ): FitnessState

    fun toTraineeResponse(trainee: Trainee): TraineeResponse

    fun toTraineeResponseList(trainees: List<Trainee>): List<TraineeResponse>

    fun toFitnessStateResponse(fitnessState: FitnessState): FitnessStateResponse

    fun toExerciseResponse(exercise: Exercise): ExerciseResponse

    fun toExerciseResponseList(exercises: List<Exercise>): List<ExerciseResponse>

    @Mapping(target = "traineeId", source = "trainingSplit.trainee.id")
    @Mapping(target = "trainerId", source = "trainingSplit.trainer.id")
    fun toTrainingSplitResponse(trainingSplit: TrainingSplit): TrainingSplitResponse

    @Mapping(target = "traineeId", source = "trainingSplit.trainee.id")
    @Mapping(target = "trainerId", source = "trainingSplit.trainer.id")
    @Mapping(target = "workouts", source = "workouts")
    fun toTrainingSplitResponse(trainingSplit: TrainingSplit, workouts: List<WorkoutResponse>): TrainingSplitResponse

    @AfterMapping
    fun afterMapping(
        trainingSplit: TrainingSplit,
        @MappingTarget trainingSplitResponse: TrainingSplitResponse
    ) {
        if (trainingSplitResponse.workouts.isEmpty()) {
            return
        }

        trainingSplitResponse.trainingVolume = trainingSplitResponse.workouts.mapNotNull { it.trainingVolume }
            .reduceRightOrNull { trainingVolume, acc -> acc + trainingVolume }
    }

    fun toTrainingSplitResponseList(trainingSplits: Iterable<TrainingSplit>): List<TrainingSplitResponse>

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "workout", source = "workout")
    @Mapping(target = "exercise", source = "exercise")
    fun toWorkoutExercise(model: WorkoutExerciseCreateRequest, workout: Workout, exercise: Exercise): WorkoutExercise

    @Mapping(target = "trainingSplitId", source = "workout.trainingSplit.id")
    fun toWorkoutResponse(workout: Workout): WorkoutResponse

    @AfterMapping
    fun afterMapping(
        workout: Workout,
        @MappingTarget workoutResponse: WorkoutResponse
    ) {
        if (workoutResponse.exercises.isEmpty()) {
            return
        }

        workoutResponse.trainingVolume = workoutResponse.exercises.map { it.exercise.trainingVolume * it.sets }
            .reduceRight { trainingVolume, acc -> acc + trainingVolume }
    }

    fun toWorkoutInfoResponse(workout: Workout): WorkoutInfoResponse

    fun toWorkoutResponseList(workouts: Iterable<Workout>): List<WorkoutResponse>

    fun toWorkoutExerciseResponse(workoutExercise: WorkoutExercise): WorkoutExerciseResponse

    @AfterMapping
    fun afterMapping(
        workoutExercise: WorkoutExercise,
        @MappingTarget workoutExerciseResponse: WorkoutExerciseResponse
    ) {
        workoutExerciseResponse.trainingVolume =
            workoutExerciseResponse.exercise.trainingVolume * workoutExerciseResponse.sets
    }

    fun toBodyWeightRecordResponse(bodyWeightRecord: BodyWeightRecord): BodyWeightRecordResponse

    fun toBodyWeightRecordResponseList(bodyWeightRecords: List<BodyWeightRecord>): List<BodyWeightRecordResponse>

    fun toWorkoutRecordResponse(workoutRecord: WorkoutRecord): WorkoutRecordResponse

    @AfterMapping
    fun afterMapping(
        workoutRecord: WorkoutRecord,
        @MappingTarget workoutRecordResponse: WorkoutRecordResponse
    ) {
        if (workoutRecordResponse.exerciseRecords.isEmpty()) {
            return
        }

        workoutRecordResponse.trainingVolume =
            workoutRecordResponse.exerciseRecords.map { it.exercise.exercise.trainingVolume * it.setRecords.size }
                .reduceRight { trainingVolume, acc -> acc + trainingVolume }
    }

    fun toWorkoutRecordInfoResponse(workoutRecord: WorkoutRecord): WorkoutRecordInfoResponse

    fun toWorkoutRecordInfoResponse(workoutRecordResponse: WorkoutRecordResponse): WorkoutRecordInfoResponse

    fun map(model: ExerciseCreateRequest, @MappingTarget exercise: Exercise)

    @Mapping(target = "patternSchedule.pattern", source = "pattern")
    fun toPatternTrainingScheduleResponse(trainingSchedule: TrainingSchedule): TrainingScheduleResponse

    @Mapping(target = "daysOfTheWeekSchedule.monday", source = "monday")
    @Mapping(target = "daysOfTheWeekSchedule.tuesday", source = "tuesday")
    @Mapping(target = "daysOfTheWeekSchedule.wednesday", source = "wednesday")
    @Mapping(target = "daysOfTheWeekSchedule.thursday", source = "thursday")
    @Mapping(target = "daysOfTheWeekSchedule.friday", source = "friday")
    @Mapping(target = "daysOfTheWeekSchedule.saturday", source = "saturday")
    @Mapping(target = "daysOfTheWeekSchedule.sunday", source = "sunday")
    fun toDaysOfTheWeekTrainingScheduleResponse(trainingSchedule: TrainingSchedule): TrainingScheduleResponse

    fun toScheduledRestDayResponse(scheduledRestDay: ScheduledRestDay): ScheduledRestDayResponse

    fun toScheduledRestDayResponseList(scheduledRestDayList: List<ScheduledRestDay>): List<ScheduledRestDayResponse>

    fun toTrainingTimeScheduleResponse(trainingTimeSchedule: TrainingTimeSchedule): TrainingTimeScheduleResponse

    fun toExercise(model: ExerciseCreateRequest): Exercise

    @Mapping(target = "workoutRecord.date", source = "workoutRecord.startedOn")
    fun toWorkoutExerciseRecordResponse(workoutExerciseRecord: WorkoutExerciseRecord): WorkoutExerciseRecordResponse

    @Mapping(target = "workoutRecord.date", source = "workoutRecord.startedOn")
    fun toWorkoutExerciseRecordInfoResponse(workoutExerciseRecord: WorkoutExerciseRecord): WorkoutExerciseRecordInfoResponse

    @AfterMapping
    fun afterMapping(
        workoutExerciseRecord: WorkoutExerciseRecord,
        @MappingTarget workoutExerciseRecordResponse: WorkoutExerciseRecordResponse
    ) {
        workoutExerciseRecordResponse.setRecordsAsText =
            workoutExerciseRecordResponse.setRecords.convertSetRecordsToText()
        workoutExerciseRecordResponse.score = workoutExerciseRecordResponse.setRecords.calculateScore()
    }

    fun toWorkoutExerciseSetRecordResponse(workoutExerciseSetRecord: WorkoutExerciseSetRecord): WorkoutExerciseSetRecordResponse

    fun toWorkoutExerciseSetRecordResponseList(workoutExerciseSetRecords: List<WorkoutExerciseSetRecord>?): List<WorkoutExerciseSetRecordResponse>

    fun toWorkoutExerciseRecordHistoryResponse(workoutExerciseRecord: WorkoutExerciseRecord?): WorkoutExerciseRecordHistoryResponse?

    @AfterMapping
    fun afterMapping(
        workoutExerciseRecord: WorkoutExerciseRecord,
        @MappingTarget workoutExerciseRecordHistoryResponse: WorkoutExerciseRecordHistoryResponse
    ) {
        workoutExerciseRecordHistoryResponse.apply {
            setRecordsAsText =
                workoutExerciseRecord.setRecords.convertSetRecordsToText()
            hasVideo = workoutExerciseRecord.setRecords.any { it.video != null }
        }

    }

    fun toTrainingVolumeResponse(trainingVolume: TrainingVolume): TrainingVolumeResponse

    fun toNoteResponse(note: Note): NoteResponse

    fun toSmartNoteResponse(smartNote: SmartNote): SmartNoteResponse

    @Mapping(target = "id", source = "note.id")
    @Mapping(target = "content", source = "note.content")
    @Mapping(target = "createdOn", source = "note.createdOn")
    @Mapping(target = "trainee", source = "note.trainee")
    fun toNoteResponse(exerciseNote: ExerciseNote): NoteResponse

    fun toNoteResponseList(notes: List<ExerciseNote>): List<NoteResponse>

    fun toBodyPhotoResponse(bodyPhoto: BodyPhoto): BodyPhotoResponse

    fun toBodyPhotoResponseList(bodyPhotos: List<BodyPhoto>): List<BodyPhotoResponse>

    fun toSleepQualityRecordResponse(sleepQualityRecord: SleepQualityRecord): SleepQualityRecordResponse

    fun toWorkoutExerciseSetRecordVideoResponse(video: WorkoutExerciseSetRecordVideo): WorkoutExerciseSetRecordVideoResponse

    fun toTraineeGroupResponse(traineeGroup: TraineeGroup): TraineeGroupResponse

    fun toTraineeGroupResponseList(traineeGroups: List<TraineeGroup>): List<TraineeGroupResponse>

    @Mapping(target = "date", ignore = true)
    fun map(model: BodyMeasurementRecordPutRequest, @MappingTarget bodyMeasurementRecord: BodyMeasurementRecord)

    fun toBodyMeasurementRecordResponse(bodyMeasurementRecord: BodyMeasurementRecord): BodyMeasurementRecordResponse

    fun toGymResponse(gym: Gym): GymResponse

    fun toGymResponseList(gyms: List<Gym>): List<GymResponse>

    fun toCreatineRecordResponse(creatineRecord: CreatineRecord): CreatineRecordResponse

    fun toCreatineRecordResponseList(creatineRecords: List<CreatineRecord>): List<CreatineRecordResponse>

    fun toIntegrationResponse(integration: Integration): IntegrationResponse

    fun toIntegrationResponseList(integrations: List<Integration>): List<IntegrationResponse>

    @Mapping(target = "instagram", ignore = true)
    @Mapping(target = "manyChatContactId", ignore = true)
    fun map(model: TraineeCreateRequest, @MappingTarget traineeInvitation: TraineeInvitation)

    fun toTraineeCreateRequest(traineeInvitation: TraineeInvitation): TraineeCreateRequest

    fun toTraineeInvitationResponse(traineeInvitation: TraineeInvitation): TraineeInvitationResponse

    fun toTraineeInvitationResponseList(traineeInvitations: List<TraineeInvitation>): List<TraineeInvitationResponse>

    fun toCourseResponse(course: Course): CourseResponse

    fun toCourseResponseList(courses: List<Course>): List<CourseResponse>
}
