package com.myprogressguru.trainingservice.service

import com.myprogressguru.trainingservice.web.payload.trainee.group.TraineeGroupCreateRequest
import com.myprogressguru.trainingservice.web.payload.trainee.group.TraineeGroupEditRequest
import com.myprogressguru.trainingservice.web.payload.trainee.group.TraineeGroupResponse

interface TraineeGroupService {

    fun getAll(trainerId: String): List<TraineeGroupResponse>

    fun create(model: TraineeGroupCreateRequest): TraineeGroupResponse

    fun edit(id: String, model: TraineeGroupEditRequest)

    fun delete(id: String)
}