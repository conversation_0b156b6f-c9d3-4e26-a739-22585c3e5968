package com.myprogressguru.trainingservice.service

import com.myprogressguru.trainingservice.web.payload.gym.GymCreateRequest
import com.myprogressguru.trainingservice.web.payload.gym.GymEditRequest
import com.myprogressguru.trainingservice.web.payload.gym.GymResponse

interface GymService {

    fun getAll(traineeId: String): List<GymResponse>

    fun create(model: GymCreateRequest): GymResponse

    fun edit(id: String, model: GymEditRequest)

    fun delete(id: String)

}