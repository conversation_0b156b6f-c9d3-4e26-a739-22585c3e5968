package com.myprogressguru.trainingservice.service

import com.myprogressguru.trainingservice.entity.Trainee
import com.myprogressguru.trainingservice.web.payload.training.split.*
import com.myprogressguru.trainingservice.web.payload.training.workout.WorkoutResponse
import com.myprogressguru.trainingservice.web.payload.training.workout.WorkoutsOrderEditRequest

interface TrainingSplitService {

    fun get(id: String): TrainingSplitResponse

    fun getActive(traineeId: String): TrainingSplitResponse?

    fun create(model: TrainingSplitCreateRequest): TrainingSplitResponse

    fun createEmpty(trainee: Trainee): TrainingSplitResponse

    fun editWorkoutsOrder(id: String, model: WorkoutsOrderEditRequest)

    fun getAll(trainerId: String?, traineeId: String?): List<TrainingSplitResponse>

    fun edit(model: TrainingSplitEditRequest, id: String)

    fun delete(id: String)

    fun copy(id: String, model: TrainingSplitCopyRequest): TrainingSplitResponse

    fun addWorkout(id: String, model: TrainingSplitWorkoutAddRequest): WorkoutResponse

    fun getActiveTrainingSplitIdsMap(traineeIds: List<String>): Map<String, String>
}