package com.myprogressguru.trainingservice.service.impl.stats

import com.myprogressguru.trainingservice.service.StatsDataRetrievalStrategy
import com.myprogressguru.trainingservice.service.StatsRetrievalRequest
import com.myprogressguru.trainingservice.web.payload.info.Stats
import com.myprogressguru.trainingservice.web.payload.info.ValueExtractor
import java.time.LocalDate


class QuarterlyStatsRetrievalStrategy<T : ValueExtractor>(
    statsDataRetrievalStrategy: StatsDataRetrievalStrategy<T>,
) : AbstractStatsRetrievalStrategy<T>(statsDataRetrievalStrategy) {

    override fun retrieve(model: StatsRetrievalRequest): Stats<T> {
        val (startDate, endDate) = getStartEndDates(model.endDate)

        return retrieve(
            startDate = startDate,
            endDate = endDate,
            traineeId = model.traineeId,
            prevEndDate = model.endDate.minusMonths(3),
            nextEndDate = model.endDate.plusMonths(3)
        )
    }

    private fun getStartEndDates(endDate: LocalDate): Pair<LocalDate, LocalDate> {
        val quarter = when (endDate.monthValue) {
            in 1..3 -> 1
            in 4..6 -> 2
            in 7..9 -> 3
            else -> 4
        }

        val startMonth = (quarter - 1) * 3 + 1
        val endMonth = quarter * 3

        return endDate.withMonth(startMonth).withDayOfMonth(1) to endDate.withMonth(endMonth).plusMonths(1)
            .withDayOfMonth(1).minusDays(1)
    }
}