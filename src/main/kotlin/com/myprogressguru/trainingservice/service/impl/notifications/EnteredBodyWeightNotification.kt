package com.myprogressguru.trainingservice.service.impl.notifications

import com.myprogressguru.notificationsutils.service.model.SendNotificationRequest
import com.myprogressguru.trainingservice.entity.Trainee
import com.myprogressguru.trainingservice.enumeration.NotificationType
import java.time.LocalDate

class EnteredBodyWeightNotification(
    private val trainee: Trainee,
    private val date: LocalDate,
    private val bodyWeightInKg: Double,
) : BaseTrainerNotification(trainee) {

    override fun buildRequest(): SendNotificationRequest = SendNotificationRequest(
        userId = trainee.trainer.id,
        type = NotificationType.ENTERED_BODY_WEIGHT.name,
        title = "${trainee.firstName} ${trainee.lastName} - New BW!",
        body = "$date - $bodyWeightInKg KG",
        data = traineeMapOf(
            "date" to date.toString(),
            "bodyWeightInKg" to bodyWeightInKg.toString()
        )
    )
}

