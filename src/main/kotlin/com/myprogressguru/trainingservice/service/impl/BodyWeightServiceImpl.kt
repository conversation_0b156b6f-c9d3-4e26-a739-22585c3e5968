package com.myprogressguru.trainingservice.service.impl

import com.myprogressguru.commonutils.service.DateService
import com.myprogressguru.commonutils.util.numeric.roundTo
import com.myprogressguru.keycloakutils.service.DataHistoryLimitService
import com.myprogressguru.keycloakutils.service.KeycloakUtilsService
import com.myprogressguru.notificationsutils.service.NotificationsService
import com.myprogressguru.trainingservice.entity.BodyWeightRecord
import com.myprogressguru.trainingservice.enumeration.DayOfWeek
import com.myprogressguru.trainingservice.exception.InvalidTraineeException
import com.myprogressguru.trainingservice.repository.BodyWeightRecordRepository
import com.myprogressguru.trainingservice.repository.TraineeRepository
import com.myprogressguru.trainingservice.service.BodyWeightService
import com.myprogressguru.trainingservice.service.TrainingMapper
import com.myprogressguru.trainingservice.service.impl.notifications.EnteredBodyWeightNotification
import com.myprogressguru.trainingservice.web.payload.weight.BodyWeightRecordHistoryResponse
import com.myprogressguru.trainingservice.web.payload.weight.BodyWeightRecordPutRequest
import com.myprogressguru.trainingservice.web.payload.weight.BodyWeightRecordResponse
import com.myprogressguru.trainingservice.web.payload.weight.WeeklyBodyWeightRecordResponse
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import java.time.LocalDate
import java.time.temporal.ChronoUnit

@Service
class BodyWeightServiceImpl(
    private val traineeRepository: TraineeRepository,
    private val bodyWeightRecordRepository: BodyWeightRecordRepository,
    private val trainingMapper: TrainingMapper,
    private val dateService: DateService,
    private val keycloakService: KeycloakUtilsService,
    private val notificationsService: NotificationsService,
    private val dataHistoryLimitService: DataHistoryLimitService,
) : BodyWeightService {

    override fun getRecord(
        traineeId: String,
        dateCandidate: LocalDate?
    ): BodyWeightRecordResponse? {
        val date = dateService.getDateOrCurrent(dateCandidate)
        val bodyWeightRecord = bodyWeightRecordRepository.findTopByTraineeIdAndDate(traineeId, date) ?: return null

        return trainingMapper.toBodyWeightRecordResponse(
            bodyWeightRecord
        )
    }

    override fun putRecord(
        traineeId: String,
        dateCandidate: LocalDate?,
        model: BodyWeightRecordPutRequest
    ): BodyWeightRecordResponse {
        val date = dateService.getDateOrCurrent(dateCandidate)

        val bodyWeightRecordCandidate =
            bodyWeightRecordRepository.findTopByTraineeIdAndDate(traineeId, date)

        if (bodyWeightRecordCandidate != null) {
            bodyWeightRecordCandidate.bodyWeightInKg = model.bodyWeightInKg!!

            return trainingMapper.toBodyWeightRecordResponse(
                bodyWeightRecordRepository.save(bodyWeightRecordCandidate)
            )
        }

        val trainee =
            traineeRepository.findByIdOrNull(traineeId) ?: throw InvalidTraineeException(traineeId)

        notificationsService.send(
            EnteredBodyWeightNotification(
                trainee = trainee,
                bodyWeightInKg = model.bodyWeightInKg!!,
                date = date,
            )
        )

        val bodyWeightRecord = BodyWeightRecord(
            bodyWeightInKg = model.bodyWeightInKg,
            date = date,
            enteredOn = dateService.getCurrentDateTime(),
            dayOfWeek = DayOfWeek.valueOf(date.dayOfWeek.name),
            trainee = trainee
        )

        return trainingMapper.toBodyWeightRecordResponse(
            bodyWeightRecordRepository.save(bodyWeightRecord)
        )
    }

    override fun getWeeklyRecord(
        traineeId: String, dayOfWeekDateCandidate: LocalDate?
    ): WeeklyBodyWeightRecordResponse {
        val dayOfWeekDate = dateService.getDateOrCurrent(dayOfWeekDateCandidate)
        val currentWeekDaysDates = dateService.getWeekDaysDatesFromDate(dayOfWeekDate)
        val previousWeekDaysDates = dateService.getWeekDaysDatesFromDate(dayOfWeekDate.minusDays(7))

        val weeklyBodyWeightRecords = listOf(
            getProcessedWeeklyBodyWeightRecord(traineeId, currentWeekDaysDates),
            getProcessedWeeklyBodyWeightRecord(traineeId, previousWeekDaysDates)
        )

        setPercentageDiff(weeklyBodyWeightRecords)

        return weeklyBodyWeightRecords.first()
    }

    override fun getWeeklyRecords(
        traineeId: String,
        firstWeekDate: LocalDate,
        lastWeekDate: LocalDate
    ): List<WeeklyBodyWeightRecordResponse> {
        val startDate = dateService.getWeekDaysDatesFromDate(firstWeekDate).first()
        val endDate = dateService.getWeekDaysDatesFromDate(lastWeekDate).last()
        val dates = dateService.getDatesBetween(startDate, endDate)

        return getProcessedBodyWeightRecords(traineeId, dates)
            .chunked(7)
            .map { getProcessedWeeklyBodyWeightRecord(it) }
            .reversed()
            .apply { setPercentageDiff(this) }
            .reversed()
    }

    override fun getRecordHistory(
        traineeId: String,
        page: Long
    ): BodyWeightRecordHistoryResponse {
        val totalPages = getBodyWeightRecordHistoryTotalPages(traineeId)

        val validPage = sanitizePage(page, totalPages)

        val dates = ((validPage - 1) * 4 until validPage * 5)
            .map { dateService.getCurrentDate().minusWeeks(it) }
            .map { dateService.getWeekDaysDatesFromDate(it) }
            .flatten()

        val processedBodyWeightRecords = getProcessedBodyWeightRecords(traineeId, dates)

        val processedWeeklyRecords = processedBodyWeightRecords
            .chunked(7)
            .sortedByDescending { it.first().date }
            .map { getProcessedWeeklyBodyWeightRecord(it) }

        setPercentageDiff(processedWeeklyRecords)

        return BodyWeightRecordHistoryResponse(
            page = validPage,
            totalPages = totalPages,
            isLastPage = if (keycloakService.isRequesterAdmin()) false else validPage == totalPages,
            weeklyRecords = processedWeeklyRecords.dropLast(1)
        )
    }

    override fun getBodyWeightRecordsMap(traineeId: String, dates: List<LocalDate>): Map<LocalDate, Double> =
        bodyWeightRecordRepository.findAllByTraineeIdAndDateInOrderByDateAsc(traineeId, dates)
            .associate { it.date to it.bodyWeightInKg }


    override fun getBodyWeightRecordsMap(traineeIds: List<String>, date: LocalDate): Map<String, Double> =
        bodyWeightRecordRepository.findAllByTraineeIdInAndDateOrderByDateAsc(traineeIds, date)
            .associate { it.trainee.id to it.bodyWeightInKg }

    private fun sanitizePage(page: Long, totalPages: Long): Long {
        if (page > totalPages && !keycloakService.isRequesterAdmin()) {
            return totalPages
        }

        return page
    }

    private fun getBodyWeightRecordHistoryTotalPages(traineeId: String): Long {
        val startDate = dataHistoryLimitService.getStartDate(traineeId)

        val oldestRecordDate =
            bodyWeightRecordRepository.findTopByTraineeIdAndDateAfterOrderByDateAsc(traineeId, startDate)?.date
                ?: return 1

        val latestRecordDate =
            bodyWeightRecordRepository.findTopByTraineeIdAndDateAfterOrderByDateDesc(traineeId, startDate)?.date
                ?: return 1

        val weeksBetween = ChronoUnit
            .WEEKS
            .between(
                oldestRecordDate,
                latestRecordDate
            )

        return (weeksBetween / 4) + 1L
    }

    private fun getProcessedBodyWeightRecords(
        traineeId: String,
        weekDaysDates: List<LocalDate>
    ): List<BodyWeightRecordResponse> {
        val startDate = weekDaysDates.min()
        val endDate = weekDaysDates.max()

        val filledRecords = getFilledBodyWeightRecords(traineeId, startDate, endDate)

        val filledRecordsDates = filledRecords.map { it.date }
        val missingRecords = weekDaysDates
            .filter { !filledRecordsDates.contains(it) }
            .map {
                BodyWeightRecordResponse(
                    date = it,
                    dayOfWeek = DayOfWeek.valueOf(it.dayOfWeek.name)
                )
            }

        return (filledRecords + missingRecords).sortedBy { it.date }
    }

    private fun getProcessedWeeklyBodyWeightRecord(
        traineeId: String, weekDaysDates: List<LocalDate>
    ): WeeklyBodyWeightRecordResponse =
        getProcessedWeeklyBodyWeightRecord(getProcessedBodyWeightRecords(traineeId, weekDaysDates))

    private fun getProcessedWeeklyBodyWeightRecord(
        processedRecords: List<BodyWeightRecordResponse>,
    ): WeeklyBodyWeightRecordResponse {
        val bodyWeightList = processedRecords.mapNotNull { it.bodyWeightInKg }

        val averageBodyWeight: Double? =
            if (bodyWeightList.isNotEmpty()) bodyWeightList.average().roundTo(2) else null

        return WeeklyBodyWeightRecordResponse(
            records = processedRecords,
            averageBodyWeight = averageBodyWeight
        )
    }

    private fun getFilledBodyWeightRecords(
        traineeId: String,
        startDate: LocalDate,
        endDate: LocalDate
    ) = trainingMapper.toBodyWeightRecordResponseList(
        bodyWeightRecordRepository.findAllByTraineeIdAndDateBetweenOrderByDateAsc(
            traineeId,
            startDate,
            endDate
        )
    )

    private fun setPercentageDiff(processedWeeklyRecords: List<WeeklyBodyWeightRecordResponse>) {
        for (i in processedWeeklyRecords.indices) {
            if (i == processedWeeklyRecords.lastIndex) {
                break
            }

            val new = processedWeeklyRecords[i]
            val prev = processedWeeklyRecords[i + 1]

            if (new.averageBodyWeight == null || prev.averageBodyWeight == null) {
                continue
            }

            new.percentageDiff =
                ((new.averageBodyWeight - prev.averageBodyWeight) / prev.averageBodyWeight * 100).roundTo(
                    2
                )
        }
    }
}