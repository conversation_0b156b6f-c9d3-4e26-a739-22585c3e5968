package com.myprogressguru.trainingservice.service.impl

import com.myprogressguru.commonutils.service.DateService
import com.myprogressguru.notificationsutils.service.NotificationsService
import com.myprogressguru.trainingservice.entity.ScheduledRestDay
import com.myprogressguru.trainingservice.exception.ExistingScheduledRestDayException
import com.myprogressguru.trainingservice.exception.InvalidScheduledRestDayException
import com.myprogressguru.trainingservice.repository.ScheduledRestDayRepository
import com.myprogressguru.trainingservice.repository.TraineeRepository
import com.myprogressguru.trainingservice.repository.TrainerRepository
import com.myprogressguru.trainingservice.service.ScheduledRestDayService
import com.myprogressguru.trainingservice.service.TrainingMapper
import com.myprogressguru.trainingservice.service.impl.notifications.ScheduledRestDayNotification
import com.myprogressguru.trainingservice.web.payload.training.schedule.RestDaysScheduleResponse
import com.myprogressguru.trainingservice.web.payload.training.schedule.ScheduledRestDayCreateRequest
import com.myprogressguru.trainingservice.web.payload.training.schedule.ScheduledRestDayEditRequest
import com.myprogressguru.trainingservice.web.payload.training.schedule.ScheduledRestDayResponse
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import java.time.LocalDate

@Service
class ScheduledRestDayServiceImpl(
    private val notificationsService: NotificationsService,
    private val scheduledRestDayRepository: ScheduledRestDayRepository,
    private val trainingMapper: TrainingMapper,
    private val dateService: DateService,
    traineeRepository: TraineeRepository,
    trainerRepository: TrainerRepository,
    @Value("\${mpg.training-schedule.days-to-add}")
    private val trainingScheduleDaysToAdd: Long
) : ScheduledRestDayService, AbstractTrainingService(trainerRepository, traineeRepository) {

    override fun create(model: ScheduledRestDayCreateRequest): ScheduledRestDayResponse {
        if (scheduledRestDayRepository.existsByRestOnAndTraineeId(model.restOn!!, model.traineeId!!)) {
            throw ExistingScheduledRestDayException()
        }

        val trainee = getTrainee(model.traineeId!!)

        val scheduledRestDay = ScheduledRestDay(
            createdOn = dateService.getCurrentDateTime(),
            restOn = model.restOn!!,
            reason = model.reason!!,
            trainee = trainee
        )

        scheduledRestDayRepository.save(scheduledRestDay)

        notificationsService.send(
            ScheduledRestDayNotification(trainee = trainee, scheduledRestDay = scheduledRestDay)
        )

        return trainingMapper.toScheduledRestDayResponse(scheduledRestDay)
    }

    override fun delete(id: String) {
        scheduledRestDayRepository.deleteById(id)
    }

    override fun edit(id: String, model: ScheduledRestDayEditRequest) {
        val scheduledRestDay =
            scheduledRestDayRepository.findByIdOrNull(id) ?: throw InvalidScheduledRestDayException(id)

        scheduledRestDay.reason = model.reason!!

        scheduledRestDayRepository.save(scheduledRestDay)
    }

    override fun getSchedule(traineeId: String): RestDaysScheduleResponse {
        val restDays = getScheduledRestDays(traineeId)

        return RestDaysScheduleResponse(restDays = trainingMapper.toScheduledRestDayResponseList(restDays))
    }

    override fun getRestDays(traineeId: String): List<LocalDate> = getScheduledRestDays(traineeId).map { it.restOn }

    override fun getScheduledRestDaysMap(
        traineeIds: List<String>,
        date: LocalDate
    ): Map<String, ScheduledRestDayResponse> =
        scheduledRestDayRepository.findAllByRestOnAndTraineeIdIn(date, traineeIds)
            .associateBy { it.trainee.id }
            .mapValues { trainingMapper.toScheduledRestDayResponse(it.value) }

    private fun getScheduledRestDays(traineeId: String): List<ScheduledRestDay> {
        val startDate = dateService.getCurrentDate()
        val endDate = startDate.plusDays(trainingScheduleDaysToAdd)

        return scheduledRestDayRepository.findAllByRestOnBetweenAndTraineeId(startDate, endDate, traineeId)
    }
}
