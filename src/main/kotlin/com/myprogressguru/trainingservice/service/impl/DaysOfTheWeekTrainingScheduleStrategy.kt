package com.myprogressguru.trainingservice.service.impl

import com.myprogressguru.commonutils.service.DateService
import com.myprogressguru.trainingservice.entity.Trainee
import com.myprogressguru.trainingservice.entity.TrainingSchedule
import com.myprogressguru.trainingservice.enumeration.TrainingScheduleType
import com.myprogressguru.trainingservice.exception.DaysOfTheWeekScheduleNotProvidedException
import com.myprogressguru.trainingservice.service.TrainingMapper
import com.myprogressguru.trainingservice.service.TrainingScheduleStrategy
import com.myprogressguru.trainingservice.web.payload.training.schedule.TrainingScheduleRequest
import com.myprogressguru.trainingservice.web.payload.training.schedule.TrainingScheduleResponse
import org.springframework.stereotype.Service
import java.time.LocalDate

@Service
class DaysOfTheWeekTrainingScheduleStrategy(
    private val trainingMapper: TrainingMapper,
    private val dateService: DateService
) : TrainingScheduleStrategy {

    override fun create(model: TrainingScheduleRequest, trainee: Trainee): TrainingSchedule {
        if (model.daysOfTheWeekSchedule == null) {
            throw DaysOfTheWeekScheduleNotProvidedException()
        }

        return TrainingSchedule(
            type = TrainingScheduleType.DAYS_OF_THE_WEEK,
            trainee = trainee,
            isActive = true,
            monday = model.daysOfTheWeekSchedule.monday,
            tuesday = model.daysOfTheWeekSchedule.tuesday,
            wednesday = model.daysOfTheWeekSchedule.wednesday,
            thursday = model.daysOfTheWeekSchedule.thursday,
            friday = model.daysOfTheWeekSchedule.friday,
            saturday = model.daysOfTheWeekSchedule.saturday,
            sunday = model.daysOfTheWeekSchedule.sunday,
            createdOn = dateService.getCurrentDateTime()
        )
    }

    override fun toTrainingScheduleResponse(trainingSchedule: TrainingSchedule): TrainingScheduleResponse =
        trainingMapper.toDaysOfTheWeekTrainingScheduleResponse(trainingSchedule)

    override fun getNextWorkoutDate(
        trainingSchedule: TrainingSchedule,
        workoutHistory: List<LocalDate>,
        currentDate: LocalDate
    ): LocalDate {
        val lastWorkoutDate = workoutHistory.maxOrNull() ?: currentDate.minusDays(1)

        val daysOfWeek = listOf(
            trainingSchedule.monday!!,
            trainingSchedule.tuesday!!,
            trainingSchedule.wednesday!!,
            trainingSchedule.thursday!!,
            trainingSchedule.friday!!,
            trainingSchedule.saturday!!,
            trainingSchedule.sunday!!
        )

        var nextWorkoutDate = if (lastWorkoutDate.isEqual(currentDate)) currentDate.plusDays(1) else currentDate
        while (!daysOfWeek[nextWorkoutDate.dayOfWeek.ordinal % 7]) {
            nextWorkoutDate = nextWorkoutDate.plusDays(1)
        }

        return nextWorkoutDate
    }
}