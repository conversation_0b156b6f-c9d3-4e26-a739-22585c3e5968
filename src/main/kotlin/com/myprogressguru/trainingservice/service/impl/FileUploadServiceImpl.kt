package com.myprogressguru.trainingservice.service.impl

import com.myprogressguru.commonutils.service.DateService
import com.myprogressguru.storageutils.service.StorageService
import com.myprogressguru.storageutils.service.model.StorageObjectResponse
import com.myprogressguru.storageutils.web.payload.StorageObjectUploadUrlRequest
import com.myprogressguru.trainingservice.exception.InvalidNoteException
import com.myprogressguru.trainingservice.exception.InvalidTraineeException
import com.myprogressguru.trainingservice.exception.InvalidWorkoutExerciseSetRecordException
import com.myprogressguru.trainingservice.repository.NoteRepository
import com.myprogressguru.trainingservice.repository.TraineeRepository
import com.myprogressguru.trainingservice.repository.WorkoutExerciseSetRecordRepository
import com.myprogressguru.trainingservice.service.FileUploadService
import com.myprogressguru.trainingservice.web.payload.body.photo.BodyPhotoUploadUrlRequest
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import java.util.*

@Service
class FileUploadServiceImpl(
    private val storageService: StorageService,
    private val dateService: DateService,
    private val traineeRepository: TraineeRepository,
    private val workoutExerciseSetRecordRepository: WorkoutExerciseSetRecordRepository,
    private val noteRepository: NoteRepository
) : FileUploadService {

    override fun getBodyPhotoUploadUrl(model: BodyPhotoUploadUrlRequest): StorageObjectResponse {
        validateTraineeId(model.traineeId!!)

        val key =
            "trainees/${model.traineeId}/body-photos/${dateService.getCurrentDate()}/${model.type}/${UUID.randomUUID()}.${model.fileExtension}"

        return storageService.getPresignedUploadUrl(key)
    }

    override fun getSetRecordVideoUploadUrl(
        setRecordId: String,
        model: StorageObjectUploadUrlRequest
    ): StorageObjectResponse {
        val setRecord = workoutExerciseSetRecordRepository.findByIdOrNull(setRecordId)
            ?: throw InvalidWorkoutExerciseSetRecordException(setRecordId)

        val key =
            "trainees/${setRecord.exerciseRecord.workoutRecord.trainee.id}/exercise-videos/${dateService.getCurrentDate()}/${UUID.randomUUID()}.${model.fileExtension}"

        return storageService.getPresignedUploadUrl(key)
    }

    override fun getNotePhotoUploadUrl(noteId: String, model: StorageObjectUploadUrlRequest): StorageObjectResponse {
        val note = noteRepository.findByIdOrNull(noteId) ?: throw InvalidNoteException(noteId)

        val key =
            "trainees/${note.trainee.id}/notes/${dateService.getCurrentDate()}/${UUID.randomUUID()}.${model.fileExtension}"

        return storageService.getPresignedUploadUrl(key)
    }

    private fun validateTraineeId(traineeId: String) {
        if (!traineeRepository.existsById(traineeId)) {
            throw InvalidTraineeException(traineeId)
        }
    }
}