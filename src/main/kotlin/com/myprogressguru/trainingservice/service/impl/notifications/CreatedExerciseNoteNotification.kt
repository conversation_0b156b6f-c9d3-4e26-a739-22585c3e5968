package com.myprogressguru.trainingservice.service.impl.notifications

import com.myprogressguru.notificationsutils.service.model.SendNotificationRequest
import com.myprogressguru.trainingservice.entity.ExerciseNote
import com.myprogressguru.trainingservice.entity.Trainee
import com.myprogressguru.trainingservice.enumeration.NotificationType

class CreatedExerciseNoteNotification(
    private val trainee: Trainee,
    private val exerciseNote: ExerciseNote,
) : BaseTrainerNotification(trainee) {

    override fun buildRequest(): SendNotificationRequest = SendNotificationRequest(
        userId = trainee.trainer.id,
        type = NotificationType.CREATED_EXERCISE_NOTE.name,
        title = "${trainee.firstName} ${trainee.lastName} - New exercise note for ${exerciseNote.exercise.name}!",
        body = exerciseNote.note.content,
        data = traineeMapOf(
            "exerciseId" to exerciseNote.exercise.id!!,
            "noteId" to exerciseNote.note.id!!,
            "noteContent" to exerciseNote.note.content,
            "exerciseName" to exerciseNote.exercise.name
        )
    )

}