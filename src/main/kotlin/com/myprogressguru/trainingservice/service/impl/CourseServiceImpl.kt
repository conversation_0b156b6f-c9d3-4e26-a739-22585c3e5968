package com.myprogressguru.trainingservice.service.impl

import com.myprogressguru.notificationsutils.service.NotificationsService
import com.myprogressguru.trainingservice.entity.Course
import com.myprogressguru.trainingservice.entity.TraineeCourse
import com.myprogressguru.trainingservice.entity.WatchedCourseVideo
import com.myprogressguru.trainingservice.exception.InvalidCourseException
import com.myprogressguru.trainingservice.exception.InvalidCourseFilterException
import com.myprogressguru.trainingservice.exception.InvalidTrainerException
import com.myprogressguru.trainingservice.repository.*
import com.myprogressguru.trainingservice.service.CourseService
import com.myprogressguru.trainingservice.service.TrainingMapper
import com.myprogressguru.trainingservice.service.impl.notifications.CourseRefreshNotification
import com.myprogressguru.trainingservice.service.impl.notifications.WatchedCourseVideoNotification
import com.myprogressguru.trainingservice.web.payload.course.CourseCreateRequest
import com.myprogressguru.trainingservice.web.payload.course.CourseResponse
import com.myprogressguru.trainingservice.web.payload.course.CourseVideoWatchRequest
import com.myprogressguru.trainingservice.web.payload.info.OptionalIdentifiableEntitiesRequest
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class CourseServiceImpl(
    private val trainingMapper: TrainingMapper,
    private val courseRepository: CourseRepository,
    private val traineeCourseRepository: TraineeCourseRepository,
    private val watchedCourseVideoRepository: WatchedCourseVideoRepository,
    private val notificationsService: NotificationsService,
    val trainerRepository: TrainerRepository,
    val traineeRepository: TraineeRepository
) : AbstractTrainingService(trainerRepository, traineeRepository), CourseService {

    override fun getAll(traineeId: String?, trainerId: String?): List<CourseResponse> {
        if (traineeId == null && trainerId == null) {
            throw InvalidCourseFilterException()
        }

        val courses = if (traineeId != null) {
            traineeCourseRepository.findAllByTraineeId(traineeId)
                .map { it.course }
        } else {
            courseRepository.findAllByTrainerId(trainerId!!)
        }

        return trainingMapper.toCourseResponseList(courses)
    }

    override fun create(model: CourseCreateRequest): CourseResponse {
        val course = Course(
            name = model.name!!,
            url = model.url!!,
            trainer = trainerRepository.findByIdOrNull(model.trainerId!!)
                ?: throw InvalidTrainerException(model.trainerId)
        )

        return trainingMapper.toCourseResponse(courseRepository.save(course))
    }

    override fun delete(id: String) {
        val course = getCourse(id)

        courseRepository.delete(course)
    }

    override fun markAsWatched(videoId: String, traineeId: String, model: CourseVideoWatchRequest) {
        if (watchedCourseVideoRepository.existsByVideoIdAndTraineeId(videoId, traineeId)) {
            return
        }

        val watchedCourseVideo = WatchedCourseVideo(
            videoId = videoId,
            trainee = getTrainee(traineeId)
        )

        watchedCourseVideoRepository.save(watchedCourseVideo)

        notificationsService.send(
            WatchedCourseVideoNotification(
                trainee = watchedCourseVideo.trainee,
                courseName = model.courseName!!,
                videoName = model.videoName!!
            )
        )
    }

    override fun markAsUnwatched(videoId: String, traineeId: String) {
        val watchedCourseVideo = watchedCourseVideoRepository.findByVideoIdAndTraineeId(videoId, traineeId) ?: return

        watchedCourseVideoRepository.delete(watchedCourseVideo)
    }

    override fun getAllWatchedVideosIds(traineeId: String): List<String> =
        watchedCourseVideoRepository.findAllByTraineeId(traineeId)
            .map { it.videoId }

    @Transactional
    override fun putCourses(traineeId: String, model: OptionalIdentifiableEntitiesRequest): List<CourseResponse> {
        val trainee = getTrainee(traineeId)

        val trainerId = trainee.trainer.id
        traineeCourseRepository.deleteByTraineeIdAndCourseTrainerId(traineeId, trainerId)
        traineeCourseRepository.flush()

        val newTraineeCourses = courseRepository.findAllByTrainerIdAndIdIn(trainerId, model.ids).map { course ->
            TraineeCourse(
                trainee = trainee,
                course = course
            )
        }

        traineeCourseRepository.saveAll(newTraineeCourses)

        val newCourses = newTraineeCourses.map { it.course }

        return trainingMapper.toCourseResponseList(newCourses)
    }

    override fun getCoursesMap(traineeIds: List<String>): Map<String, List<CourseResponse>> =
        traineeCourseRepository.findAllByTraineeIdIn(traineeIds)
            .groupBy { it.trainee.id }
            .mapValues { entry ->
                entry.value.map { it.course }
            }
            .mapValues { entry ->
                trainingMapper.toCourseResponseList(entry.value)
            }

    override fun notifyRefresh(id: String) {
        val traineeIds = traineeCourseRepository.findAllByCourseId(id)
            .map { it.trainee.id }
            .distinct()

        traineeIds.forEach {
            notificationsService.send(
                CourseRefreshNotification(
                    traineeId = it,
                    course = getCourse(id)
                )
            )
        }
    }

    private fun getCourse(id: String) = courseRepository.findByIdOrNull(id) ?: throw InvalidCourseException(id)
}