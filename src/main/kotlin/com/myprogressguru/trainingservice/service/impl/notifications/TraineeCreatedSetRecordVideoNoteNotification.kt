package com.myprogressguru.trainingservice.service.impl.notifications

import com.myprogressguru.notificationsutils.service.model.SendNotificationRequest
import com.myprogressguru.trainingservice.entity.Note
import com.myprogressguru.trainingservice.entity.Trainee
import com.myprogressguru.trainingservice.entity.WorkoutExerciseSetRecord
import com.myprogressguru.trainingservice.enumeration.NotificationType

class TraineeCreatedSetRecordVideoNoteNotification(
    private val trainee: Trainee,
    private val setRecord: WorkoutExerciseSetRecord,
    private val note: Note,
) : BaseTrainerNotification(trainee) {

    override fun buildRequest(): SendNotificationRequest = SendNotificationRequest(
        userId = trainee.trainer.id,
        type = NotificationType.CREATED_SET_RECORD_VIDEO_NOTE.name,
        title = "${trainee.firstName} ${trainee.lastName} - New set video note for ${setRecord.exerciseRecord.exercise.exercise.name}!",
        body = note.content,
        data = traineeMapOf(
            "setRecordId" to setRecord.id!!,
            "noteId" to note.id!!,
            "noteContent" to note.content,
            "exerciseName" to setRecord.exerciseRecord.exercise.exercise.name
        )
    )

}