package com.myprogressguru.trainingservice.service.impl.stats

import com.myprogressguru.trainingservice.service.StatsDataRetrievalStrategy
import com.myprogressguru.trainingservice.service.StatsRetrievalRequest
import com.myprogressguru.trainingservice.web.payload.info.Stats
import com.myprogressguru.trainingservice.web.payload.info.ValueExtractor


class MonthlyStatsRetrievalStrategy<T : ValueExtractor>(
    statsDataRetrievalStrategy: StatsDataRetrievalStrategy<T>,
) : AbstractStatsRetrievalStrategy<T>(statsDataRetrievalStrategy) {

    override fun retrieve(model: StatsRetrievalRequest): Stats<T> {
        val startDate = model.endDate.withDayOfMonth(1)
        val endDate = model.endDate.plusMonths(1)
            .withDayOfMonth(1)
            .minusDays(1)

        return retrieve(
            startDate = startDate,
            endDate = endDate,
            traineeId = model.traineeId,
            prevEndDate = model.endDate.minusMonths(1),
            nextEndDate = model.endDate.plusMonths(1)
        )
    }
}