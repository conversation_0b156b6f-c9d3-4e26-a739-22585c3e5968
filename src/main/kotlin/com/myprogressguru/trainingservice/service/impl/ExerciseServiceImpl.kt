package com.myprogressguru.trainingservice.service.impl

import com.myprogressguru.notificationsutils.service.NotificationsService
import com.myprogressguru.trainingservice.entity.ExerciseNote
import com.myprogressguru.trainingservice.entity.QExercise
import com.myprogressguru.trainingservice.exception.DuplicateExerciseNameException
import com.myprogressguru.trainingservice.exception.InvalidExerciseException
import com.myprogressguru.trainingservice.exception.InvalidTraineeException
import com.myprogressguru.trainingservice.repository.ExerciseNoteRepository
import com.myprogressguru.trainingservice.repository.ExerciseRepository
import com.myprogressguru.trainingservice.repository.TraineeRepository
import com.myprogressguru.trainingservice.service.ExerciseService
import com.myprogressguru.trainingservice.service.NoteService
import com.myprogressguru.trainingservice.service.TrainingMapper
import com.myprogressguru.trainingservice.service.impl.notifications.CreatedExerciseNoteNotification
import com.myprogressguru.trainingservice.web.payload.exercise.ExerciseCreateRequest
import com.myprogressguru.trainingservice.web.payload.exercise.ExerciseResponse
import com.myprogressguru.trainingservice.web.payload.note.NoteRequest
import com.myprogressguru.trainingservice.web.payload.note.NoteResponse
import com.myprogressguru.trainingservice.web.payload.translation.TranslationsResponse
import jakarta.transaction.Transactional
import org.springframework.cache.annotation.CacheEvict
import org.springframework.cache.annotation.Cacheable
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service

@Service
class ExerciseServiceImpl(
    private val noteService: NoteService,
    private val exerciseRepository: ExerciseRepository,
    private val exerciseNoteRepository: ExerciseNoteRepository,
    private val traineeRepository: TraineeRepository,
    private val trainingMapper: TrainingMapper,
    private val notificationsService: NotificationsService,
) : ExerciseService {

    companion object {
        private val EXERCISE_PATH: QExercise = QExercise.exercise
    }

    @CacheEvict(value = ["exercises"], key = "'translations'")
    override fun create(model: ExerciseCreateRequest): ExerciseResponse {
        var predicate = EXERCISE_PATH.name.eq(model.name!!)
        if (model.nameBg != null) {
            predicate = predicate.or(EXERCISE_PATH.nameBg.eq(model.nameBg))
        }

        if (exerciseRepository.exists(predicate)) {
            throw DuplicateExerciseNameException()
        }

        val exercise = trainingMapper.toExercise(model)

        exerciseRepository.save(exercise)

        return trainingMapper.toExerciseResponse(exercise)
    }

    @CacheEvict(value = ["exercises"], key = "'translations'")
    override fun edit(id: String, model: ExerciseCreateRequest) {
        val exercise = getExercise(id)

        trainingMapper.map(model, exercise)

        exerciseRepository.save(exercise)
    }


    override fun delete(id: String) {
        exerciseRepository.deleteById(id)
    }

    override fun getAll(): List<ExerciseResponse> =
        trainingMapper.toExerciseResponseList(exerciseRepository.findAllByOrderByName())

    override fun get(id: String): ExerciseResponse = trainingMapper.toExerciseResponse(getExercise(id))

    @Cacheable(value = ["exercises"], key = "'translations'")
    override fun getTranslations(): TranslationsResponse {
        val exercises = exerciseRepository.findAllByOrderByName()

        return TranslationsResponse(
            en = exercises.associate { it.id!! to it.name },
            bg = exercises.filter { it.nameBg != null }.associate { it.id!! to it.nameBg!! }
        )
    }

    override fun getNotes(exerciseId: String, traineeId: String): List<NoteResponse> =
        trainingMapper.toNoteResponseList(
            exerciseNoteRepository.findAllByExerciseIdAndTraineeIdOrderByNoteCreatedOn(exerciseId, traineeId)
        )

    override fun createNote(exerciseId: String, traineeId: String, model: NoteRequest): NoteResponse {
        val exercise = getExercise(exerciseId)
        val trainee = traineeRepository.findByIdOrNull(traineeId) ?: throw InvalidTraineeException(traineeId)

        val note = noteService.create(model)

        val exerciseNote = ExerciseNote(
            exercise = exercise,
            trainee = trainee,
            note = note
        )
        exerciseNoteRepository.save(exerciseNote)

        notificationsService.send(
            CreatedExerciseNoteNotification(
                trainee = trainee,
                exerciseNote = exerciseNote,
            )
        )

        return trainingMapper.toNoteResponse(exerciseNote)
    }

    @Transactional
    override fun deleteNote(noteId: String) = exerciseNoteRepository.deleteByNoteId(noteId)

    private fun getExercise(id: String) = exerciseRepository.findByIdOrNull(id) ?: throw InvalidExerciseException(id)

}
