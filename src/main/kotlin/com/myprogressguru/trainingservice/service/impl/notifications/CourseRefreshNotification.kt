package com.myprogressguru.trainingservice.service.impl.notifications

import com.myprogressguru.notificationsutils.service.impl.SimpleNotification
import com.myprogressguru.notificationsutils.service.model.SendNotificationRequest
import com.myprogressguru.trainingservice.entity.Course
import com.myprogressguru.trainingservice.enumeration.NotificationType

class CourseRefreshNotification(
    private val traineeId: String,
    private val course: Course
) : SimpleNotification() {

    override fun buildRequest(): SendNotificationRequest = SendNotificationRequest(
        userId = traineeId,
        type = NotificationType.COURSE_REFRESH.name,
        title = "${course.name} - New Lessons Available",
        body = "Check out the new lessons in your course!",
        onlyPush = true
    )
}