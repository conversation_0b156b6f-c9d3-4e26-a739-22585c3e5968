package com.myprogressguru.trainingservice.service.impl.stats

import com.myprogressguru.commonutils.service.DateService
import com.myprogressguru.trainingservice.service.StatsDataRetrievalStrategy
import com.myprogressguru.trainingservice.service.StatsRetrievalRequest
import com.myprogressguru.trainingservice.web.payload.info.Stats
import com.myprogressguru.trainingservice.web.payload.info.ValueExtractor


class WeeklyStatsRetrievalStrategy<T : ValueExtractor>(
    private val dateService: DateService,
    statsDataRetrievalStrategy: StatsDataRetrievalStrategy<T>,
) : AbstractStatsRetrievalStrategy<T>(statsDataRetrievalStrategy) {

    override fun retrieve(model: StatsRetrievalRequest): Stats<T> {
        val weekDays = dateService.getWeekDaysDatesFromDate(model.endDate)
        val startDate = weekDays.first()
        val endDate = weekDays.last()

        return retrieve(
            startDate = startDate,
            endDate = endDate,
            traineeId = model.traineeId,
            prevEndDate = model.endDate.minusWeeks(1),
            nextEndDate = model.endDate.plusWeeks(1)
        )
    }
}