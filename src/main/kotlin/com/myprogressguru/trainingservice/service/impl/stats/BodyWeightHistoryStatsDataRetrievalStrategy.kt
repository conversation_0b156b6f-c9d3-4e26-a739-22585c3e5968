package com.myprogressguru.trainingservice.service.impl.stats

import com.myprogressguru.trainingservice.service.BodyWeightService
import com.myprogressguru.trainingservice.service.StatsDataRetrievalRequest
import com.myprogressguru.trainingservice.service.StatsDataRetrievalStrategy
import com.myprogressguru.trainingservice.web.payload.weight.WeeklyBodyWeightRecordResponse

class BodyWeightHistoryStatsDataRetrievalStrategy(
    private val bodyWeightService: BodyWeightService
) : StatsDataRetrievalStrategy<WeeklyBodyWeightRecordResponse> {

    override fun retrieve(model: StatsDataRetrievalRequest): List<WeeklyBodyWeightRecordResponse> =
        bodyWeightService.getWeeklyRecords(
            traineeId = model.traineeId,
            firstWeekDate = model.startDate,
            lastWeekDate = model.endDate
        )
}