package com.myprogressguru.trainingservice.service.impl.stats

import com.myprogressguru.trainingservice.repository.StatsRepository
import com.myprogressguru.trainingservice.service.StatsDataRetrievalRequest
import com.myprogressguru.trainingservice.service.StatsDataRetrievalStrategy
import com.myprogressguru.trainingservice.web.payload.info.ValueExtractor

class RepoStatsDataRetrievalStrategy<T : ValueExtractor>(
    private val statsRepository: StatsRepository<T>
) : StatsDataRetrievalStrategy<T> {

    override fun retrieve(model: StatsDataRetrievalRequest): List<T> =
        statsRepository.findAllByTraineeIdAndDateBetween(model.traineeId, model.startDate, model.endDate)
}