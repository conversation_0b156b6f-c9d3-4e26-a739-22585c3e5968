package com.myprogressguru.trainingservice.service.impl

import com.myprogressguru.commonutils.service.DateService
import com.myprogressguru.trainingservice.config.properties.WhoopProperties
import com.myprogressguru.trainingservice.entity.Integration
import com.myprogressguru.trainingservice.enumeration.IntegrationType
import com.myprogressguru.trainingservice.exception.InvalidTraineeException
import com.myprogressguru.trainingservice.repository.IntegrationRepository
import com.myprogressguru.trainingservice.repository.TraineeRepository
import com.myprogressguru.trainingservice.service.IntegrationService
import com.myprogressguru.trainingservice.service.TrainingMapper
import com.myprogressguru.trainingservice.service.rest.client.TokenResponse
import com.myprogressguru.trainingservice.service.rest.client.WhoopClient
import com.myprogressguru.trainingservice.web.payload.integration.IntegrationResponse
import jakarta.transaction.Transactional
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.springframework.data.repository.findByIdOrNull
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.time.ZonedDateTime

@Service
class IntegrationServiceImpl(
    private val whoopClient: WhoopClient,
    private val whoopProperties: WhoopProperties,
    private val dateService: DateService,
    private val traineeRepository: TraineeRepository,
    private val integrationRepository: IntegrationRepository,
    private val trainingMapper: TrainingMapper
) : IntegrationService {

    override fun createWhoopIntegration(traineeId: String, code: String): IntegrationResponse {
        val request = mapOf(
            "grant_type" to "authorization_code",
            "code" to code,
            "client_id" to whoopProperties.clientId,
            "client_secret" to whoopProperties.clientSecret,
            "redirect_uri" to whoopProperties.callbackUrl,
        )

        val token = whoopClient.getToken(request)

        val user = whoopClient.getUserProfile("Bearer ${token.accessToken}")

        integrationRepository.findByExternalUserIdAndTypeAndTraineeId(user.userId, IntegrationType.WHOOP, traineeId)
            ?.let {
                it.accessToken = token.accessToken
                it.accessTokenExpiresAt = getAccessTokenExpiresAt(token)
                it.refreshToken = token.refreshToken
                integrationRepository.save(it)

                return trainingMapper.toIntegrationResponse(it)
            }

        val integration = Integration(
            externalUserId = user.userId,
            createdOn = dateService.getCurrentDate(),
            type = IntegrationType.WHOOP,
            accessToken = token.accessToken,
            accessTokenExpiresAt = getAccessTokenExpiresAt(token),
            refreshToken = token.refreshToken,
            trainee = traineeRepository.findByIdOrNull(traineeId) ?: throw InvalidTraineeException(traineeId)
        )

        integrationRepository.save(integration)

        return trainingMapper.toIntegrationResponse(integration)
    }

    override fun getAll(traineeId: String): List<IntegrationResponse> = trainingMapper.toIntegrationResponseList(
        integrationRepository.findAllByTraineeId(traineeId)
    )

    override fun getWhoopAuthHeaderAndTraineeId(externalUserId: String): Pair<String, String>? {
        val integration =
            integrationRepository.findByTypeAndExternalUserId(IntegrationType.WHOOP, externalUserId) ?: return null

        if (integration.accessTokenExpiresAt.isBefore(dateService.getCurrentDateTime())) {
            try {
                refreshTokens(integration)
                integrationRepository.save(integration)
            } catch (e: Exception) {
                integrationRepository.delete(integration)
                return null
            }

        }

        return "Bearer ${integration.accessToken}" to integration.trainee.id
    }

    @Transactional
    @Scheduled(initialDelay = 300000, fixedRate = 43200000)
    @SchedulerLock(name = "refresh-integration-tokens")
    override fun refreshTokens() {
        val whoopIntegrations = integrationRepository.findAllByType(IntegrationType.WHOOP).filter { integration ->
            try {
                refreshTokens(integration)
                true
            } catch (e: Exception) {
                integrationRepository.delete(integration)
                false
            }
        }

        integrationRepository.saveAll(whoopIntegrations)
    }

    private fun refreshTokens(integration: Integration) {
        val request = mapOf(
            "grant_type" to "refresh_token",
            "refresh_token" to integration.refreshToken,
            "client_id" to whoopProperties.clientId,
            "client_secret" to whoopProperties.clientSecret,
            "scope" to "offline"
        )

        val token = whoopClient.getToken(request)

        integration.accessToken = token.accessToken
        integration.accessTokenExpiresAt = getAccessTokenExpiresAt(token)
        integration.refreshToken = token.refreshToken
    }

    private fun getAccessTokenExpiresAt(token: TokenResponse): ZonedDateTime =
        dateService.getCurrentDateTime().plusSeconds(token.expiresIn.minus(100L))
}