package com.myprogressguru.trainingservice.service.impl.notifications

import com.myprogressguru.notificationsutils.service.impl.SimpleNotification
import com.myprogressguru.notificationsutils.service.model.SendNotificationRequest
import com.myprogressguru.trainingservice.entity.TraineeInvitation
import com.myprogressguru.trainingservice.enumeration.NotificationType

class FilledTraineeInvitationNotification(
    private val traineeInvitation: TraineeInvitation,
) : SimpleNotification() {

    override fun buildRequest(): SendNotificationRequest = SendNotificationRequest(
        userId = traineeInvitation.trainer.id,
        type = NotificationType.FILLED_TRAINEE_INVITATION.name,
        title = "${traineeInvitation.firstName} ${traineeInvitation.lastName} filled the trainee invitation.",
        body = "Click to see the details.",
        data = mapOf(
            "traineeFullName" to "${traineeInvitation.firstName} ${traineeInvitation.lastName}",
            "traineeInvitationId" to traineeInvitation.id!!,
            "traineeEmail" to traineeInvitation.email!!,
        )
    )
}