package com.myprogressguru.trainingservice.service.impl

import com.myprogressguru.trainingservice.enumeration.StatsPeriod
import com.myprogressguru.trainingservice.service.BodyWeightHistoryStatsService
import com.myprogressguru.trainingservice.service.StatsRetrievalRequest
import com.myprogressguru.trainingservice.service.StatsRetrievalStrategy
import com.myprogressguru.trainingservice.web.payload.info.Stats
import com.myprogressguru.trainingservice.web.payload.weight.WeeklyBodyWeightRecordResponse
import org.springframework.stereotype.Service

@Service
class BodyWeightHistoryStatsServiceImpl(
    private val historyStatsStrategies: Map<StatsPeriod, StatsRetrievalStrategy<WeeklyBodyWeightRecordResponse>>
) : BodyWeightHistoryStatsService {
    
    override fun getHistoryStats(
        model: StatsRetrievalRequest,
        period: StatsPeriod
    ): Stats<WeeklyBodyWeightRecordResponse> = historyStatsStrategies[period]!!.retrieve(model)
}