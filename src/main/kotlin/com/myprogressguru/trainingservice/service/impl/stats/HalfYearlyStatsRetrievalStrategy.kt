package com.myprogressguru.trainingservice.service.impl.stats

import com.myprogressguru.trainingservice.service.StatsDataRetrievalStrategy
import com.myprogressguru.trainingservice.service.StatsRetrievalRequest
import com.myprogressguru.trainingservice.web.payload.info.Stats
import com.myprogressguru.trainingservice.web.payload.info.ValueExtractor
import java.time.LocalDate


class HalfYearlyStatsRetrievalStrategy<T : ValueExtractor>(
    statsDataRetrievalStrategy: StatsDataRetrievalStrategy<T>,
) : AbstractStatsRetrievalStrategy<T>(statsDataRetrievalStrategy) {

    override fun retrieve(model: StatsRetrievalRequest): Stats<T> {
        val (startDate, endDate) = getStartEndDates(model.endDate)

        return retrieve(
            startDate = startDate,
            endDate = endDate,
            traineeId = model.traineeId,
            prevEndDate = model.endDate.minusMonths(6),
            nextEndDate = model.endDate.plusMonths(6)
        )
    }

    private fun getStartEndDates(endDate: LocalDate): Pair<LocalDate, LocalDate> {
        if (endDate.monthValue > 6) {
            return endDate.withMonth(7).withDayOfMonth(1) to endDate.withMonth(12).withDayOfMonth(31)
        }

        return endDate.withMonth(1).withDayOfMonth(1) to endDate.withMonth(6).withDayOfMonth(30)
    }
}