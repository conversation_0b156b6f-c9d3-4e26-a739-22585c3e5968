package com.myprogressguru.trainingservice.service.impl.notifications

import com.myprogressguru.notificationsutils.service.impl.SimpleNotification
import com.myprogressguru.notificationsutils.service.model.SendNotificationRequest
import com.myprogressguru.trainingservice.entity.TraineeInvitation
import com.myprogressguru.trainingservice.enumeration.NotificationType

class PreApprovedTraineeInvitationNotification(
    private val traineeInvitation: TraineeInvitation,
    private val traineeId: String
) : SimpleNotification() {

    override fun buildRequest(): SendNotificationRequest = SendNotificationRequest(
        userId = traineeInvitation.trainer.id,
        type = NotificationType.PRE_APPROVED_TRAINEE_INVITATION.name,
        title = "${traineeInvitation.firstName} ${traineeInvitation.lastName} created from pre-approved invitation.",
        body = "Click to see the details.",
        data = mapOf(
            "traineeFullName" to "${traineeInvitation.firstName} ${traineeInvitation.lastName}",
            "traineeInvitationId" to traineeInvitation.id!!,
            "traineeEmail" to traineeInvitation.email!!,
            "traineeId" to traineeId
        )
    )
}