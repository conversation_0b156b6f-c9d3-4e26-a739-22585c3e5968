package com.myprogressguru.trainingservice.service.impl.stats

import com.myprogressguru.trainingservice.service.StatsDataRetrievalStrategy
import com.myprogressguru.trainingservice.service.StatsRetrievalRequest
import com.myprogressguru.trainingservice.web.payload.info.Stats
import com.myprogressguru.trainingservice.web.payload.info.ValueExtractor


class YearlyStatsRetrievalStrategy<T : ValueExtractor>(
    statsDataRetrievalStrategy: StatsDataRetrievalStrategy<T>,
) : AbstractStatsRetrievalStrategy<T>(statsDataRetrievalStrategy) {

    override fun retrieve(model: StatsRetrievalRequest): Stats<T> {
        val startDate = model.endDate.withDayOfYear(1)
        val endDate = model.endDate.withDayOfYear(model.endDate.lengthOfYear())

        return retrieve(
            startDate = startDate,
            endDate = endDate,
            traineeId = model.traineeId,
            prevEndDate = model.endDate.minusYears(1),
            nextEndDate = model.endDate.plusYears(1)
        )
    }
}