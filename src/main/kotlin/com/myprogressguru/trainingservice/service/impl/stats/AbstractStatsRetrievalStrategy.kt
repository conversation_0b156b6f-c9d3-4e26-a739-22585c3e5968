package com.myprogressguru.trainingservice.service.impl.stats

import com.myprogressguru.trainingservice.service.StatsDataRetrievalRequest
import com.myprogressguru.trainingservice.service.StatsDataRetrievalStrategy
import com.myprogressguru.trainingservice.service.StatsRetrievalStrategy
import com.myprogressguru.trainingservice.web.payload.info.Stats
import com.myprogressguru.trainingservice.web.payload.info.ValueExtractor
import java.time.LocalDate


abstract class AbstractStatsRetrievalStrategy<T : ValueExtractor>(
    private val statsDataRetrievalStrategy: StatsDataRetrievalStrategy<T>
) : StatsRetrievalStrategy<T> {

    protected fun retrieve(
        startDate: LocalDate,
        endDate: LocalDate,
        traineeId: String,
        prevEndDate: LocalDate,
        nextEndDate: LocalDate
    ): Stats<T> {
        val statsRecords = statsDataRetrievalStrategy.retrieve(
            StatsDataRetrievalRequest(
                traineeId = traineeId,
                startDate = startDate,
                endDate = endDate
            )
        )

        return Stats(
            data = statsRecords,
            startDate = startDate,
            endDate = endDate,
            prevEndDate = prevEndDate,
            nextEndDate = nextEndDate
        )
    }
}