package com.myprogressguru.trainingservice.service.impl

import com.myprogressguru.commonutils.service.DateService
import com.myprogressguru.trainingservice.entity.TrainingSchedule
import com.myprogressguru.trainingservice.enumeration.TrainingScheduleType
import com.myprogressguru.trainingservice.repository.TraineeRepository
import com.myprogressguru.trainingservice.repository.TrainerRepository
import com.myprogressguru.trainingservice.repository.TrainingScheduleRepository
import com.myprogressguru.trainingservice.service.ScheduledRestDayService
import com.myprogressguru.trainingservice.service.TrainingScheduleService
import com.myprogressguru.trainingservice.service.TrainingScheduleStrategy
import com.myprogressguru.trainingservice.web.payload.training.schedule.TrainingScheduleRequest
import com.myprogressguru.trainingservice.web.payload.training.schedule.TrainingScheduleResponse
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import java.time.LocalDate

@Service
class TrainingScheduleServiceImpl(
    private val trainingScheduleRepository: TrainingScheduleRepository,
    private val trainingScheduleStrategies: Map<TrainingScheduleType, TrainingScheduleStrategy>,
    trainerRepository: TrainerRepository,
    traineeRepository: TraineeRepository,
    private val dateService: DateService,
    @Value("\${mpg.training-schedule.days-to-add}")
    private val trainingScheduleDaysToAdd: Long,
    private val scheduledRestDayService: ScheduledRestDayService
) : TrainingScheduleService, AbstractTrainingService(trainerRepository, traineeRepository) {

    override fun create(model: TrainingScheduleRequest): TrainingScheduleResponse {
        val trainee = getTrainee(model.traineeId!!)

        val trainingScheduleStrategy = trainingScheduleStrategies.getValue(model.type!!)

        val trainingSchedule = trainingScheduleStrategy.create(model, trainee)

        makeCurrentInactive(trainee.id)

        trainingScheduleRepository.save(trainingSchedule)

        return trainingScheduleStrategy.toTrainingScheduleResponse(trainingSchedule)
    }


    override fun getActive(traineeId: String): TrainingScheduleResponse? {
        val trainingSchedule = trainingScheduleRepository.findFirstByIsActiveTrueAndTraineeId(
            traineeId
        ) ?: return null

        val trainingScheduleStrategy = trainingScheduleStrategies.getValue(trainingSchedule.type)

        return trainingScheduleStrategy.toTrainingScheduleResponse(trainingSchedule)
    }

    private fun makeCurrentInactive(traineeId: String) {
        trainingScheduleRepository.findFirstByIsActiveTrueAndTraineeId(traineeId)?.apply {
            isActive = false
            trainingScheduleRepository.save(this)
        }
    }


    override fun getNextWorkoutDate(traineeId: String, workoutHistory: List<LocalDate>): LocalDate? {
        val trainingSchedule = (trainingScheduleRepository.findFirstByIsActiveTrueAndTraineeId(traineeId)
            ?: return null)

        return getNextWorkoutDate(traineeId, trainingSchedule, workoutHistory)
    }


    override fun getNextWorkoutDatesMap(
        traineeIds: List<String>,
        workoutHistoryByTraineeIdMap: Map<String, List<LocalDate>>
    ): Map<String, LocalDate> =
        trainingScheduleRepository.findAllByIsActiveTrueAndTraineeIdIn(traineeIds)
            .associateBy { it.trainee.id }
            .mapValues {
                getNextWorkoutDate(
                    it.key,
                    it.value,
                    workoutHistoryByTraineeIdMap[it.key] ?: emptyList()
                )
            }


    override fun getSchedule(
        traineeId: String,
        workoutHistory: List<LocalDate>
    ): List<LocalDate> {
        val trainingSchedule = trainingScheduleRepository.findFirstByIsActiveTrueAndTraineeId(traineeId)
            ?: return emptyList()

        val restDays = scheduledRestDayService.getRestDays(traineeId)

        val startDate = dateService.getCurrentDate()
        val endDate = startDate.plusDays(trainingScheduleDaysToAdd)

        val trainingScheduleStrategy = trainingScheduleStrategies.getValue(trainingSchedule.type)

        val monthlyWorkoutDates = mutableListOf<LocalDate>()
        var nextWorkoutDate = trainingScheduleStrategy.getNextWorkoutDate(trainingSchedule, workoutHistory, startDate)

        while (nextWorkoutDate <= endDate) {
            if (!restDays.contains(nextWorkoutDate)) {
                monthlyWorkoutDates.add(nextWorkoutDate)
            }

            nextWorkoutDate = trainingScheduleStrategy.getNextWorkoutDate(
                trainingSchedule,
                workoutHistory + monthlyWorkoutDates,
                nextWorkoutDate.plusDays(1)
            )
        }

        return monthlyWorkoutDates
    }

    private fun getNextWorkoutDate(
        traineeId: String,
        trainingSchedule: TrainingSchedule,
        workoutHistory: List<LocalDate>
    ): LocalDate {
        val trainingScheduleStrategy = trainingScheduleStrategies.getValue(trainingSchedule.type)
        val restDays = scheduledRestDayService.getRestDays(traineeId)

        return generateSequence(dateService.getCurrentDate()) { it.plusDays(1) }
            .map { currentDate ->
                trainingScheduleStrategy.getNextWorkoutDate(
                    trainingSchedule,
                    workoutHistory,
                    currentDate
                )
            }
            .first { nextWorkoutDate -> !restDays.contains(nextWorkoutDate) }
    }
}