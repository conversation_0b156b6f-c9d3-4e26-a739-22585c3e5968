package com.myprogressguru.trainingservice.service.impl

import com.myprogressguru.commonutils.service.DateService
import com.myprogressguru.trainingservice.entity.Trainee
import com.myprogressguru.trainingservice.entity.TrainingSchedule
import com.myprogressguru.trainingservice.enumeration.TrainingScheduleType
import com.myprogressguru.trainingservice.exception.PatternScheduleNotProvidedException
import com.myprogressguru.trainingservice.service.TrainingMapper
import com.myprogressguru.trainingservice.service.TrainingScheduleStrategy
import com.myprogressguru.trainingservice.web.payload.training.schedule.TrainingScheduleRequest
import com.myprogressguru.trainingservice.web.payload.training.schedule.TrainingScheduleResponse
import org.springframework.stereotype.Service
import java.time.LocalDate


@Service
class PatternTrainingScheduleStrategy(
    private val trainingMapper: TrainingMapper,
    private val dateService: DateService
) : TrainingScheduleStrategy {

    override fun create(model: TrainingScheduleRequest, trainee: Trainee): TrainingSchedule {
        if (model.patternSchedule == null) {
            throw PatternScheduleNotProvidedException()
        }

        return TrainingSchedule(
            type = TrainingScheduleType.PATTERN,
            trainee = trainee,
            isActive = true,
            pattern = model.patternSchedule.pattern,
            createdOn = dateService.getCurrentDateTime()
        )
    }

    override fun toTrainingScheduleResponse(trainingSchedule: TrainingSchedule): TrainingScheduleResponse =
        trainingMapper.toPatternTrainingScheduleResponse(trainingSchedule)

    override fun getNextWorkoutDate(
        trainingSchedule: TrainingSchedule,
        workoutHistory: List<LocalDate>,
        currentDate: LocalDate
    ): LocalDate {
        if (workoutHistory.isEmpty()) {
            return currentDate
        }

        val pattern = trainingSchedule.pattern!!

        val currentPattern = getCurrentPattern(pattern, workoutHistory, currentDate)
        val currScheduleStage = getScheduleProgressIndex(pattern, currentPattern)
        val daysTillNextWorkout = findDaysUntilNextWorkout(pattern, currScheduleStage)

        var nextWorkoutDate = currentDate.plusDays(daysTillNextWorkout.toLong())

        val matchingWorkoutInHistory = getNextWorkoutInHistory(
            nextWorkoutDate, workoutHistory, 0, workoutHistory.size - 1
        )

        if (nextWorkoutDate == matchingWorkoutInHistory) {
            nextWorkoutDate = getNextWorkoutDate(trainingSchedule, workoutHistory, currentDate.plusDays(1))
        }
        return nextWorkoutDate
    }

    private fun getNextWorkoutInHistory(
        target: LocalDate, workoutHistory: List<LocalDate>,
        left: Int, right: Int
    ): LocalDate? {
        if (left > right) {
            return null
        }
        val midIndex = left + (right - left) / 2
        val middleWorkout = workoutHistory[midIndex]
        return if (target == middleWorkout) {
            middleWorkout
        } else if (target.isBefore(middleWorkout)) {
            getNextWorkoutInHistory(target, workoutHistory, left, midIndex - 1)
        } else {
            getNextWorkoutInHistory(target, workoutHistory, midIndex + 1, right)
        }
    }

    fun getAdjacentWorkoutInHistory(
        targetWorkout: LocalDate, workoutHistory: List<LocalDate>,
        left: Int, right: Int, getNext: Boolean
    ): LocalDate? {
        if (left > right) {
            return null
        }

        val midIndex = left + (right - left) / 2
        val middleWorkout = workoutHistory[midIndex]
        return if (targetWorkout == middleWorkout) {
            if (getNext && midIndex < workoutHistory.size - 1) {
                workoutHistory[midIndex + 1]
            } else if (!getNext && midIndex > 0) {
                workoutHistory[midIndex - 1]
            } else {
                null
            }
        } else if (targetWorkout.isBefore(middleWorkout)) {
            getAdjacentWorkoutInHistory(targetWorkout, workoutHistory, left, midIndex - 1, getNext)
        } else {
            getAdjacentWorkoutInHistory(targetWorkout, workoutHistory, midIndex + 1, right, getNext)
        }
    }

    private fun findDaysUntilNextWorkout(pattern: String, currScheduleStage: Int): Int {
        var counterUntilNextWorkout = 0
        var i = currScheduleStage + 1
        while (i <= pattern.length) {
            if (i == pattern.length) i = 0
            if (pattern[i] == '0') counterUntilNextWorkout++ else break
            i++
        }

        return counterUntilNextWorkout
    }

    private fun getScheduleProgressIndex(pattern: String, currentPattern: String): Int {
        var matchedIndex = -1
        for (i in currentPattern.length - 1 downTo 0) {
            val matchingPattern = currentPattern.substring(i)
            val matchesStart = pattern.startsWith(matchingPattern)
            if (matchesStart) {
                matchedIndex = matchingPattern.length - 1
            }
        }

        return matchedIndex
    }

    private fun getCurrentPattern(pattern: String, workoutHistory: List<LocalDate>, currentDate: LocalDate): String {
        val today = currentDate.minusDays(1)
        val workoutHistorySize = workoutHistory.size
        val scheduleLength = pattern.length
        val currPattern = StringBuilder()
        var lastWorkoutIndex = workoutHistorySize - 1
        for (i in 0 until scheduleLength) {
            val currDate = today.minusDays(i.toLong())
            var lastWorkout: LocalDate? = workoutHistory[lastWorkoutIndex]
            if (lastWorkout!!.isAfter(currDate)) {
                lastWorkout = getWorkoutBeforeDate(currDate, workoutHistory)
                lastWorkoutIndex = getLastWorkoutIndex(lastWorkout, workoutHistory)
                if (lastWorkout == null) break
            }
            if (lastWorkout == currDate) {
                currPattern.append("1")
                lastWorkoutIndex--
            } else {
                currPattern.append("0")
            }
            if (lastWorkoutIndex < 0) break
        }
        return currPattern.reverse().toString()
    }

    private fun getLastWorkoutIndex(lastWorkout: LocalDate?, workoutHistory: List<LocalDate>): Int {
        for (i in workoutHistory.indices.reversed()) {
            val currDate = workoutHistory[i]
            if (currDate == lastWorkout) return i
        }
        return -1
    }

    private fun getWorkoutBeforeDate(targetDate: LocalDate, workoutHistory: List<LocalDate>): LocalDate? {
        for (i in workoutHistory.indices.reversed()) {
            val currDate = workoutHistory[i]
            if (currDate.isBefore(targetDate)) return currDate
        }
        return null
    }
}
