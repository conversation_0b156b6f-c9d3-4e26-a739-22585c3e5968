package com.myprogressguru.trainingservice.service.impl.notifications

import com.myprogressguru.notificationsutils.service.model.SendNotificationRequest
import com.myprogressguru.trainingservice.entity.Trainee
import com.myprogressguru.trainingservice.enumeration.NotificationType
import java.time.LocalDate

class EnteredBodyMeasurementsNotification(
    private val trainee: Trainee,
    private val date: LocalDate
) : BaseTrainerNotification(trainee) {

    override fun buildRequest(): SendNotificationRequest = SendNotificationRequest(
        userId = trainee.trainer.id,
        type = NotificationType.ENTERED_BODY_MEASUREMENTS.name,
        title = "${trainee.firstName} ${trainee.lastName} - Body Measurements!",
        body = "${date}",
        data = traineeMapOf(
            "date" to date.toString()
        )
    )
}

