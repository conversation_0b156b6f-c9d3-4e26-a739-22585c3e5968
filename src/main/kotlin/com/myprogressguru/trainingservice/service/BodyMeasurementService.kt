package com.myprogressguru.trainingservice.service

import com.myprogressguru.trainingservice.web.payload.body.measurement.BodyMeasurementRecordPutRequest
import com.myprogressguru.trainingservice.web.payload.body.measurement.BodyMeasurementRecordResponse
import org.springframework.data.domain.Page
import java.time.LocalDate

interface BodyMeasurementService {

    fun getRecord(traineeId: String, date: LocalDate): BodyMeasurementRecordResponse?

    fun putRecord(
        traineeId: String,
        model: BodyMeasurementRecordPutRequest
    ): BodyMeasurementRecordResponse

    fun deleteRecord(traineeId: String, date: LocalDate)

    fun getHistory(traineeId: String, page: Int): Page<BodyMeasurementRecordResponse>

    fun getBodyMeasurementRecordsMap(
        traineeId: String,
        dates: List<LocalDate>
    ): Map<LocalDate, BodyMeasurementRecordResponse>
}