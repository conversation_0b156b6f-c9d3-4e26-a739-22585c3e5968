package com.myprogressguru.trainingservice.service

import com.myprogressguru.trainingservice.entity.Note
import com.myprogressguru.trainingservice.web.payload.note.NotePhotoRequest
import com.myprogressguru.trainingservice.web.payload.note.NoteRequest

interface NoteService {

    fun create(model: NoteRequest): Note

    fun edit(id: String, model: NoteRequest)

    fun delete(id: String)

    fun putPhoto(id: String, model: NotePhotoRequest)
}