package com.myprogressguru.trainingservice.service

import com.myprogressguru.trainingservice.web.payload.training.workout.*

interface WorkoutService {

    fun getAll(
        trainerId: String,
        trainingSplitId: String?
    ): List<WorkoutResponse>

    fun edit(id: String, model: WorkoutEditRequest)

    fun copy(id: String, trainingSplitId: String?): WorkoutResponse

    fun create(model: WorkoutCreateRequest): WorkoutResponse

    fun delete(id: String)

    fun addExercise(id: String, model: WorkoutExerciseCreateRequest): WorkoutExerciseResponse

    fun removeExercise(id: String, exerciseId: String)

    fun editExercise(
        id: String,
        exerciseId: String,
        model: WorkoutExerciseCreateRequest
    ): WorkoutExerciseResponse

    fun editExercisesOrder(id: String, model: WorkoutExercisesOrderEditRequest)

}