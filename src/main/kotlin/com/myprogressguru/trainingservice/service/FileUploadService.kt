package com.myprogressguru.trainingservice.service

import com.myprogressguru.storageutils.service.model.StorageObjectResponse
import com.myprogressguru.storageutils.web.payload.StorageObjectUploadUrlRequest
import com.myprogressguru.trainingservice.web.payload.body.photo.BodyPhotoUploadUrlRequest

interface FileUploadService {

    fun getBodyPhotoUploadUrl(model: BodyPhotoUploadUrlRequest): StorageObjectResponse

    fun getSetRecordVideoUploadUrl(setRecordId: String, model: StorageObjectUploadUrlRequest): StorageObjectResponse

    fun getNotePhotoUploadUrl(noteId: String, model: StorageObjectUploadUrlRequest): StorageObjectResponse

}