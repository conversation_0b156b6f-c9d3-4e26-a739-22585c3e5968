package com.myprogressguru.trainingservice.service

import com.myprogressguru.trainingservice.web.payload.info.Stats
import com.myprogressguru.trainingservice.web.payload.info.ValueExtractor
import java.time.LocalDate

interface StatsRetrievalStrategy<T : ValueExtractor> {

    fun retrieve(model: StatsRetrievalRequest): Stats<T>
}

data class StatsRetrievalRequest(
    val traineeId: String,
    val endDate: LocalDate,
    val startDate: LocalDate? = null
)