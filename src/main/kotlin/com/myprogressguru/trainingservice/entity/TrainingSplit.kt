package com.myprogressguru.trainingservice.entity

import com.myprogressguru.trainingservice.entity.projection.TrainerOptionalTraineeResource
import java.time.LocalDate
import jakarta.persistence.*

@Entity
@Table(name = "training_splits")
data class TrainingSplit(

    @Column(nullable = false)
    var name: String,

    @ManyToOne
    override var trainee: Trainee? = null,

    @ManyToOne
    override var trainer: Trainer,

    @Column(nullable = false)
    var createdOn: LocalDate,

    @Column(nullable = false)
    @get:JvmName("getIsActive")
    @set:JvmName("setIsActive")
    var isActive: Boolean = false,

    @OrderBy("orderNumber")
    @OneToMany(mappedBy = "trainingSplit", cascade = [CascadeType.ALL])
    var workouts: MutableList<Workout> = mutableListOf()

) : BaseEntity(), TrainerOptionalTraineeResource
