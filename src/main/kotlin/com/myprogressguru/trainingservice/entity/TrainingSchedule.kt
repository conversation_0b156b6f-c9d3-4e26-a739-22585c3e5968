package com.myprogressguru.trainingservice.entity

import com.myprogressguru.trainingservice.enumeration.TrainingScheduleType
import java.time.ZonedDateTime
import jakarta.persistence.*

@Entity
@Table(name = "training_schedules")
data class TrainingSchedule(

    @Enumerated(EnumType.STRING)
    var type: TrainingScheduleType,

    @Column(nullable = false)
    @get:JvmName("getIsActive")
    @set:JvmName("setIsActive")
    var isActive: Boolean = true,

    @Column
    var pattern: String? = null,

    @Column
    var monday: Boolean? = null,

    @Column
    var tuesday: Boolean? = null,

    @Column
    var wednesday: Boolean? = null,

    @Column
    var thursday: Boolean? = null,

    @Column
    var friday: Boolean? = null,

    @Column
    var saturday: Boolean? = null,

    @Column
    var sunday: Boolean? = null,

    @ManyToOne
    var trainee: Trainee,

    @Column(nullable = false)
    var createdOn: ZonedDateTime,

    ) : BaseEntity()
