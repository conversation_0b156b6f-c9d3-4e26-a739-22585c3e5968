package com.myprogressguru.trainingservice.entity

import jakarta.persistence.*

@Entity
@Table(name = "workout_exercise_set_record_videos")
data class WorkoutExerciseSetRecordVideo(

    var url: String,

    @OrderBy("createdOn")
    @OneToMany(cascade = [CascadeType.ALL], orphanRemoval = true)
    @JoinTable(
        name = "workout_exercise_set_record_videos_notes",
        joinColumns = [JoinColumn(name = "workout_exercise_set_record_video_id", referencedColumnName = "id")],
        inverseJoinColumns = [JoinColumn(name = "note_id", referencedColumnName = "id")]
    )
    var notes: MutableList<Note> = mutableListOf(),

): BaseEntity()