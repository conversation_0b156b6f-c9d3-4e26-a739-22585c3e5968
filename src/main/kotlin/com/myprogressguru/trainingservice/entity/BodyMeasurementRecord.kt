package com.myprogressguru.trainingservice.entity


import com.myprogressguru.trainingservice.entity.projection.TraineeResource
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.ManyToOne
import jakarta.persistence.Table
import java.time.LocalDate

@Entity
@Table(name = "body_measurement_records")
data class BodyMeasurementRecord(

    @Column
    var chestInCm: Double?,

    @Column
    var bicepsInCm: Double?,

    @Column
    var legInCm: Double?,

    @Column
    var waistInCm: Double?,

    @Column
    var hipsInCm: Double?,

    var date: LocalDate,

    @ManyToOne
    override var trainee: Trainee

) : BaseEntity(), TraineeResource
