package com.myprogressguru.trainingservice.entity

import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.ManyToOne
import jakarta.persistence.Table
import java.time.LocalTime
import java.time.ZonedDateTime

@Entity
@Table(name = "training_time_schedules")
data class TrainingTimeSchedule(

    @Column(nullable = false)
    @get:JvmName("getIsActive")
    @set:JvmName("setIsActive")
    var isActive: Boolean = true,

    var monday: LocalTime? = null,

    var tuesday: LocalTime? = null,

    var wednesday: LocalTime? = null,

    var thursday: LocalTime? = null,

    var friday: LocalTime? = null,

    var saturday: LocalTime? = null,

    var sunday: LocalTime? = null,

    @ManyToOne
    var trainee: Trainee,

    @Column(nullable = false)
    var createdOn: ZonedDateTime,

    ) : BaseEntity()
