package com.myprogressguru.trainingservice.entity

import com.myprogressguru.trainingservice.entity.projection.TraineeResource
import java.time.LocalDate
import java.time.ZonedDateTime
import jakarta.persistence.*

@Entity
@Table(
    name = "scheduled_rest_days",
    uniqueConstraints = [
        UniqueConstraint(
            name = "scheduled_rest_days_unique_idx",
            columnNames = ["rest_on", "trainee_id"]
        ),
    ]
)
data class ScheduledRestDay(

    @Column(name = "rest_on", nullable = false)
    var restOn: LocalDate,

    @Column(nullable = false)
    var createdOn: ZonedDateTime,

    @Column(nullable = false)
    var reason: String,

    @ManyToOne()
    override var trainee: Trainee

) : BaseEntity(), TraineeResource
