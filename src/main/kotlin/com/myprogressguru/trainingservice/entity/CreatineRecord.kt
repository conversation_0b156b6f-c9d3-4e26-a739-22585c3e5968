package com.myprogressguru.trainingservice.entity

import com.myprogressguru.trainingservice.entity.projection.TraineeResource
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.ManyToOne
import jakarta.persistence.Table
import java.time.LocalDate

@Entity
@Table(name = "creatine_records")
data class CreatineRecord(

    @Column(nullable = false)
    var startedOn: LocalDate,

    @Column(nullable = false)
    var endedOn: LocalDate,

    @ManyToOne
    override var trainee: Trainee,

    ) : BaseEntity(), TraineeResource
