package com.myprogressguru.trainingservice.entity

import com.myprogressguru.trainingservice.entity.projection.TrainerResource
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.ManyToOne
import jakarta.persistence.Table

@Entity
@Table(name = "courses")
data class Course(

    @Column(nullable = false)
    var name: String,

    @Column(nullable = false)
    var url: String,

    @ManyToOne
    override var trainer: Trainer

) : BaseEntity(), TrainerResource
