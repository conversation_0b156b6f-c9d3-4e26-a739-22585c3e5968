package com.myprogressguru.trainingservice.entity

import com.myprogressguru.trainingservice.entity.projection.TraineeResource
import com.myprogressguru.trainingservice.enumeration.PhysicalActivityLevel
import java.time.ZonedDateTime
import jakarta.persistence.*

@Entity
@Table(name = "fitness_states")
data class FitnessState(

    @ManyToOne
    override var trainee: Trainee,

    @Column(nullable = false)
    var createdOn: ZonedDateTime,

    @Column(nullable = false)
    var bodyWeightInKg: Double,

    @Column(nullable = false)
    var bodyFatPercentage: Double,

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    var physicalActivityLevel: PhysicalActivityLevel,

    @Column(nullable = false)
    var physicalActivityFactor: Double,

    @Column(nullable = false)
    var thermicEffectOfFoodFactor: Double,

    @Column(nullable = false)
    var trainingSessionDurationInMin: Int,

    @Column(nullable = false)
    var energyBalanceFactor: Double,

    @Column(nullable = false)
    var trainingDaysPerWeek: Int,

    @Column(nullable = false)
    var leanMassInKg: Double,

    @Column(nullable = false)
    var basalMetabolicRate: Int,

    @Column(nullable = false)
    var trainingEnergyExpenditure: Int,

    @Column(nullable = false)
    var restingEnergyExpenditure: Int,

    @Column(nullable = false)
    var energyExpenditureOnTrainingDays: Int,

    @Column(nullable = false)
    var maintenanceEnergyIntake: Int,

    @Column(nullable = false)
    var averageTargetEnergyIntake: Int,

    ) : BaseEntity(), TraineeResource
