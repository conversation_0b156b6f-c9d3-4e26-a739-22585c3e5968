package com.myprogressguru.trainingservice.entity

import com.myprogressguru.trainingservice.entity.projection.TrainerResource
import com.myprogressguru.trainingservice.enumeration.Gender
import jakarta.persistence.*
import java.time.LocalDate
import java.time.ZonedDateTime

@Entity
@Table(name = "trainee_invitations")
data class TraineeInvitation(

    @ManyToOne
    override var trainer: Trainer,

    @Column
    var instagram: String? = null,

    @Column
    var email: String? = null,

    @Column
    var firstName: String? = null,

    @Column
    var lastName: String? = null,

    @Enumerated(EnumType.STRING)
    var gender: Gender? = null,

    @Column
    var heightInCm: Int? = null,

    @Column
    var birthdate: LocalDate? = null,

    @Column
    var occupation: String? = null,

    @Column
    var createdOn: ZonedDateTime,

    @Column
    var filled: Boolean = false,

    @Column
    var preApproved: Boolean = false,

    @Column
    var manyChatContactId: Long? = null

) : BaseEntity(), TrainerResource
