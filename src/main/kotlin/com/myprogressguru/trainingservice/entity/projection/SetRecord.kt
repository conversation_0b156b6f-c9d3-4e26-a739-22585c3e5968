package com.myprogressguru.trainingservice.entity.projection

interface SetRecord {
    val orderNumber: Int

    val reps: Double?

    val weightInKg: Double?
}

fun List<SetRecord>.convertSetRecordsToText(): String? {
    val setRecords = this
    if (setRecords.isEmpty()) {
        return null
    }

    val sortedArray = setRecords.sortedBy { it.orderNumber }.filter { it.weightInKg != null && it.reps != null }
    if (sortedArray.isEmpty()) {
        return null
    }

    val result = mutableListOf<String>()
    var lastWeight = sortedArray[0].weightInKg
    var repsList = mutableListOf(sortedArray[0].reps)

    for (i in 1 until sortedArray.size) {
        if (sortedArray[i].weightInKg == lastWeight) {
            repsList.add(sortedArray[i].reps)
        } else {
            result.add(
                "${
                    lastWeight?.toBigDecimal()?.stripTrailingZeros()?.toPlainString()
                }x${
                    repsList.joinToString(",") {
                        it?.toBigDecimal()?.stripTrailingZeros()?.toPlainString() ?: "0"
                    }
                }"
            )
            lastWeight = sortedArray[i].weightInKg
            repsList = mutableListOf(sortedArray[i].reps)
        }
    }

    // Add the last group
    result.add(
        "${
            lastWeight?.toBigDecimal()?.stripTrailingZeros()?.toPlainString()
        }x${repsList.joinToString(",") { it?.toBigDecimal()?.stripTrailingZeros()?.toPlainString() ?: "0" }}"
    )

    return result.joinToString("; ")
}

fun List<SetRecord>.calculateScore(): Long? {
    if (isEmpty()) {
        return null
    }

    val sanitizedSetRecords = this.map {
        if (it.weightInKg == 0.0) {
            object : SetRecord by it {
                override val weightInKg = 0.1
            }
        } else {
            it
        }
    }

    val maxWeight = sanitizedSetRecords
        .mapNotNull { it.weightInKg }
        .maxOrNull() ?: return null

    val maxWeightSet = sanitizedSetRecords
        .filter { it.weightInKg == maxWeight }
        .sortedWith(compareByDescending<SetRecord> { it.reps }.thenBy { it.orderNumber })
        .firstOrNull()

    val maxWeightReps = maxWeightSet?.reps ?: 0.0

    val otherSets = sanitizedSetRecords.filterNot { it.orderNumber == maxWeightSet?.orderNumber }

    val sumOfOtherReps = otherSets.sumOf { (it.reps ?: 0.0) * (it.weightInKg ?: 0.0) }

    return ((maxWeight * 1000) + (maxWeightReps * 700) + sumOfOtherReps).toLong()
}

