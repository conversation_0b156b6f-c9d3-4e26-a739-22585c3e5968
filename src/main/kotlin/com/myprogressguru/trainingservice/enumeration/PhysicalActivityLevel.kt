package com.myprogressguru.trainingservice.enumeration

enum class PhysicalActivityLevel {

    SEDENTARY {
        override fun getMultiplier(gender: Gender) = 1.00
    },
    LOW_ACTIVE {
        override fun getMultiplier(gender: Gender) = if (gender == Gender.MALE) 1.11 else 1.12
    },
    ACTIVE {
        override fun getMultiplier(gender: Gender) = if (gender == Gender.MALE) 1.25 else 1.27
    },
    VERT_ACTIVE {
        override fun getMultiplier(gender: Gender) = if (gender == Gender.MALE) 1.48 else 1.45
    };

    abstract fun getMultiplier(gender: Gender): Double
}
