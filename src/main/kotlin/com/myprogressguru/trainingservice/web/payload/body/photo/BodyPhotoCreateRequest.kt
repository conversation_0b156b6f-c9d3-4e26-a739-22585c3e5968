package com.myprogressguru.trainingservice.web.payload.body.photo

import com.myprogressguru.commonutils.annotation.ValidEnum
import com.myprogressguru.trainingservice.enumeration.BodyPhotoType
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import java.time.LocalDate

data class BodyPhotoCreateRequest(

    @field:NotBlank
    val traineeId: String?,

    @field:NotNull
    val date: LocalDate?,

    @field:ValidEnum
    val type: BodyPhotoType?,

    @field:NotBlank
    val photoId: String?,

)