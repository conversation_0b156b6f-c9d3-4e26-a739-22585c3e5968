package com.myprogressguru.trainingservice.web.payload.exercise

data class TrainingVolumeResponse(

    val chest: Double,

    val frontDelts: Double,

    val lateralDelts: Double,

    val biceps: Double,

    val abs: Double,

    val upperTraps: Double,

    val rearDelts: Double,

    val triceps: Double,

    val midTraps: Double,

    val lowerTraps: Double,

    val lats: Double,

    val erectors: Double,

    val quads: Double,

    val calves: Double,

    val glutes: Double,

    val hamstrings: Double,

    val forearms: Double,

    ) {

    operator fun plus(other: TrainingVolumeResponse): TrainingVolumeResponse {
        return TrainingVolumeResponse(
            chest + other.chest,
            frontDelts + other.frontDelts,
            lateralDelts + other.lateralDelts,
            biceps + other.biceps,
            abs + other.abs,
            upperTraps + other.upperTraps,
            rearDelts + other.rearDelts,
            triceps + other.triceps,
            midTraps + other.midTraps,
            lowerTraps + other.lowerTraps,
            lats + other.lats,
            erectors + other.erectors,
            quads + other.quads,
            calves + other.calves,
            glutes + other.glutes,
            hamstrings + other.hamstrings,
            forearms + other.forearms
        )
    }

    operator fun times(sets: Int): TrainingVolumeResponse {
        return TrainingVolumeResponse(
            chest * sets,
            frontDelts * sets,
            lateralDelts * sets,
            biceps * sets,
            abs * sets,
            upperTraps * sets,
            rearDelts * sets,
            triceps * sets,
            midTraps * sets,
            lowerTraps * sets,
            lats * sets,
            erectors * sets,
            quads * sets,
            calves * sets,
            glutes * sets,
            hamstrings * sets,
            forearms * sets
        )
    }
}
