package com.myprogressguru.trainingservice.web.payload.trainee

import com.myprogressguru.commonutils.annotation.ValidEnum
import com.myprogressguru.trainingservice.enumeration.Gender
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Positive
import java.time.LocalDate

data class TraineeCreateRequest(

    @field:Email
    @field:NotNull
    val email: String?,

    @field:NotNull
    val firstName: String?,

    @field:NotNull
    val lastName: String?,

    @field:ValidEnum
    val gender: Gender?,

    @field:Positive
    val heightInCm: Int?,

    @field:NotNull
    val birthdate: LocalDate?,

    @field:NotNull
    val occupation: String?,

    val instagram: String?,

    val manyChatContactId: Long?

)
