package com.myprogressguru.trainingservice.web.payload.training.workout

import com.myprogressguru.trainingservice.enumeration.SetsType
import com.myprogressguru.trainingservice.web.payload.exercise.ExerciseResponse
import com.myprogressguru.trainingservice.web.payload.exercise.TrainingVolumeResponse

data class WorkoutExerciseResponse(

    val id: String,

    val mainOrderNumber: Int,

    val subOrderNumber: Int?,

    val sets: Int,

    val setsType: SetsType,

    val repsTargetLowerBound: Int,

    val repsTargetUpperBound: Int?,

    val exercise: ExerciseResponse,

    var trainingVolume: TrainingVolumeResponse?

)
