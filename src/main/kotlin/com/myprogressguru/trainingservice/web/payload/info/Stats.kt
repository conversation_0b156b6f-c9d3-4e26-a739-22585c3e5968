package com.myprogressguru.trainingservice.web.payload.info

import com.myprogressguru.commonutils.util.numeric.roundTo
import java.time.LocalDate

class Stats<T : ValueExtractor>(
    val data: List<T>,
    val startDate: LocalDate,
    val endDate: LocalDate,
    val prevEndDate: LocalDate,
    val nextEndDate: LocalDate,
) {
    val average: Double? = if (data.isNotEmpty()) {
        val nonNullValues = data.mapNotNull { it.extractValue() }
        if (nonNullValues.isNotEmpty()) {
            (nonNullValues.sum() / nonNullValues.size).roundTo(2)
        } else {
            null
        }
    } else {
        null
    }

    fun <R : ValueExtractor> map(transform: (T) -> R): Stats<R> {
        val newData = data.map(transform)
        return Stats(newData, startDate, endDate, prevEndDate, nextEndDate)
    }
}

interface ValueExtractor {
    fun extractValue(): Double?
}

