package com.myprogressguru.trainingservice.web.payload.training.workout

import com.myprogressguru.trainingservice.web.payload.note.NoteResponse

data class WorkoutExerciseRecordHistoryResponse(

    val setRecords: List<WorkoutExerciseSetRecordResponse>,

    var setRecordsAsText: String?,

    val workoutRecord: WorkoutRecordInfoResponse,

    val notes: List<NoteResponse> = listOf(),

    var hasVideo: Boolean = false,

    @get:JvmName("getIsSkipped")
    val isSkipped: Boolean = false,

    val personalRecordsCount: Int = 0

)
