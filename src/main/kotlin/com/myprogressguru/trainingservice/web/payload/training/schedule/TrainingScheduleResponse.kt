package com.myprogressguru.trainingservice.web.payload.training.schedule

import com.fasterxml.jackson.annotation.JsonInclude
import com.myprogressguru.trainingservice.enumeration.TrainingScheduleType

@JsonInclude(JsonInclude.Include.NON_NULL)
data class TrainingScheduleResponse(

    val type: TrainingScheduleType,

    var patternSchedule: PatternTrainingScheduleResponse? = null,

    var daysOfTheWeekSchedule: DaysOfTheWeekTrainingScheduleResponse? = null

)
