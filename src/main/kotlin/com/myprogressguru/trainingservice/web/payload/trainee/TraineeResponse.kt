package com.myprogressguru.trainingservice.web.payload.trainee

import com.myprogressguru.trainingservice.enumeration.Gender
import com.myprogressguru.trainingservice.web.payload.trainee.group.TraineeGroupResponse
import java.time.LocalDate

data class TraineeResponse(

    val id: String,

    val firstName: String,

    val lastName: String,

    val gender: Gender,

    val heightInCm: Int,

    val birthdate: LocalDate,

    val pictureUrl: String?,

    val occupation: String,

    val instagram: String?,

    val groups: List<TraineeGroupResponse> = listOf()

)
