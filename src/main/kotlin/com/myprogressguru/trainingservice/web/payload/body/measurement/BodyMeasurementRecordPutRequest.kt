package com.myprogressguru.trainingservice.web.payload.body.measurement

import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import java.time.LocalDate

data class BodyMeasurementRecordPutRequest(

    @field:Min(1)
    val chestInCm: Double?,

    @field:Min(1)
    val bicepsInCm: Double?,

    @field:Min(1)
    val legInCm: Double?,

    @field:Min(1)
    val waistInCm: Double?,

    @field:Min(1)
    val hipsInCm: Double?,

    @field:NotNull
    val date: LocalDate?

)