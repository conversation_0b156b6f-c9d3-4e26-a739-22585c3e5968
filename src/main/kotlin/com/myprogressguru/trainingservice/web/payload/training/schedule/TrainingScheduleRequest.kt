package com.myprogressguru.trainingservice.web.payload.training.schedule

import com.myprogressguru.commonutils.annotation.ValidEnum
import com.myprogressguru.trainingservice.enumeration.TrainingScheduleType
import jakarta.validation.Valid
import jakarta.validation.constraints.NotBlank

data class TrainingScheduleRequest(

    @field:ValidEnum
    val type: TrainingScheduleType?,

    @field:Valid
    val patternSchedule: PatternTrainingScheduleRequest?,

    @field:Valid
    val daysOfTheWeekSchedule: DaysOfTheWeekTrainingScheduleRequest?,

    @field:NotBlank
    val traineeId: String?

)
