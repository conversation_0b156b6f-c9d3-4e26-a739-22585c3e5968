package com.myprogressguru.trainingservice.web.controller

import com.myprogressguru.commonutils.annotation.DateRequestParam
import com.myprogressguru.trainingservice.service.BodyMeasurementService
import com.myprogressguru.trainingservice.web.payload.body.measurement.BodyMeasurementRecordPutRequest
import com.myprogressguru.trainingservice.web.payload.body.measurement.BodyMeasurementRecordResponse
import jakarta.validation.Valid
import org.springframework.data.domain.Page
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*
import java.time.LocalDate

@RestController
@RequestMapping("/v1/body-measurement-records")
class BodyMeasurementRecordController(
    private val bodyMeasurementService: BodyMeasurementService
) {

    @PreAuthorize("@authService.isAdminOrTrainerOfOrTrainee(#traineeId)")
    @GetMapping
    fun getRecord(
        @RequestParam(required = true) traineeId: String,
        @DateRequestParam(required = true) date: LocalDate
    ): BodyMeasurementRecordResponse? = bodyMeasurementService.getRecord(traineeId, date)

    @PreAuthorize("@authService.isAdminOrTrainerOfOrTrainee(#traineeId)")
    @PutMapping
    fun putRecord(
        @RequestParam(required = true) traineeId: String,
        @Valid @RequestBody model: BodyMeasurementRecordPutRequest
    ): BodyMeasurementRecordResponse = bodyMeasurementService.putRecord(traineeId, model)

    @PreAuthorize("@authService.isAdminOrTrainerOfOrTrainee(#traineeId)")
    @DeleteMapping
    fun deleteRecord(
        @RequestParam(required = true) traineeId: String,
        @DateRequestParam(required = true) date: LocalDate
    ) = bodyMeasurementService.deleteRecord(traineeId, date)

    @PreAuthorize("@authService.isAdminOrTrainerOfOrTrainee(#traineeId)")
    @GetMapping("/history")
    fun getHistory(
        @RequestParam(required = true) traineeId: String,
        @RequestParam(required = false, defaultValue = "1") page: Int
    ): Page<BodyMeasurementRecordResponse> =
        bodyMeasurementService.getHistory(traineeId, page)

}