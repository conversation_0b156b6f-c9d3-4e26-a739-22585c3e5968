package com.myprogressguru.trainingservice.web.controller

import com.myprogressguru.keycloakutils.annotation.UserId
import com.myprogressguru.keycloakutils.constant.AuthConstants.Authorization.ADMIN
import com.myprogressguru.keycloakutils.service.model.AccountData
import com.myprogressguru.trainingservice.service.*
import com.myprogressguru.trainingservice.web.payload.course.CourseResponse
import com.myprogressguru.trainingservice.web.payload.gym.DefaultGymRequest
import com.myprogressguru.trainingservice.web.payload.info.OptionalIdentifiableEntitiesRequest
import com.myprogressguru.trainingservice.web.payload.trainee.*
import com.myprogressguru.trainingservice.web.payload.trainer.ChangeTrainerRequest
import jakarta.validation.Valid
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.domain.Page
import org.springframework.http.HttpStatus
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*
import java.time.LocalDate

@RestController
@RequestMapping("/v1/trainees")
class TraineeController(
    private val traineeService: TraineeService,
    private val traineeInvitationService: TraineeInvitationService,
    private val authService: AuthService,
    private val summaryService: SummaryService,
    private val courseService: CourseService
) {
    @GetMapping("/any")
    fun anyUserExists(): Boolean = authService.anyUserExists()

    @PreAuthorize("@keycloakService.requestFromAnyServiceAccount")
    @GetMapping("/ids")
    fun getAllIds(@RequestParam trainerId: String?): List<String> = traineeService.getAllIds(trainerId)

    @PreAuthorize("@keycloakService.isRequesterAdminOrRequesterIdMatchesUserId(#trainerId)")
    @GetMapping
    fun getAll(
        @RequestParam(required = false, defaultValue = "1") page: Int,
        @RequestParam(required = true) trainerId: String,
        @RequestParam(required = false) search: String?,
        @RequestParam(required = false, defaultValue = "") fields: List<String>,
        @RequestParam(required = false) groupId: String?,
    ): Page<DetailedTraineeResponse> = traineeService.getAll(
        AllTraineesRequest(
            page = page,
            trainerId = trainerId,
            search = search,
            fields = fields,
            groupId = groupId
        )
    )

    @PreAuthorize("@authService.isAdminOrTrainerOfOrTrainee(#id)")
    @GetMapping("/{id}")
    fun get(
        @PathVariable id: String
    ): TraineeResponse = traineeService.get(id)

    @PreAuthorize("@authService.isAdminOrTrainerOfOrTrainee(#id)")
    @GetMapping("/{id}/account-data")
    fun getAccountData(
        @PathVariable id: String
    ): AccountData = authService.getAccountData(id)

    @PreAuthorize("@authService.isAdminOrTrainerOfOrTrainee(#id)")
    @GetMapping("/{id}/fitness-states/latest")
    fun getFitnessStateByTraineeId(
        @PathVariable id: String
    ): FitnessStateResponse = traineeService.getLatestFitnessState(id)

    @PreAuthorize("@authService.isAdminOrTrainerOf(#id)")
    @PostMapping("/{id}/fitness-states")
    fun createFitnessState(
        @PathVariable id: String,
        @Valid @RequestBody model: FitnessStateCreateRequest
    ): FitnessStateResponse = traineeService.createFitnessState(id, model)

    @PreAuthorize("@authService.isAdminOrTrainerOf(#id)")
    @PutMapping("/{id}/groups")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun putGroups(
        @PathVariable id: String,
        @Valid @RequestBody model: OptionalIdentifiableEntitiesRequest
    ) = traineeService.putGroups(id, model)

    @PreAuthorize("@authService.isAdminOrTrainerOf(#id)")
    @PutMapping("/{id}/courses")
    fun putCourses(
        @PathVariable id: String,
        @Valid @RequestBody model: OptionalIdentifiableEntitiesRequest
    ): List<CourseResponse> = courseService.putCourses(id, model)

    @PreAuthorize("@authService.isAdminOrTrainerOfOrTrainee(#id)")
    @PutMapping("/{id}/default-gym")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun putDefaultGym(
        @PathVariable id: String,
        @Valid @RequestBody model: DefaultGymRequest
    ) = traineeService.putDefaultGym(id, model)

    @PreAuthorize("hasRole('ROLE_TRAINER') OR NOT @authService.anyUserExists()")
    @ResponseStatus(HttpStatus.CREATED)
    @PostMapping
    fun create(
        @Valid @RequestBody model: TraineeCreateRequest,
        @UserId userId: String?,
    ): TraineeResponse = traineeService.create(model, userId)

    @PreAuthorize("@authService.isAdminOrTrainerOf(#id)")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @PostMapping("/{id}/activate-account")
    fun activateAccount(@PathVariable id: String) = traineeService.activateAccount(id)

    @PreAuthorize(ADMIN)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @PutMapping("/{id}/trainer")
    fun changeTrainer(
        @PathVariable id: String,
        @Valid @RequestBody model: ChangeTrainerRequest
    ) = traineeService.changeTrainer(id, model.trainerId!!)

    @PreAuthorize("@authService.isAdminOrTrainerOf(#id)")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @PostMapping("/{id}/promote")
    fun promoteToPro(
        @PathVariable id: String
    ) = traineeService.promoteToPro(id)

    @PreAuthorize("@authService.isAdminOrTrainerOf(#id)")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @PostMapping("/{id}/demote")
    fun demoteFromPro(
        @PathVariable id: String
    ) = traineeService.demoteFromPro(id)

    @PreAuthorize("@authService.isAdminOrTrainerOfOrTrainee(#id)")
    @GetMapping("/{id}/summary/{date}")
    fun getDaySummary(
        @PathVariable id: String,
        @PathVariable date: LocalDate
    ) = summaryService.getDaySummary(id, date)

    @PreAuthorize("@authService.isAdminOrTrainerOf(#id)")
    @GetMapping("/{id}/summary")
    fun getSummary(
        @PathVariable id: String
    ): String = summaryService.getSummary(id)

    @PreAuthorize("hasAnyRole('ROLE_TRAINER') AND @keycloakService.requesterIdMatchesUserId(#trainerId)")
    @GetMapping("/invitations")
    fun getAllInvitations(
        @RequestParam(required = true) trainerId: String,
    ): List<TraineeInvitationResponse> = traineeInvitationService.getAll(trainerId)

    @PreAuthorize("@authService.canCreateTraineeInvitation(#model.trainerId, #apiKey)")
    @ResponseStatus(HttpStatus.CREATED)
    @PostMapping("/invitations")
    fun createInvitation(
        @Valid @RequestBody model: TraineeInvitationCreateRequest,
        @RequestHeader("X-Api-Key", required = false) apiKey: String?,
        @Value("\${mpg.training-service.invitations-api-key") expectedApiKey: String
    ): TraineeInvitationResponse = traineeInvitationService.create(model)

    @GetMapping("/invitations/{id}")
    fun getInvitation(
        @PathVariable id: String,
    ): TraineeInvitationResponse = traineeInvitationService.get(id)

    @ResponseStatus(HttpStatus.NO_CONTENT)
    @PutMapping("/invitations/{id}")
    fun editInvitation(
        @PathVariable id: String,
        @Valid @RequestBody model: TraineeCreateRequest
    ) = traineeInvitationService.edit(id, model)

    @PreAuthorize("@authService.isAdminOrTrainerOwnerOf(@traineeInvitationRepository, #id)")
    @PostMapping("/invitations/{id}")
    fun approveInvitation(
        @PathVariable id: String,
    ): TraineeResponse = traineeInvitationService.approve(id)

    @ResponseStatus(HttpStatus.NO_CONTENT)
    @PreAuthorize("@authService.isAdminOrTrainerOwnerOf(@traineeInvitationRepository, #id)")
    @DeleteMapping("/invitations/{id}")
    fun declineInvitation(
        @PathVariable id: String,
    ) = traineeInvitationService.decline(id)
}
