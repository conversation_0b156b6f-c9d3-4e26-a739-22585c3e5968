package com.myprogressguru.trainingservice.web.controller

import com.myprogressguru.trainingservice.service.IntegrationService
import com.myprogressguru.trainingservice.web.payload.integration.IntegrationResponse
import com.myprogressguru.trainingservice.web.payload.integration.WhoopIntegrationRequest
import jakarta.validation.Valid
import org.springframework.http.HttpStatus
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*


@RestController
@RequestMapping("/v1/integrations")
class IntegrationController(
    private val integrationService: IntegrationService
) {

    @PreAuthorize("@keycloakService.requesterIdMatchesUserId(#traineeId)")
    @ResponseStatus(HttpStatus.CREATED)
    @PostMapping("/whoop")
    fun create(
        @RequestParam(required = true) traineeId: String,
        @Valid @RequestBody model: WhoopIntegrationRequest
    ): IntegrationResponse = integrationService.createWhoopIntegration(traineeId, model.code!!)

    @PreAuthorize("@authService.isAdminOrTrainerOfOrTrainee(#traineeId)")
    @GetMapping
    fun getAll(
        @RequestParam(required = true) traineeId: String,
    ): List<IntegrationResponse> = integrationService.getAll(traineeId)

}
