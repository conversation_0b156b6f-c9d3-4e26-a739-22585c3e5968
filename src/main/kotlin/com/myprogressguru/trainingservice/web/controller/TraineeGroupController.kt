package com.myprogressguru.trainingservice.web.controller

import com.myprogressguru.trainingservice.service.TraineeGroupService
import com.myprogressguru.trainingservice.web.payload.trainee.group.TraineeGroupCreateRequest
import com.myprogressguru.trainingservice.web.payload.trainee.group.TraineeGroupEditRequest
import com.myprogressguru.trainingservice.web.payload.trainee.group.TraineeGroupResponse
import org.springframework.http.HttpStatus
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/v1/trainee-groups")
class TraineeGroupController(
    private val traineeGroupService: TraineeGroupService
) {

    @PreAuthorize("@keycloakService.isRequesterAdminOrRequesterIdMatchesUserId(#trainerId)")
    @GetMapping
    fun getAll(
        @RequestParam(required = true) trainerId: String,
    ): List<TraineeGroupResponse> = traineeGroupService.getAll(trainerId)

    @PreAuthorize("@keycloakService.isRequesterAdminOrRequesterIdMatchesUserId(#model.trainerId)")
    @ResponseStatus(HttpStatus.CREATED)
    @PostMapping
    fun create(
        @RequestBody model: TraineeGroupCreateRequest
    ): TraineeGroupResponse = traineeGroupService.create(model)

    @PreAuthorize("@authService.isAdminOrTrainerOwnerOf(@traineeGroupRepository, #id)")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @PutMapping("/{id}")
    fun edit(
        @PathVariable id: String,
        @RequestBody model: TraineeGroupEditRequest
    ) = traineeGroupService.edit(id, model)

    @PreAuthorize("@authService.isAdminOrTrainerOwnerOf(@traineeGroupRepository, #id)")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @DeleteMapping("/{id}")
    fun delete(
        @PathVariable id: String,
    ) = traineeGroupService.delete(id)


}