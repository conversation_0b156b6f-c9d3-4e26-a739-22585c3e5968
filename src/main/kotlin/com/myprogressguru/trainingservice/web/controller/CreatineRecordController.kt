package com.myprogressguru.trainingservice.web.controller


import com.myprogressguru.trainingservice.service.CreatineRecordService
import com.myprogressguru.trainingservice.web.payload.creatine.CreatineRecordCreateRequest
import com.myprogressguru.trainingservice.web.payload.creatine.CreatineRecordEditRequest
import com.myprogressguru.trainingservice.web.payload.creatine.CreatineRecordResponse
import jakarta.validation.Valid
import org.springframework.http.HttpStatus
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/v1/creatine-records")
class CreatineRecordController(
    private val creatineRecordService: CreatineRecordService,
) {
    @PreAuthorize("@authService.isAdminOrTrainerOfOrTrainee(#traineeId)")
    @GetMapping
    fun getAll(
        @RequestParam(required = true) traineeId: String
    ): List<CreatineRecordResponse> = creatineRecordService.getAll(traineeId)

    @PreAuthorize("@authService.isAdminOrTrainerOfOrTrainee(#model.traineeId)")
    @ResponseStatus(HttpStatus.CREATED)
    @PostMapping
    fun create(
        @Valid @RequestBody model: CreatineRecordCreateRequest,
    ): CreatineRecordResponse = creatineRecordService.create(model)

    @PreAuthorize("@authService.isAdminOrTraineeOwnerOf(@creatineRecordRepository, #id)")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @DeleteMapping("/{id}")
    fun delete(
        @PathVariable id: String
    ) = creatineRecordService.delete(id)

    @PreAuthorize("@authService.isAdminOrTraineeOwnerOf(@creatineRecordRepository, #id)")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @PatchMapping("/{id}")
    fun edit(
        @PathVariable id: String,
        @Valid @RequestBody model: CreatineRecordEditRequest
    ) = creatineRecordService.edit(id, model)
}