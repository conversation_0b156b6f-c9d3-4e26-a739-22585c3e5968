package com.myprogressguru.trainingservice.web.controller

import com.myprogressguru.commonutils.annotation.DateRequestParam
import com.myprogressguru.trainingservice.service.WorkoutExerciseSetRecordService
import com.myprogressguru.trainingservice.web.payload.training.workout.GetPRSetRecordsRequest
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*
import java.time.LocalDate

@RestController
@RequestMapping("/v1/workout-exercise-set-records")
class WorkoutExerciseSetRecordController(
    private val workoutExerciseSetRecordService: WorkoutExerciseSetRecordService
) {

    @PreAuthorize("@authService.isAdminOrTrainerOfOrTrainee(#traineeId)")
    @GetMapping("/{id}")
    fun get(
        @PathVariable id: String,
        @RequestParam(required = true) traineeId: String,
    ) = workoutExerciseSetRecordService.get(id, traineeId)

    @PreAuthorize("@authService.isAdminOrTrainerOfOrTrainee(#traineeId)")
    @GetMapping("/pr")
    fun getPRSetRecords(
        @RequestParam(required = true) traineeId: String,
        @RequestParam(required = true) exerciseId: String,
        @DateRequestParam(required = false, requestParamName = "endDate") endDate: LocalDate?
    ) = workoutExerciseSetRecordService.getPRSetRecords(
        GetPRSetRecordsRequest(
            traineeId = traineeId,
            exerciseId = exerciseId,
            endDate = endDate
        )
    )

}