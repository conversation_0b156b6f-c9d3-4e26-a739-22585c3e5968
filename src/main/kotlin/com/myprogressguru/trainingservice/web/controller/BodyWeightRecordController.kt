package com.myprogressguru.trainingservice.web.controller

import com.myprogressguru.commonutils.annotation.DateRequestParam
import com.myprogressguru.trainingservice.enumeration.StatsPeriod
import com.myprogressguru.trainingservice.service.BodyWeightHistoryStatsService
import com.myprogressguru.trainingservice.service.BodyWeightService
import com.myprogressguru.trainingservice.service.StatsRetrievalRequest
import com.myprogressguru.trainingservice.web.payload.info.Stats
import com.myprogressguru.trainingservice.web.payload.weight.BodyWeightRecordHistoryResponse
import com.myprogressguru.trainingservice.web.payload.weight.BodyWeightRecordPutRequest
import com.myprogressguru.trainingservice.web.payload.weight.BodyWeightRecordResponse
import com.myprogressguru.trainingservice.web.payload.weight.WeeklyBodyWeightRecordResponse
import jakarta.validation.Valid
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*
import java.time.LocalDate

@RestController
@RequestMapping("/v1/body-weight-records")
class BodyWeightRecordController(
    private val bodyWeightService: BodyWeightService,
    private val bodyWeightHistoryStatsService: BodyWeightHistoryStatsService
) {

    @PreAuthorize("@authService.isAdminOrTrainerOfOrTrainee(#traineeId)")
    @GetMapping
    fun getBodyWeightRecord(
        @RequestParam(required = true) traineeId: String, @DateRequestParam date: LocalDate?
    ): BodyWeightRecordResponse? = bodyWeightService.getRecord(traineeId, date)

    @PreAuthorize("@authService.isAdminOrTrainerOfOrTrainee(#traineeId)")
    @PutMapping
    fun putBodyWeightRecord(
        @RequestParam(required = true) traineeId: String,
        @DateRequestParam date: LocalDate?,
        @Valid @RequestBody model: BodyWeightRecordPutRequest
    ): BodyWeightRecordResponse = bodyWeightService.putRecord(traineeId, date, model)

    @PreAuthorize("@authService.isAdminOrTrainerOfOrTrainee(#traineeId)")
    @GetMapping("/weekly")
    fun getWeeklyBodyWeightRecord(
        @RequestParam(required = true) traineeId: String,
        @DateRequestParam(requestParamName = "dayOfWeekDate") dayOfWeekDate: LocalDate?
    ): WeeklyBodyWeightRecordResponse =
        bodyWeightService.getWeeklyRecord(traineeId, dayOfWeekDate)

    @PreAuthorize("@authService.isAdminOrTrainerOfOrTrainee(#traineeId)")
    @GetMapping("/history")
    fun getBodyWeightRecordHistory(
        @RequestParam(required = true) traineeId: String,
        @RequestParam(required = false, defaultValue = "1") page: Long
    ): BodyWeightRecordHistoryResponse =
        bodyWeightService.getRecordHistory(traineeId, page)

    @PreAuthorize("@authService.isAdminOrTrainerOfOrTrainee(#traineeId)")
    @GetMapping("/history/stats")
    fun getHistoryStats(
        @RequestParam(required = true) traineeId: String,
        @DateRequestParam(required = true, requestParamName = "endDate") endDate: LocalDate,
        @DateRequestParam(required = false, requestParamName = "startDate") startDate: LocalDate?,
        @RequestParam(required = true) period: StatsPeriod
    ): Stats<WeeklyBodyWeightRecordResponse> =
        bodyWeightHistoryStatsService.getHistoryStats(
            StatsRetrievalRequest(traineeId, endDate, startDate),
            period
        )
}