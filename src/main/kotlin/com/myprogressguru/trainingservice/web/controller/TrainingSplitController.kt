package com.myprogressguru.trainingservice.web.controller

import com.myprogressguru.trainingservice.service.TrainingSplitService
import com.myprogressguru.trainingservice.web.payload.training.split.*
import com.myprogressguru.trainingservice.web.payload.training.workout.WorkoutResponse
import com.myprogressguru.trainingservice.web.payload.training.workout.WorkoutsOrderEditRequest
import jakarta.validation.Valid
import org.springframework.http.HttpStatus
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/v1/training-splits")
class TrainingSplitController(
    private val trainingSplitService: TrainingSplitService
) {

    @PreAuthorize(
        "@keycloakService.isRequesterAdminOrRequesterIdMatchesUserId(#model.trainerId) OR " +
                "@authService.isAdminOrTrainerOfOrTrainee(#model.traineeId)"
    )
    @ResponseStatus(HttpStatus.CREATED)
    @PostMapping
    fun create(
        @Valid @RequestBody model: TrainingSplitCreateRequest,
    ): TrainingSplitResponse = trainingSplitService.create(model)

    @PreAuthorize(
        "@keycloakService.isRequesterAdminOrRequesterIdMatchesUserId(#trainerId) OR " +
                "@authService.isAdminOrTrainerOfOrTrainee(#traineeId)"
    )
    @GetMapping
    fun getAll(
        @RequestParam(required = false) trainerId: String?,
        @RequestParam(required = false) traineeId: String?
    ): List<TrainingSplitResponse> = trainingSplitService.getAll(trainerId, traineeId)

    @PreAuthorize("@authService.isAdminOrTrainerOfOrTrainee(#traineeId)")
    @GetMapping("/active")
    fun getActive(
        @RequestParam(required = true) traineeId: String
    ): TrainingSplitResponse? = trainingSplitService.getActive(traineeId)

    @PreAuthorize("@authService.isAdminOrTrainerOwnerOfOrOptionalTraineeProOf(@trainingSplitRepository, #id)")
    @GetMapping("/{id}")
    fun get(
        @PathVariable id: String
    ): TrainingSplitResponse = trainingSplitService.get(id)

    @PreAuthorize("@authService.isAdminOrTrainerOwnerOf(@trainingSplitRepository, #id)")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @PatchMapping("/{id}")
    fun edit(
        @PathVariable id: String,
        @RequestBody model: TrainingSplitEditRequest,
    ) = trainingSplitService.edit(model, id)

    @PreAuthorize("@authService.isAdminOrTrainerOwnerOf(@trainingSplitRepository, #id)")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @DeleteMapping("/{id}")
    fun delete(
        @PathVariable id: String,
    ) = trainingSplitService.delete(id)

    @PreAuthorize(
        "@authService.isAdminOrTrainerOwnerOf(@trainingSplitRepository, #id) AND " +
                "@authService.isAdminOrOptionalTrainerOf(#model.traineeId)"
    )
    @PostMapping("/{id}/copy")
    fun copy(
        @PathVariable id: String,
        @RequestBody model: TrainingSplitCopyRequest,
    ): TrainingSplitResponse = trainingSplitService.copy(id, model)

    @PreAuthorize("@authService.isAdminOrTrainerOwnerOfOrOptionalTraineeProOf(@trainingSplitRepository, #id)")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @PutMapping("/{id}/workouts/order")
    fun editWorkoutsOrder(
        @PathVariable id: String,
        @Valid @RequestBody model: WorkoutsOrderEditRequest,
    ) = trainingSplitService.editWorkoutsOrder(id, model)

    @PreAuthorize("@authService.isAdminOrTrainerOwnerOfOrOptionalTraineeProOf(@trainingSplitRepository, #id)")
    @PostMapping("/{id}/workouts")
    fun addWorkout(
        @PathVariable id: String,
        @Valid @RequestBody model: TrainingSplitWorkoutAddRequest,
    ): WorkoutResponse = trainingSplitService.addWorkout(id, model)
}
