package com.myprogressguru.trainingservice.web.controller

import com.myprogressguru.commonutils.annotation.DateRequestParam
import com.myprogressguru.storageutils.service.model.StorageObjectResponse
import com.myprogressguru.storageutils.web.payload.StorageObjectUploadUrlRequest
import com.myprogressguru.storageutils.web.payload.notes.SmartNoteCreateRequest
import com.myprogressguru.storageutils.web.payload.notes.SmartNoteResponse
import com.myprogressguru.trainingservice.enumeration.StatsPeriod
import com.myprogressguru.trainingservice.service.FileUploadService
import com.myprogressguru.trainingservice.service.WorkoutExerciseRecordService
import com.myprogressguru.trainingservice.service.WorkoutExerciseSetRecordService
import com.myprogressguru.trainingservice.service.WorkoutRecordService
import com.myprogressguru.trainingservice.web.payload.exercise.TrainingVolumeHistoryResponse
import com.myprogressguru.trainingservice.web.payload.note.NoteRequest
import com.myprogressguru.trainingservice.web.payload.note.NoteResponse
import com.myprogressguru.trainingservice.web.payload.training.workout.*
import jakarta.validation.Valid
import org.springframework.data.domain.Page
import org.springframework.http.HttpStatus
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*
import java.time.LocalDate

@RestController
@RequestMapping("/v1/workout-records")
class WorkoutRecordController(
    private val workoutRecordService: WorkoutRecordService,
    private val workoutExerciseRecordService: WorkoutExerciseRecordService,
    private val workoutExerciseSetRecordService: WorkoutExerciseSetRecordService,
    private val fileUploadService: FileUploadService
) {

    @PreAuthorize("@authService.isAdminOrTrainerOfOrTrainee(#traineeId)")
    @GetMapping
    fun getHistory(
        @RequestParam(required = true) traineeId: String,
        @RequestParam(required = false, defaultValue = "1") page: Int,
        @RequestParam(required = false, defaultValue = "10") size: Int,
        @RequestParam(required = false, defaultValue = "false") basicInfo: Boolean,
        @DateRequestParam(required = false, requestParamName = "endDate") endDate: LocalDate?,
    ): Page<BaseWorkoutRecordResponse> = workoutRecordService.getHistory(
        WorkoutHistoryRequest(
            traineeId = traineeId,
            page = page,
            size = size,
            basicInfo = basicInfo,
            endDate = endDate
        )
    )

    @PreAuthorize("@authService.isAdminOrTrainerOfOrTrainee(#traineeId)")
    @GetMapping("/training-volume/stats")
    fun getTrainingVolumeHistory(
        @RequestParam(required = true) traineeId: String,
        @DateRequestParam(required = true, requestParamName = "endDate") endDate: LocalDate,
        @DateRequestParam(required = false, requestParamName = "startDate") startDate: LocalDate?,
        @RequestParam(required = true) period: StatsPeriod
    ): TrainingVolumeHistoryResponse = workoutRecordService.getTrainingVolumeHistory(traineeId, endDate, period)

    @PreAuthorize("@authService.isAdminOrTrainerOwnerOfOrOptionalTraineeOf(@workoutRepository, #model.workoutId)")
    @ResponseStatus(HttpStatus.CREATED)
    @PostMapping
    fun create(
        @Valid @RequestBody model: WorkoutRecordCreateRequest
    ): WorkoutRecordResponse = workoutRecordService.create(model)

    @PreAuthorize("@authService.isAdminOrTraineeOwnerOf(@workoutRecordRepository, #id)")
    @GetMapping("/{id}")
    fun get(
        @PathVariable id: String,
    ): WorkoutRecordResponse = workoutRecordService.get(id)

    @PreAuthorize("@authService.isAdminOrTraineeOwnerOf(@workoutRecordRepository, #id)")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @PatchMapping("/{id}")
    fun edit(
        @PathVariable id: String,
        @Valid @RequestBody model: WorkoutRecordEditRequest
    ) = workoutRecordService.edit(id, model)

    @PreAuthorize("@authService.isAdminOrTraineeOwnerOf(@workoutRecordRepository, #id)")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @DeleteMapping("/{id}")
    fun delete(
        @PathVariable id: String
    ) = workoutRecordService.delete(id)

    @PreAuthorize("@authService.isAdminOrTrainerOfOrTrainee(#traineeId)")
    @GetMapping("/active")
    fun getActive(
        @RequestParam(required = true) traineeId: String
    ): WorkoutRecordResponse? = workoutRecordService.getActive(traineeId)

    @PreAuthorize("@authService.isAdminOrTrainerOfOrTrainee(#traineeId)")
    @GetMapping("/next")
    fun getNext(
        @RequestParam(required = true) traineeId: String
    ): ScheduledWorkoutResponse? = workoutRecordService.getNext(traineeId)

    @PreAuthorize("@authService.isAdminOrTrainerOfOrTrainee(#traineeId)")
    @GetMapping("/schedule")
    fun getSchedule(
        @RequestParam(required = true) traineeId: String
    ): WorkoutsScheduleResponse? = workoutRecordService.getSchedule(traineeId)

    @PreAuthorize("@authService.isAdminOrTrainerOfOrTrainee(#traineeId)")
    @PostMapping("/active/end")
    fun endActive(
        @RequestParam(required = true) traineeId: String
    ): WorkoutRecordResponse = workoutRecordService.endActive(traineeId)

    @PreAuthorize("@authService.isAdminOrTrainerOfOrTrainee(#traineeId)")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @DeleteMapping("/active/cancel")
    fun cancelActive(
        @RequestParam(required = true) traineeId: String
    ) = workoutRecordService.cancelActive(traineeId)

    @PreAuthorize("@authService.isAdminOrTraineeOwnerOf(@workoutRecordRepository, #id)")
    @PostMapping("/{id}/notes")
    fun createNote(
        @PathVariable id: String,
        @Valid @RequestBody model: NoteRequest
    ): NoteResponse = workoutRecordService.createNote(id, model)

    @PreAuthorize("@authService.isAdminOrTraineeOwnerOf(@workoutRecordRepository, #id)")
    @GetMapping("/{id}/exercise-records/{exerciseRecordId}")
    fun getExerciseRecord(
        @PathVariable id: String,
        @PathVariable exerciseRecordId: String,
    ): WorkoutExerciseRecordResponse = workoutExerciseRecordService.get(id, exerciseRecordId)

    @PreAuthorize("@authService.isAdminOrTraineeOwnerOf(@workoutRecordRepository, #id)")
    @PatchMapping("/{id}/exercise-records/{exerciseRecordId}")
    fun editExerciseRecord(
        @PathVariable id: String,
        @PathVariable exerciseRecordId: String,
        @Valid @RequestBody model: WorkoutExerciseRecordEditRequest
    ): WorkoutExerciseRecordResponse = workoutExerciseRecordService.edit(id, exerciseRecordId, model)

    @PreAuthorize("@authService.isAdminOrTraineeOwnerOf(@workoutRecordRepository, #id)")
    @PostMapping("/{id}/exercise-records/{exerciseRecordId}/skip")
    fun skipExerciseRecord(
        @PathVariable id: String,
        @PathVariable exerciseRecordId: String,
    ): WorkoutExerciseRecordResponse = workoutExerciseRecordService.skip(id, exerciseRecordId)

    @PreAuthorize("@authService.isAdminOrTraineeOwnerOf(@workoutRecordRepository, #id)")
    @PostMapping("/{id}/exercise-records/{exerciseRecordId}/skip/cancel")
    fun cancelSkipExerciseRecord(
        @PathVariable id: String,
        @PathVariable exerciseRecordId: String,
    ): WorkoutExerciseRecordResponse = workoutExerciseRecordService.cancelSkip(id, exerciseRecordId)

    @PreAuthorize("@authService.isAdminOrTraineeOwnerOf(@workoutRecordRepository, #id)")
    @PostMapping("/{id}/exercise-records/{exerciseRecordId}/notes")
    fun createExerciseRecordNote(
        @PathVariable id: String,
        @PathVariable exerciseRecordId: String,
        @Valid @RequestBody model: NoteRequest
    ): NoteResponse = workoutExerciseRecordService.createNote(id, exerciseRecordId, model)

    @PreAuthorize("@authService.isAdminOrTraineeOwnerOf(@workoutRecordRepository, #id)")
    @ResponseStatus(HttpStatus.CREATED)
    @PostMapping("/{id}/exercise-records/{exerciseRecordId}/set-records")
    fun createSetRecord(
        @PathVariable id: String,
        @PathVariable exerciseRecordId: String,
    ): WorkoutExerciseSetRecordResponse = workoutExerciseSetRecordService.create(exerciseRecordId)

    @PreAuthorize("@authService.isAdminOrTraineeOwnerOf(@workoutRecordRepository, #id)")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @PutMapping("/{id}/exercise-records/{exerciseRecordId}/set-records/{setRecordId}")
    fun editSetRecord(
        @PathVariable id: String,
        @PathVariable exerciseRecordId: String,
        @PathVariable setRecordId: String,
        @Valid @RequestBody model: WorkoutExerciseSetRecordEditRequest
    ) = workoutExerciseSetRecordService.edit(exerciseRecordId, setRecordId, model)

    @PreAuthorize("@authService.isAdminOrTraineeOwnerOf(@workoutRecordRepository, #id)")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @DeleteMapping("/{id}/exercise-records/{exerciseRecordId}/set-records/{setRecordId}")
    fun deleteSetRecord(
        @PathVariable id: String,
        @PathVariable exerciseRecordId: String,
        @PathVariable setRecordId: String,
    ) = workoutExerciseSetRecordService.delete(exerciseRecordId, setRecordId)

    @PreAuthorize("@authService.isAdminOrTraineeOwnerOf(@workoutRecordRepository, #id)")
    @ResponseStatus(HttpStatus.CREATED)
    @PostMapping("/{id}/exercise-records/{exerciseRecordId}/set-records/{setRecordId}/video/upload-url")
    fun getSetRecordVideoUploadUrl(
        @PathVariable id: String,
        @PathVariable exerciseRecordId: String,
        @PathVariable setRecordId: String,
        @Valid @RequestBody model: StorageObjectUploadUrlRequest
    ): StorageObjectResponse = fileUploadService.getSetRecordVideoUploadUrl(setRecordId, model)

    @PreAuthorize("@authService.isAdminOrTraineeOwnerOf(@workoutRecordRepository, #id)")
    @PutMapping("/{id}/exercise-records/{exerciseRecordId}/set-records/{setRecordId}/video")
    fun putSetRecordVideo(
        @PathVariable id: String,
        @PathVariable exerciseRecordId: String,
        @PathVariable setRecordId: String,
        @Valid @RequestBody model: WorkoutExerciseSetRecordVideoRequest
    ): WorkoutExerciseSetRecordVideoResponse =
        workoutExerciseSetRecordService.putVideo(exerciseRecordId, setRecordId, model)

    @PreAuthorize("@authService.isAdminOrTraineeOwnerOf(@workoutRecordRepository, #id)")
    @PostMapping("/{id}/exercise-records/{exerciseRecordId}/set-records/{setRecordId}/smart-notes")
    fun createSmartNote(
        @PathVariable id: String,
        @PathVariable exerciseRecordId: String,
        @PathVariable setRecordId: String,
        @Valid @RequestBody model: SmartNoteCreateRequest
    ): SmartNoteResponse = workoutExerciseSetRecordService.createSmartNote(exerciseRecordId, setRecordId, model)

    @PreAuthorize("@authService.isAdminOrTraineeOwnerOf(@workoutRecordRepository, #id)")
    @DeleteMapping("/{id}/exercise-records/{exerciseRecordId}/set-records/{setRecordId}/video")
    fun deleteSetRecordVideo(
        @PathVariable id: String,
        @PathVariable exerciseRecordId: String,
        @PathVariable setRecordId: String,
    ) = workoutExerciseSetRecordService.deleteVideo(exerciseRecordId, setRecordId)

    @PreAuthorize("@authService.isAdminOrTraineeOwnerOf(@workoutRecordRepository, #id)")
    @PostMapping("/{id}/exercise-records/{exerciseRecordId}/set-records/{setRecordId}/video/notes")
    fun createSetRecordVideoNote(
        @PathVariable id: String,
        @PathVariable exerciseRecordId: String,
        @PathVariable setRecordId: String,
        @Valid @RequestBody model: NoteRequest
    ): NoteResponse = workoutExerciseSetRecordService.createVideoNote(exerciseRecordId, setRecordId, model)
}