package com.myprogressguru.trainingservice.config

import com.myprogressguru.cloudutils.config.CloudUtilsBeanConfig
import com.myprogressguru.commonutils.config.CommonUtilsBeanConfig
import com.myprogressguru.keycloakutils.service.rest.interceptor.ServiceTokenInterceptor
import com.myprogressguru.notificationsutils.config.NotificationsUtilsBeanConfig
import com.myprogressguru.storageutils.config.StorageUtilsBeanConfig
import com.myprogressguru.trainingservice.config.properties.WhoopProperties
import com.myprogressguru.trainingservice.service.rest.client.ManyChatClient
import com.myprogressguru.trainingservice.service.rest.client.NutritionServiceClient
import com.myprogressguru.trainingservice.service.rest.client.PaymentsServiceClient
import com.myprogressguru.trainingservice.service.rest.client.WhoopClient
import org.springframework.ai.chat.client.ChatClient
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Import
import org.springframework.web.client.RestClient
import org.springframework.web.client.support.RestClientAdapter
import org.springframework.web.service.invoker.HttpServiceProxyFactory
import software.amazon.awssdk.services.ssm.SsmClient

@Import(
    value = [CommonUtilsBeanConfig::class, CloudUtilsBeanConfig::class, StorageUtilsBeanConfig::class, NotificationsUtilsBeanConfig::class]
)
@EnableConfigurationProperties(WhoopProperties::class)
@Configuration
class BeanConfig {

    @Bean
    fun whoopClient(): WhoopClient {
        val client = RestClient.create("https://api.prod.whoop.com")
        val factory = HttpServiceProxyFactory.builderFor(RestClientAdapter.create(client)).build()

        return factory.createClient(WhoopClient::class.java)
    }

    @Bean
    fun paymentsServiceClient(
        @Value("\${mpg.payments-service.url}") paymentsServiceUrl: String,
        serviceTokenInterceptor: ServiceTokenInterceptor
    ): PaymentsServiceClient {
        val client = RestClient.builder()
            .baseUrl("${paymentsServiceUrl}/api/v1").requestInterceptor(serviceTokenInterceptor).build()
        val factory = HttpServiceProxyFactory.builderFor(RestClientAdapter.create(client)).build()

        return factory.createClient(PaymentsServiceClient::class.java)
    }

    @Bean
    fun nutritionServiceClient(
        @Value("\${mpg.nutrition-service.url}") nutritionServiceUrl: String,
        serviceTokenInterceptor: ServiceTokenInterceptor
    ): NutritionServiceClient {
        val client = RestClient.builder()
            .baseUrl("${nutritionServiceUrl}/api/v1").requestInterceptor(serviceTokenInterceptor).build()
        val factory = HttpServiceProxyFactory.builderFor(RestClientAdapter.create(client)).build()

        return factory.createClient(NutritionServiceClient::class.java)
    }

    @Bean
    fun manyChatClient(): ManyChatClient {
        val client = RestClient.create("https://api.manychat.com")
        val factory = HttpServiceProxyFactory.builderFor(RestClientAdapter.create(client)).build()

        return factory.createClient(ManyChatClient::class.java)
    }

    @Bean
    fun ssmClient(): SsmClient = SsmClient.create()

    @Bean
    fun chatClient(builder: ChatClient.Builder): ChatClient = builder.build()
}