//package com.myprogressguru.trainingservice.config
//
//import feign.RequestInterceptor
//import org.keycloak.admin.client.Keycloak
//import org.springframework.context.annotation.Bean
//import org.springframework.context.annotation.Configuration
//
//@Configuration
//class FeignClientConfig(
//    private val keycloak: Keycloak
//) {
//
//    @Bean
//    fun requestInterceptor(): RequestInterceptor = RequestInterceptor { template ->
//        if (template.headers()["Authorization"].isNullOrEmpty()) {
//            template.header("Authorization", "Bearer ${getAccessToken()}")
//        }
//    }
//
//    private fun getAccessToken(): String? = keycloak.tokenManager().accessTokenString
//}