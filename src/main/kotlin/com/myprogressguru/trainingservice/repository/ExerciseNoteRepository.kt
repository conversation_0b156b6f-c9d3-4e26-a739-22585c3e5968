package com.myprogressguru.trainingservice.repository

import com.myprogressguru.trainingservice.entity.ExerciseNote
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query

interface ExerciseNoteRepository: JpaRepository<ExerciseNote, String> {

    @Query("SELECT en FROM ExerciseNote en JOIN en.note n WHERE en.exercise.id = :exerciseId AND en.trainee.id = :traineeId ORDER BY n.createdOn")
    fun findAllByExerciseIdAndTraineeIdOrderByNoteCreatedOn(exerciseId: String, traineeId: String): List<ExerciseNote>

    fun deleteByNoteId(noteId: String)

}