package com.myprogressguru.trainingservice.repository

import com.myprogressguru.trainingservice.entity.BodyMeasurementRecord
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.querydsl.QuerydslPredicateExecutor
import java.time.LocalDate

interface BodyMeasurementRecordRepository : JpaRepository<BodyMeasurementRecord, String>,
    QuerydslPredicateExecutor<BodyMeasurementRecord> {

    fun findTopByTraineeIdAndDate(traineeId: String, date: LocalDate): BodyMeasurementRecord?

    fun findAllByTraineeIdAndDateInOrderByDateAsc(
        traineeId: String,
        dates: List<LocalDate>
    ): List<BodyMeasurementRecord>
}
