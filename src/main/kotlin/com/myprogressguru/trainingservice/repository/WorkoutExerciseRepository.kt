package com.myprogressguru.trainingservice.repository

import com.myprogressguru.trainingservice.entity.WorkoutExercise
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param

interface WorkoutExerciseRepository : JpaRepository<WorkoutExercise, String> {

    @Query("SELECT DISTINCT we.id FROM WorkoutExercise we " +
            "JOIN WorkoutExerciseRecord wer " +
            "ON wer.exercise.id = we.id " +
            "WHERE we.id IN :workoutExerciseIds")
    fun findAllIdsWithExistingRecords(@Param("workoutExerciseIds") workoutExerciseIds: List<String>): List<String>

}
