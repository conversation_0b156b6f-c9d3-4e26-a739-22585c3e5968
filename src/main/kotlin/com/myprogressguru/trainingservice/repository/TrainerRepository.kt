package com.myprogressguru.trainingservice.repository

import com.myprogressguru.trainingservice.entity.Trainer
import com.myprogressguru.trainingservice.web.payload.trainer.TrainerResponse
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query

interface TrainerRepository : JpaRepository<Trainer, String> {

    @Query("SELECT new com.myprogressguru.trainingservice.web.payload.trainer.TrainerResponse(t.id, tr.firstName, tr.lastName) FROM Trainer t JOIN Trainee tr ON t.id = tr.id")
    fun findAllAsTrainerResponse(): List<TrainerResponse>
}
