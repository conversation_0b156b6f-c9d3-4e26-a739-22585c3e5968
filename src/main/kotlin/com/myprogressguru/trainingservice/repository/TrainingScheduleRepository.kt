package com.myprogressguru.trainingservice.repository

import com.myprogressguru.trainingservice.entity.TrainingSchedule
import org.springframework.data.jpa.repository.JpaRepository

interface TrainingScheduleRepository : JpaRepository<TrainingSchedule, String> {

    fun findFirstByIsActiveTrueAndTraineeId(traineeId: String): TrainingSchedule?

    fun findAllByIsActiveTrueAndTraineeIdIn(traineeIds: List<String>): List<TrainingSchedule>
}
