package com.myprogressguru.trainingservice.repository

import com.myprogressguru.trainingservice.entity.BodyWeightRecord
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import java.time.LocalDate

interface BodyWeightRecordRepository : JpaRepository<BodyWeightRecord, String> {

    fun findTopByTraineeIdAndDate(traineeId: String, date: LocalDate): BodyWeightRecord?

    fun findAllByTraineeIdAndDateInOrderByDateAsc(
        traineeId: String,
        dates: List<LocalDate>
    ): List<BodyWeightRecord>

    fun findAllByTraineeIdInAndDateOrderByDateAsc(
        traineeId: List<String>,
        date: LocalDate
    ): List<BodyWeightRecord>

    @Query("SELECT AVG(bwr.bodyWeightInKg) FROM BodyWeightRecord bwr WHERE bwr.date IN :dates")
    fun getAverageBodyWeightInKgByDateIn(dates: List<LocalDate>): Double?

    fun findTopByTraineeIdAndDateAfterOrderByDateDesc(traineeId: String, startDate: LocalDate): BodyWeightRecord?

    fun findTopByTraineeIdAndDateAfterOrderByDateAsc(traineeId: String, startDate: LocalDate): BodyWeightRecord?

    fun findAllByTraineeIdAndDateBetweenOrderByDateAsc(
        traineeId: String,
        startDate: LocalDate,
        endDate: LocalDate
    ): List<BodyWeightRecord>
}
