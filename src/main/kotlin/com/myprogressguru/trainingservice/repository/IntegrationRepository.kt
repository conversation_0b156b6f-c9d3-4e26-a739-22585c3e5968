package com.myprogressguru.trainingservice.repository

import com.myprogressguru.trainingservice.entity.Integration
import com.myprogressguru.trainingservice.enumeration.IntegrationType
import org.springframework.data.jpa.repository.JpaRepository

interface IntegrationRepository : JpaRepository<Integration, String> {

    fun findByTypeAndExternalUserId(type: IntegrationType, externalUserId: String): Integration?

    fun findAllByType(integrationType: IntegrationType): List<Integration>

    fun findByExternalUserIdAndTypeAndTraineeId(
        externalUserId: String,
        type: IntegrationType,
        traineeId: String
    ): Integration?

    fun findAllByTraineeId(traineeId: String): List<Integration>
}