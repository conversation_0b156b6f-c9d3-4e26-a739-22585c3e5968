package com.myprogressguru.trainingservice.repository

import com.myprogressguru.trainingservice.entity.TrainingSplit
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.querydsl.QuerydslPredicateExecutor

interface TrainingSplitRepository : JpaRepository<TrainingSplit, String>, QuerydslPredicateExecutor<TrainingSplit> {

    fun findAllByTrainerId(trainerId: String): List<TrainingSplit>

    fun findFirstByIsActiveTrueAndTraineeId(traineeId: String): TrainingSplit?

    fun findAllByIsActiveTrueAndTraineeIdIn(traineeIds: List<String>): List<TrainingSplit>
}
