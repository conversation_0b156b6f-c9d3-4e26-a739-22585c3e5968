package com.myprogressguru.trainingservice.repository

import com.myprogressguru.trainingservice.entity.TrainingTimeSchedule
import org.springframework.data.jpa.repository.JpaRepository

interface TrainingTimeScheduleRepository : JpaRepository<TrainingTimeSchedule, String> {

    fun findFirstByIsActiveTrueAndTraineeId(traineeId: String): TrainingTimeSchedule?

    fun findAllByIsActiveTrueAndTraineeIdIn(traineeIds: List<String>): List<TrainingTimeSchedule>
}
