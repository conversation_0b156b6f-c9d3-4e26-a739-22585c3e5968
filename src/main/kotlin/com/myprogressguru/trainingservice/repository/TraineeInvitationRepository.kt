package com.myprogressguru.trainingservice.repository

import com.myprogressguru.trainingservice.entity.TraineeInvitation
import org.springframework.data.jpa.repository.JpaRepository
import java.time.ZonedDateTime

interface TraineeInvitationRepository : JpaRepository<TraineeInvitation, String> {

    fun findAllByTrainerIdAndFilledIsTrue(trainerId: String): List<TraineeInvitation>

    fun deleteAllByCreatedOnBeforeAndFilledIsFalse(date: ZonedDateTime): Int
}