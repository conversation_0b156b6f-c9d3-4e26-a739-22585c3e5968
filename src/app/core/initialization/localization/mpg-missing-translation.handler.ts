import {
  MissingTranslationHandler,
  MissingTranslationHandlerParams,
} from '@ngx-translate/core';

export class MPGMissingTranslationHandler implements MissingTranslationHandler {
  handle(params: MissingTranslationHandlerParams): any {
    if (!params.interpolateParams) {
      return params.key;
    }

    const { fallback } = params.interpolateParams as { fallback: string };

    return fallback || params.key;
  }
}
