import { HttpClient } from '@angular/common/http';

import { TranslateHttpLoader } from '@ngx-translate/http-loader';

import { ApplicationLocalizationInitializationService } from './application-localization-initialization.service';

export class ApplicationLocalizationInitializationFactory {
  static createTranslateLoader(httpClient: HttpClient): TranslateHttpLoader {
    const relativePathToLocaleFolder = '../../../../assets/locale/';
    return new TranslateHttpLoader(httpClient, relativePathToLocaleFolder);
  }

  static getApplicationLocalizationInitializationFunction(
    localizationInitService: ApplicationLocalizationInitializationService
  ): () => Promise<void> {
    return async (): Promise<void> => {
      return localizationInitService.initialize();
    };
  }
}
