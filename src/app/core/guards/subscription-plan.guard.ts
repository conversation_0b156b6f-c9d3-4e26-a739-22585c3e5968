import { inject } from '@angular/core';
import { Router, UrlTree } from '@angular/router';
import { Observable } from 'rxjs';
import { SubscriptionPlanType } from '../../payments/enumerations';
import { SubscriptionPlanService } from '../../payments/services';

export const subscriptionPlanGuard: (
  requiredPlan: SubscriptionPlanType,
) =>
  | boolean
  | UrlTree
  | Promise<boolean | UrlTree>
  | Observable<boolean | UrlTree> = (requiredPlan: SubscriptionPlanType) => {
  const subscriptionPlanService: SubscriptionPlanService = inject(
    SubscriptionPlanService,
  );
  const router: Router = inject(Router);
  const requiredPlanMissing =
    subscriptionPlanService.showPaidFeatureModalIfNeeded(requiredPlan);

  return requiredPlanMissing.then((isMissing) => {
    if (isMissing) {
      return router.createUrlTree(['/trainee']);
    }

    return true;
  });
};
