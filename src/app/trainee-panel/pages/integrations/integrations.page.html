<ion-header>
  <ion-toolbar color="dark">
    <ion-buttons slot="start">
      <ion-menu-button></ion-menu-button>
    </ion-buttons>
    <ion-title> {{ "integrations.label" | translate }}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-row>
    <ion-col offset-lg="3" size="12" size-lg="6">
      <div class="whoop flex-column-space-around">
        <div class="flex-centered">
          <img src="/assets/icon/whoop-wordmark.svg" />
        </div>
        <div
          (click)="handleWhoopIntegration()"
          class="cta ion-activatable overflow-hidden"
        >
          <ion-ripple-effect></ion-ripple-effect>
          <h2>
            {{ (integrationsByType && integrationsByType['WHOOP'] ?
            'integrations.connected' : 'buttons.connect') | translate |
            uppercase }}
          </h2>
        </div>
      </div>
      <div class="discord">
        <img src="/assets/images/discord.jpg" />

        <ion-button (click)="handleDiscord()" class="join"
          >{{ 'buttons.join' | translate }}</ion-button
        >
      </div>
    </ion-col>
  </ion-row>
</ion-content>
