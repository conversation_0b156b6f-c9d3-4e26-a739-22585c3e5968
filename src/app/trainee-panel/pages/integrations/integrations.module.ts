import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { IntegrationsPageRoutingModule } from './integrations-routing.module';

import { IntegrationsPage } from './integrations.page';
import { TranslateModule } from '@ngx-translate/core';
import { CardComponentModule } from '../../../shared/components/card/card.module';
import { LoadingSpinnerComponentModule } from '../../../shared/components/loading-spinner/loading-spinner.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    IntegrationsPageRoutingModule,
    TranslateModule,
    CardComponentModule,
    LoadingSpinnerComponentModule,
  ],
  declarations: [IntegrationsPage],
})
export class IntegrationsPageModule {}
