<mpg-header></mpg-header>
<ion-content mpgContentScroll>
  <ion-row>
    <ion-col offset-lg="3" size="12" size-lg="6">
      <div class="header flex-start">
        <ion-label class="no-select"
          >{{ 'training.sessions' | translate }}
        </ion-label>
        <ion-label (click)="handleNavigateToSplit()" class="no-select"
          >{{ 'training.split' | translate }}
        </ion-label>
      </div>
      <div class="date-control flex-space-between">
        <div
          [class.hidden]="(selectedWorkoutRecordIndex && selectedWorkoutRecordIndex === history.content.length - 1) || history?.content?.length === 0"
          class="button flex-centered ion-activatable overflow-hidden"
        >
          <ion-ripple-effect></ion-ripple-effect>
          <ion-icon
            (click)="handleDateForwardClick()"
            name="chevron-back"
          ></ion-icon>
        </div>
        @if (!isLoading) {
        <ion-datetime-button
          datetime="endDate"
          mode="ios"
        ></ion-datetime-button>
        } @else {
        <ion-spinner></ion-spinner>
        }
        <div
          [class.hidden]="!selectedWorkoutRecordIndex || history?.content?.length === 0"
          class="button flex-centered ion-activatable overflow-hidden"
        >
          <ion-ripple-effect></ion-ripple-effect>
          <ion-icon
            (click)="handleDateBackClick()"
            name="chevron-forward"
          ></ion-icon>
        </div>
      </div>
      @if (selectedWorkoutRecord) {
      <div class="session">
        <div class="flex-column-centered">
          <ion-label class="summary">{{ 'summary' | translate }}</ion-label>
          <ion-label class="workout-name">
            {{ selectedWorkoutRecord?.workout?.name }}
          </ion-label>
        </div>
        <div class="stats">
          <div class="flex-column-start left">
            <div class="flex-centered">
              <ion-icon name="timer"></ion-icon>
              <ion-label>
                {{ (selectedWorkoutRecord?.startedOn | date: "HH:mm") +
                (selectedWorkoutRecord?.endedOn ? " - " +
                (selectedWorkoutRecord?.endedOn | date: "HH:mm") : "") }}
              </ion-label>
            </div>
            <ng-container
              *mpgHasSubscriptionPlan="SubscriptionPlanType.ADVANCED"
            >
              @if (selectedWorkoutRecord?.gym) {
              <div class="flex-centered">
                <ion-icon name="navigate-circle-outline"></ion-icon>
                <ion-label> {{ selectedWorkoutRecord?.gym.name }}</ion-label>
              </div>
              } @if (selectedWorkoutRecord?.onCreatine) {
              <div class="flex-centered">
                <ion-icon name="pulse"></ion-icon>
                <ion-label>
                  {{ "creatine-tracker.creatine" | translate }}
                </ion-label>
              </div>
              }
            </ng-container>
          </div>
          <div class="flex-column-end right">
            @if (selectedWorkoutRecord?.bodyWeightInKg) {
            <div class="flex-centered">
              <ion-label>
                {{ selectedWorkoutRecord?.bodyWeightInKg }} KG
              </ion-label>
              <ion-icon src="/assets/icon/body-outline.svg"></ion-icon>
            </div>
            } @if (selectedWorkoutRecord?.sleepQuality) {
            <div class="flex-centered">
              <ion-label>
                {{ selectedWorkoutRecord?.sleepQuality }}%
              </ion-label>
              <ion-icon name="moon"></ion-icon>
            </div>
            } @if (totalPersonalRecordsCount() > 0) {
            <div class="flex-centered pr">
              <ion-label> {{ totalPersonalRecordsCount() }}</ion-label>
              <ion-icon src="/assets/icon/pr.svg"></ion-icon>
            </div>
            }
          </div>
        </div>
        @if (selectedWorkoutRecord?.notes?.length > 0) {
        <div class="notes">
          <div class="flex-end">
            <ion-icon src="/assets/icon/quote.svg"></ion-icon>
          </div>
          <div>
            <ion-label>{{ selectedWorkoutRecord.notes[0].content }}</ion-label>
          </div>
        </div>
        } @for (exerciseRecord of selectedWorkoutRecord?.exerciseRecords; track
        exerciseRecord) {
        <div class="exercise-container flex">
          <div class="order-number">
            <ion-label>{{ getOrderNumber(exerciseRecord) }}</ion-label>
          </div>
          <div class="exercise flex-grow">
            <div class="exercise-header flex-space-between">
              <ion-label
                >{{ "exercises." + exerciseRecord.exercise.exercise.id |
                translate: { fallback: exerciseRecord.exercise.exercise.name }
                }}
              </ion-label>
              <mpg-options-button
                [options]="getOptions(exerciseRecord)"
              ></mpg-options-button>
            </div>
            <div class="sets">
              @for (setRecord of exerciseRecord.setRecords; track setRecord) {
              <div
                class="set flex-space-between ion-activatable overflow-hidden"
              >
                <ion-ripple-effect></ion-ripple-effect>
                <div class="placeholder"></div>
                <ion-label class="weight">{{ setRecord.weightInKg }}</ion-label>
                <ion-icon src="/assets/icon/cross.svg"></ion-icon>
                <ion-label class="reps">{{ setRecord.reps }}</ion-label>
                <div class="placeholder"></div>
                @if (setRecord.isPersonalRecord) {
                <ion-img src="/assets/images/pr-3d.png"></ion-img>
                }
                <ion-icon class="quote" src="/assets/icon/quote.svg"></ion-icon>
              </div>
              }
            </div>
          </div>
        </div>
        }
      </div>
      }
    </ion-col>
  </ion-row>
</ion-content>
<ion-modal [keepContentsMounted]="true">
  <ng-template>
    <ion-datetime
      [formControl]="dateFormControl"
      [locale]="locale$ | async"
      id="endDate"
      presentation="date"
    ></ion-datetime>
  </ng-template>
</ion-modal>
