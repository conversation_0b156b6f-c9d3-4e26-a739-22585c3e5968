import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { TraineeTrainingSessionsPageRoutingModule } from './trainee-training-sessions-routing.module';

import { TraineeTrainingSessionsPage } from './trainee-training-sessions.page';
import { HeaderModule } from '../../../shared/components/header/header.module';
import { TranslateModule } from '@ngx-translate/core';
import { TrainingScheduleCalendarComponentModule } from '../../../training/components/training-schedule-calendar/training-schedule-calendar.module';
import { HasSubscriptionPlanDirectiveModule } from '../../../payments/directives/has-subscription-plan-directive.module';
import { OptionsButtonModule } from '../../../shared/components/options-button/options-button.module';
import { ContentScrollDirectiveModule } from '../../../shared/directives/content-scroll/content-scroll-directive.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    TraineeTrainingSessionsPageRoutingModule,
    HeaderModule,
    TranslateModule,
    TrainingScheduleCalendarComponentModule,
    ReactiveFormsModule,
    HasSubscriptionPlanDirectiveModule,
    OptionsButtonModule,
    ContentScrollDirectiveModule,
  ],
  declarations: [TraineeTrainingSessionsPage],
})
export class TraineeTrainingSessionsPageModule {}
