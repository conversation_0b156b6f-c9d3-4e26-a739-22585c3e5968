.header {
  margin-top: 1px;
  padding-top: 16px;
  box-shadow: 0 -0.25px 0 var(--color-background-primary-black-300);

  ion-label {
    font-weight: 350;
    font-size: 30px;
    color: var(--color-background-primary-black-400);
    cursor: pointer;

    &:first-of-type {
      color: var(--color-background-secondary-white-600);
      margin-right: 16px;
    }
  }
}

.date-control {
  margin-top: 32px;
  margin-bottom: 32px;
  border-radius: var(--radius-medium-small);
  background: var(--color-background-primary-black-600);
  padding: 8px;

  .button {
    border-radius: var(--radius-extra_small);
    border: 0.5px solid var(--color-background-primary-black-400);
    background: var(--color-background-primary-black-500);
    padding: 4px 8px;

    &.hidden {
      visibility: hidden;
    }

    ion-icon {
      color: var(--color-background-secondary-white-500);
      font-size: 20px;
    }
  }
}

ion-datetime-button::part(native) {
  color: var(--color-background-secondary-white-500);
  font-size: 20px;
  font-weight: 350
}


.session {
  padding: 16px 0;
  background: var(--color-background-primary-black-700);
  border-radius: var(--radius-large) var(--radius-large) var(--radius-medium-small) var(--radius-medium-small);

  .summary {
    color: var(--color-background-primary-black-300);
    font-size: var(--typefaces-size-2xl);
    font-weight: 300;
    margin-bottom: 8px;
  }

  .workout-name {
    color: var(--color-background-secondary-white-600);
    font-size: 24px;
    font-weight: 300;
  }

  .stats {
    margin: 24px 0;
    padding: 16px;
    box-shadow: 0 -0.25px 0 var(--color-background-primary-black-200),
    0 0.25px 0 var(--color-background-primary-black-200);
    background: var(--color-background-primary-black-500);
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    .left {
      color: var(--color-background-primary-black-200);
      font-size: var(--typefaces-size-2xl);
      font-weight: 300;

      ion-icon {
        color: var(--color-background-primary-black-200);
        font-size: 20px;
        margin-right: 8px;
      }


      & > div:not(:last-of-type) {
        margin-bottom: 10px;
      }
    }

    .right {
      color: var(--color-background-secondary-white-600);
      font-size: var(--typefaces-size-2xl);
      font-weight: 300;

      ion-icon {
        color: var(--color-background-secondary-white-600);
        font-size: 20px;
        margin-left: 8px;
      }

      & > div:not(:last-of-type) {
        margin-bottom: 10px;
      }

      div.pr {
        color: var(--color-warning-main-yellow-600);

        ion-icon {
          color: var(--color-warning-main-yellow-600);
        }
      }
    }
  }

  .notes {
    padding-bottom: 24px;
    margin-bottom: 24px;

    ion-icon {
      color: var(--color-background-secondary-white-600);
      font-size: 20px;
    }

    ion-label {
      color: var(--color-background-secondary-white-600);
      font-size: var(--typefaces-size-2xl);
      font-weight: 400;
    }
  }
}

.exercise-container {
  box-shadow: 0 -0.25px 0 var(--color-background-primary-black-200);
  padding: 8px;
  background: var(--color-background-primary-black-600);

  .order-number {
    width: 40px;
    margin-right: 8px;
    margin-left: 8px;

    ion-label {
      color: var(--color-background-secondary-white-500);
      font-size: 30px;
      font-weight: 350;
      margin: 0 auto;
    }

  }

  .exercise {
    .exercise-header {
      margin-bottom: 16px;

      ion-label {
        color: var(--color-background-secondary-white-600);
        font-size: var(--typefaces-size-2xl);
        font-weight: 300;
        max-width: 80%;
      }

    }

    .sets {

      .set {
        border-radius: var(--radius-medium-small);
        border: 0.25px solid var(--color-background-primary-black-400);
        background: var(--color-background-primary-black-500);
        padding: 8px;
        position: relative;

        &:not(:last-of-type) {
          margin-bottom: 8px;
        }

        ion-img {
          width: 25px;
          height: 25px;
          position: absolute;
          top: calc(50% - 1px);
          left: 6px;
          transform: translateY(-50%);
        }

        ion-label {
          color: var(--color-background-secondary-white-600);
          font-size: 20px;
          font-weight: 350;
        }

        ion-icon {
          font-size: 25px;
          color: var(--color-background-secondary-white-600);
        }

        .quote {
          font-size: 20px;
          position: absolute;
          top: calc(50% - 1px);
          right: 6px;
          transform: translateY(-50%);
        }
      }
    }
  }

}
