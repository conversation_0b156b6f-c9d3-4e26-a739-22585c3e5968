import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { LegalPageRoutingModule } from './legal-routing.module';

import { LegalPage } from './legal-page.component';
import { TranslateModule } from '@ngx-translate/core';
import { CardComponentModule } from '../../../shared/components/card/card.module';
import { LoadingSpinnerComponentModule } from '../../../shared/components/loading-spinner/loading-spinner.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    LegalPageRoutingModule,
    TranslateModule,
    CardComponentModule,
    LoadingSpinnerComponentModule,
  ],
  declarations: [LegalPage],
})
export class LegalPageModule {}
