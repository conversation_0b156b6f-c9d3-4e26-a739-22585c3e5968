import { Component } from '@angular/core';
import { ViewDidEnter } from '@ionic/angular';
import {
  BodyWeightRecordHistory,
  WeeklyBodyWeightRecord,
} from '../../../training/models';
import { BodyWeightService, TraineeService } from '../../../training/services';
import { switchMap, tap } from 'rxjs/operators';
import { RecordEditEvent } from '../../../training/components/body-weight-record-history-table/body-weight-record-history-table.component';
import { Observable } from 'rxjs';
import { DateService, ModalService } from '../../../shared/services';
import { ActivatedRoute } from '@angular/router';
import { ChartSeries } from '../../../shared/models';

@Component({
  selector: 'mpg-trainee-weight',
  templateUrl: './trainee-weight.page.html',
  styleUrls: ['./trainee-weight.page.scss'],
})
export class TraineeWeightPage implements ViewDidEnter {
  bodyWeightRecordHistory: BodyWeightRecordHistory;
  highlightedDate: string;
  private traineeId: string;
  private page = 1;

  constructor(
    private bodyWeightService: BodyWeightService,
    private traineeService: TraineeService,
    private modalService: ModalService,
    private dateService: DateService,
    private route: ActivatedRoute,
  ) {}

  get canShowChart(): boolean {
    return (
      this.bodyWeightRecordHistory?.weeklyRecords.filter(
        (r) => r.averageBodyWeight,
      ).length > 1
    );
  }

  ionViewDidEnter() {
    this.traineeService.traineeId$
      .pipe(
        tap(() => {
          this.bodyWeightRecordHistory = undefined;
          this.page = 1;
        }),
        tap((traineeId) => (this.traineeId = traineeId)),
        switchMap(() => this.fetchBodyWeightRecordHistory(this.page)),
      )
      .subscribe();
    this.route.queryParamMap.subscribe((map) => {
      this.highlightedDate =
        map.get('date') || this.dateService.getCurrentDate();
    });
  }

  handleRefresh(event: any) {
    this.ionViewDidEnter();
    event.target.complete();
  }

  handleRecordEdit(
    { record, index }: RecordEditEvent,
    weeklyRecord: WeeklyBodyWeightRecord,
    weeklyRecordIndex: number,
  ) {
    this.bodyWeightService
      .getBodyWeightRecordInput(record)
      .pipe(
        switchMap((weight) => {
          return this.bodyWeightService.putBodyWeightRecord(
            { bodyWeightInKg: weight },
            record.date,
          );
        }),
        tap((updatedRecord) => {
          weeklyRecord.records[index].bodyWeightInKg =
            updatedRecord.bodyWeightInKg;

          this.updateWeeklyRecordAverage(weeklyRecord);

          if (weeklyRecordIndex > 0) {
            this.updateWeeklyRecordPercentageDiff(
              this.bodyWeightRecordHistory.weeklyRecords[weeklyRecordIndex - 1],
              weeklyRecord,
            );
          }

          if (
            weeklyRecordIndex !==
            this.bodyWeightRecordHistory.weeklyRecords.length - 1
          ) {
            this.updateWeeklyRecordPercentageDiff(
              weeklyRecord,
              this.bodyWeightRecordHistory.weeklyRecords[weeklyRecordIndex + 1],
            );
          }
        }),
      )
      .subscribe();
  }

  handleInfiniteScroll(event: any) {
    this.fetchBodyWeightRecordHistory(++this.page)
      .pipe(
        tap(() => {
          event.target.complete();
        }),
      )
      .subscribe();
  }

  fetchBodyWeightRecordHistory(
    page: number,
  ): Observable<BodyWeightRecordHistory> {
    return this.bodyWeightService
      .getBodyWeightRecordHistory(this.traineeId, page)
      .pipe(
        tap((record) => {
          if (!this.bodyWeightRecordHistory) {
            this.bodyWeightRecordHistory = record;
            return;
          }

          this.bodyWeightRecordHistory = {
            ...record,
            weeklyRecords: this.bodyWeightRecordHistory.weeklyRecords.concat(
              record.weeklyRecords,
            ),
          };
        }),
      );
  }

  handleChartOpen() {
    const series: ChartSeries[] = [
      this.bodyWeightRecordHistory.weeklyRecords
        .filter((r) => r.averageBodyWeight)
        .map((r) => {
          let displayValue = `${r.averageBodyWeight}`;
          if (r.percentageDiff) {
            displayValue += ` [${r.percentageDiff}%]`;
          }

          return {
            x: new Date(r.records[r.records.length - 1].date).getTime(),
            y: r.averageBodyWeight,
            displayValue,
          };
        }),
    ];

    this.modalService.createChart({
      title: 'weight.average-weight',
      series,
      useDisplayValue: true,
    });
  }

  private updateWeeklyRecordAverage(weeklyRecord: WeeklyBodyWeightRecord) {
    const bodyWeightRecordsArr = weeklyRecord.records
      .map((r) => r.bodyWeightInKg)
      .filter((r) => !!r);

    const average =
      bodyWeightRecordsArr.reduce((prev, curr) => prev + curr) /
      bodyWeightRecordsArr.length;

    weeklyRecord.averageBodyWeight = +average.toFixed(2);
  }

  private updateWeeklyRecordPercentageDiff(
    newerWeeklyRecord: WeeklyBodyWeightRecord,
    prevWeeklyRecord: WeeklyBodyWeightRecord,
  ) {
    if (!newerWeeklyRecord || !newerWeeklyRecord.averageBodyWeight) {
      return;
    }

    if (!prevWeeklyRecord || !prevWeeklyRecord.averageBodyWeight) {
      return;
    }

    newerWeeklyRecord.percentageDiff = +(
      ((newerWeeklyRecord.averageBodyWeight -
        prevWeeklyRecord.averageBodyWeight) /
        prevWeeklyRecord.averageBodyWeight) *
      100
    ).toFixed(2);
  }
}
