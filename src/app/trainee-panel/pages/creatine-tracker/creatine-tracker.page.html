<ion-header>
  <ion-toolbar color="dark">
    <ion-buttons slot="start">
      <ion-menu-button></ion-menu-button>
    </ion-buttons>
    <ion-title> {{ "creatine-tracker.label" | translate }}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-fab #fab class="mb" horizontal="end" slot="fixed" vertical="bottom">
  <ion-fab-button>
    <ion-icon name="add"></ion-icon>
  </ion-fab-button>
  <ion-fab-list side="top">
    <ion-fab-button
      (click)="handleCreatineRecordChange()"
      [attr.data-desc]="'creatine-tracker.start' | translate"
      >
      <ion-icon name="pulse"></ion-icon>
    </ion-fab-button>
  </ion-fab-list>
</ion-fab>
<ion-content (click)="fab.close()">
  <ion-row>
    <ion-col offset-lg="3" size="12" size-lg="6">
      <ion-list [inset]="true" class="ion-no-padding">
        @for (creatineRecord of creatineRecords; track creatineRecord; let i = $index) {
          <ion-item-sliding>
            <ion-item
              (click)="handleCreatineRecordChange(creatineRecord)"
              [button]="true"
              >
              <ion-label class="ion-text-center">
                <ion-chip
                  >{{ creatineRecord.startedOn | date: "dd.MM.yyyy" }}
                </ion-chip>
                -
                <ion-chip
                  >{{ creatineRecord.endedOn | date: "dd.MM.yyyy" }}
                </ion-chip>
              </ion-label>
            </ion-item>
            <ion-item-options>
              <ion-item-option
                (click)="handleDeleteCreatineRecord(creatineRecord)"
                >
                <ion-icon
                  color="light"
                  name="trash"
                  slot="icon-only"
                ></ion-icon>
              </ion-item-option>
            </ion-item-options>
          </ion-item-sliding>
          @if (i < creatineRecords.length - 1) {
            <ion-item
              class="ion-text-center"
              >
              <ion-icon color="dark" name="time"></ion-icon>
              @if (
                calculateRestPeriod(
                creatineRecords[i + 1].endedOn,
                creatineRecord.startedOn
                ); as diffObj
                ) {
                <ion-label
                  >
                  {{ diffObj.key + (diffObj.count === 1 ? ".one" : ".many") |
                  translate: {count: diffObj.count} }}
                </ion-label>
              }
            </ion-item>
          }
        }
      </ion-list>
    </ion-col>
  </ion-row>
</ion-content>
