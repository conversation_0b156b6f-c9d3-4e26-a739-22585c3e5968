ion-content {
  z-index: 3;
}

ion-thumbnail {
  --size: 85px;
  border-radius: 10px;
  overflow: hidden;
  margin-left: 16px;
}

ion-chip:first-of-type {
  margin-left: 0;
}

ion-fab-button {
  --box-shadow: none;
  --background: transparent;

}

.container {
  padding-bottom: 20px;
}

.page-header {
  background: linear-gradient(180deg, #0A0A0A, var(--color-background-primary-black-500));
}

.hero-block {
  border-radius: 0 0 var(--radius-extra-large) var(--radius-extra-large);
  background: var(--color-background-primary-black-500);
  margin-bottom: 32px;
  padding-bottom: 32px;
  padding-top: 2px;
  position: relative;
  left: -30px;
  width: calc(100% + 60px);

  .header {
    padding-left: 30px;
    padding-right: 30px;
  }

  ion-icon {
    color: var(--color-background-secondary-white-600);
    font-size: 25px;
    margin-bottom: 3px;
    cursor: pointer;
  }
}

.calories {
  margin-top: 16px;

  .value {
    position: absolute;
    left: 50%;
    bottom: 20px;
    transform: translateX(-50%);

    ion-label:nth-of-type(1) {
      color: var(--color-background-secondary-white-600);
      font-size: 28px;
      font-weight: 350;
    }

    ion-label:nth-of-type(2) {
      color: var(--color-background-primary-black-200);
      font-size: 20px;
      font-weight: 300;
    }
  }

  ion-icon {
    color: var(--color-background-secondary-white-600);
    font-size: 24px;
    position: absolute;
    left: 40px;
    top: 10px;
  }
}

.macros {
  margin-top: 16px;
  padding-left: 46px;
  padding-right: 46px;

  .chart {
    width: 32%;

    &:not(:last-of-type) {
      margin-right: 32px;
    }
  }
}

mpg-button {
  ion-icon {
    font-size: 20px;
    margin-right: 10px;
  }

  ion-label {
    font-weight: 350;
  }
}

.new-meal-block {
  cursor: pointer;
  border-radius: var(--radius-medium);
  border: 0.5px solid var(--color-background-primary-black-400);
  background: var(--color-background-primary-black-500);
  height: 88px;
  position: relative;

  ion-icon {
    font-size: 20px;
    color: var(--color-background-secondary-white-600);
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
}

.empty {
  height: 32px;
}

.loader {
  height: 260px;
  padding-top: 100px;
}
