<div class="page-header">
  <mpg-header></mpg-header>
</div>

<ion-modal [keepContentsMounted]="true">
  <ng-template>
    <ion-datetime
      (ionChange)="handleDateChange($event)"
      [firstDayOfWeek]="1"
      [value]="selectedDateControl?.value"
      id="datetime"
      presentation="date"
    ></ion-datetime>
  </ng-template>
</ion-modal>

<ion-content mpgContentScroll>
  <div [class.no-border-radius]="isLoading" class="hero-block">
    <div class="margin-left-l margin-right-l">
      <mpg-page-title title="nutrition.label">
        <div header-actions>
          @if (latestMealRecord && !mealDiary) {
          <mpg-button
            color="dark"
            (click)="selectedDateControl.setValue(latestMealRecord.date)"
          >
            <ion-icon src="/assets/icon/calendar-alert.svg"></ion-icon>
            {{ latestMealRecordDateTime | timeAgo : (locale$ | async) |
            lowercase }}
          </mpg-button>
          } @if (mealDiary) {
          <ion-icon
            (click)="handleCopyMeals()"
            src="/assets/icon/duplicate.svg"
            style="font-size: 18px"
          ></ion-icon>
          }
        </div>
      </mpg-page-title>
    </div>
    <ion-row>
      <ion-col offset-lg="3" size="12" size-lg="6">
        <div class="header">
          <div class="flex-space-between">
            <ion-icon
              (click)="handleDateBackClick()"
              name="chevron-back"
            ></ion-icon>
            <ion-datetime-button
              class="date-button"
              datetime="datetime"
              mode="ios"
            >
              <ion-text slot="date-target"
                >{{ (selectedDateControl?.value | relativeDate : (locale$ |
                async)) || (selectedDateControl?.value | date :'mediumDate' :
                undefined : (locale$ | async)) }}
              </ion-text>
            </ion-datetime-button>
            <ion-icon
              (click)="handleDateForwardClick()"
              name="chevron-forward"
            ></ion-icon>
          </div>
        </div>
        @if (!isLoading) {
        <div class="calories p-relative">
          <mpg-chart [chartOptions]="chartOptions"></mpg-chart>
          <div class="value">
            <ion-label>{{ mealDiary?.nutritionInfo?.calories || 0 }}</ion-label>
            <ion-label>/{{ caloriesGoal }} kcal</ion-label>
          </div>
          <ion-icon src="/assets/icon/flame.svg"></ion-icon>
        </div>
        <div class="macros flex-space-between">
          <div class="chart">
            <mpg-macronutrient-chart
              [value]="mealDiary?.nutritionInfo?.protein || 0"
              type="protein"
            ></mpg-macronutrient-chart>
          </div>
          <div class="chart">
            <mpg-macronutrient-chart
              [value]="mealDiary?.nutritionInfo?.carbs || 0"
              type="carbs"
            ></mpg-macronutrient-chart>
          </div>
          <div class="chart">
            <mpg-macronutrient-chart
              [value]="mealDiary?.nutritionInfo?.fat || 0"
              type="fat"
            ></mpg-macronutrient-chart>
          </div>
        </div>
        } @else {
        <div class="loader">
          <mpg-loading-spinner
            [isLoading]="true"
            [fullScreen]="false"
          ></mpg-loading-spinner>
        </div>
        }
      </ion-col>
    </ion-row>
  </div>
  <ion-refresher (ionRefresh)="handleRefresh($event)" slot="fixed">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>
  <ion-row>
    <ion-col class="ion-no-padding" offset-lg="3" size-lg="6">
      <div class="flex-space-around margin-bottom-l">
        <mpg-button (click)="handleExploreIdeas()">
          <ion-icon name="search-outline"></ion-icon>
          <ion-label>{{ 'nutrition.meal-ideas' | translate }}</ion-label>
        </mpg-button>
        <mpg-button color="dark">
          <ion-label>{{ 'nutrition.goal-calculator' | translate }}</ion-label>
        </mpg-button>
      </div>
      @for (mealRecord of mealDiary?.mealRecords; track mealRecord) {
      <mpg-meal-record-item-v2
        [id]="mealRecord.id"
        (onAddFoodClick)="handleLogFood({mealRecord})"
        (onDateChange)="selectedDateControl.setValue($event)"
        (onUpdate)="handleMealRecordUpdate($event, mealRecord.orderNumber)"
        [date]="selectedDateControl.value"
        [mealRecords]="mealDiary.mealRecords"
        [mealRecord]="mealRecord"
        [collapsed]="updatedMealRecordId !== mealRecord.id"
      ></mpg-meal-record-item-v2>
      <div class="empty"></div>
      }
      <div (click)="handleCreateMeal()" class="new-meal-block ion-activatable">
        <ion-ripple-effect></ion-ripple-effect>
        <ion-icon name="add"></ion-icon>
      </div>
      <div class="bottom-placeholder"></div>
    </ion-col>
  </ion-row>
</ion-content>
