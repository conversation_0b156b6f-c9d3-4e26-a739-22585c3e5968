import { Component, OnInit } from '@angular/core';
import { ViewDidEnter } from '@ionic/angular';
import {
  AlertService,
  DateService,
  Locale,
  LocalizationService,
  ModalService,
  ScrollService,
  ToastService,
} from '../../../shared/services';
import { FormControl } from '@angular/forms';
import { EMPTY, forkJoin, Observable, switchMap } from 'rxjs';
import {
  FoodRecordRequest,
  LatestMealRecord,
  MealDiary,
  MealRecord,
} from '../../../nutrition/models';
import {
  MealDiaryService,
  MealRecordService,
  NutritionNavbarAction,
  NutritionNavbarService,
} from '../../../nutrition/services';
import { ActivatedRoute, Router } from '@angular/router';
import { map } from 'rxjs/operators';
import { FoodSearchModalComponent } from '../../../nutrition/components/food-search-modal/food-search-modal.component';
import { QuickAddModalComponent } from '../../../nutrition/components/quick-add-modal/quick-add-modal.component';
import { SubscriptionPlanType } from '../../../payments/enumerations';
import { SubscriptionPlanService } from '../../../payments/services';
import { MealIdeasExploreModalComponent } from '../../../nutrition/components/meal-ideas-explore-modal/meal-ideas-explore-modal.component';
import { ApexOptions } from 'ng-apexcharts';
import { MealRecordSelectorModalComponent } from '../../../nutrition/components/meal-record-selector-modal/meal-record-selector-modal.component';

@Component({
  selector: 'mpg-trainee-nutrition',
  templateUrl: './trainee-nutrition.page.html',
  styleUrls: ['./trainee-nutrition.page.scss'],
})
export class TraineeNutritionPage implements OnInit, ViewDidEnter {
  mealDiary: MealDiary;
  latestMealRecord: LatestMealRecord;
  selectedDateControl: FormControl<string>;
  locale$: Observable<Locale>;
  chartOptions: ApexOptions = {
    chart: {
      height: 350,
      type: 'radialBar',
    },
  };
  caloriesGoal = 2000;
  isLoading = true;
  updatedMealRecordId: string;

  constructor(
    private dateService: DateService,
    private modalService: ModalService,
    private localizationService: LocalizationService,
    private mealDiaryService: MealDiaryService,
    private mealRecordService: MealRecordService,
    private alertService: AlertService,
    private toastService: ToastService,
    private subscriptionPlanService: SubscriptionPlanService,
    private nutritionNavbarService: NutritionNavbarService,
    private scrollService: ScrollService,
    private router: Router,
    private route: ActivatedRoute,
  ) {}

  get latestMealRecordDateTime(): string {
    return this.dateService.getDateTime(
      this.latestMealRecord.date,
      this.latestMealRecord.time,
    );
  }

  handleRefresh(event: any) {
    this.ionViewDidEnter();
    event.target.complete();
  }

  ngOnInit() {
    this.selectedDateControl = new FormControl<string>(null);
    this.selectedDateControl.valueChanges
      .pipe(
        switchMap((date) => {
          this.router.navigate([], { queryParams: { date } });

          return forkJoin({
            mealDiary: this.mealDiaryService.getMealDiary(date),
            latestMealRecord: this.mealRecordService.getLatest(date),
          });
        }),
      )
      .subscribe(({ mealDiary, latestMealRecord }) => {
        this.mealDiary = mealDiary;
        this.latestMealRecord = latestMealRecord;
        this.isLoading = false;
        this.buildChart();
      });

    this.locale$ = this.localizationService.locale$;
    this.nutritionNavbarService.navbarAction$.subscribe((action) => {
      switch (action) {
        case NutritionNavbarAction.SEARCH:
          this.handleLogFood();
          break;
        case NutritionNavbarAction.QUICK_ADD:
          this.handleQuickAdd();
          break;
        case NutritionNavbarAction.BARCODE:
          this.handleLogFood({ barcodeScannerOnStart: true });
          break;
      }
    });
  }

  ionViewDidEnter(): void {
    this.route.queryParamMap
      .pipe(map((queryParamMap) => queryParamMap.get('date')))
      .subscribe((date) => {
        this.selectedDateControl.setValue(
          date || this.dateService.getCurrentDate(),
        );
      });
  }

  handleDateBackClick() {
    this.selectedDateControl.setValue(
      this.dateService.getDateMinusDays(this.selectedDateControl.value, 1),
    );
  }

  handleDateForwardClick() {
    this.selectedDateControl.setValue(
      this.dateService.getDatePlusDays(this.selectedDateControl.value, 1),
    );
  }

  handleLogFood({
    mealRecord,
    barcodeScannerOnStart,
  }: { mealRecord?: MealRecord; barcodeScannerOnStart?: boolean } = {}) {
    this.modalService
      .create<MealDiary>({
        component: FoodSearchModalComponent,
        componentProps: {
          date: this.selectedDateControl.value,
          mealRecords: this.mealDiary?.mealRecords,
          mealOrderNumber: mealRecord?.orderNumber,
          barcodeScannerOnStart,
          onChange: (mealDiary: MealDiary) => {
            this.handleMealRecordUpdate(mealDiary);
          },
          onMealRecordCreate: (newMealRecord: MealRecord) => {
            this.handleMealRecordCreated(newMealRecord);
          },
        },
      })
      .subscribe((mealDiary: MealDiary) => {
        this.handleMealRecordUpdate(mealDiary);
      });
  }

  handleCreateMeal() {
    this.mealDiaryService
      .createMealRecordCreationModal(
        this.mealDiary?.mealRecords?.length + 1 || 1,
        this.selectedDateControl.value,
      )
      .subscribe((mealRecord) => {
        this.handleMealRecordCreated(mealRecord);
      });
  }

  handleDateChange(event: any) {
    this.selectedDateControl.setValue(
      this.dateService.getSimpleDate(event.detail.value),
    );
  }

  handleMealRecordCreated(newMealRecord: MealRecord) {
    if (!this.mealDiary) {
      this.selectedDateControl.setValue(this.selectedDateControl.value);
      return;
    }

    this.mealDiary.mealRecords =
      this.mealDiary.mealRecords.concat(newMealRecord);
  }

  handleMealRecordUpdate(mealDiary: MealDiary, orderNumber?: number) {
    this.mealDiary = mealDiary;
    this.buildChart();

    const mealRecord = orderNumber
      ? this.mealDiary.mealRecords.find(
          (mealRecord) => mealRecord.orderNumber === orderNumber,
        )
      : this.mealDiary.mealRecords[this.mealDiary.mealRecords.length - 1];

    this.updatedMealRecordId = mealRecord.id;
    this.scrollService.scrollToElementById(mealRecord.id);
  }

  async handleCopyMeals() {
    await this.subscriptionPlanService.showPaidFeatureModalIfNeeded(
      SubscriptionPlanType.ADVANCED,
    );

    this.modalService
      .createSimpleInput({
        title: 'nutrition.copy-meals',
        label: 'date',
        value: this.dateService.getCurrentDate(),
        isRequired: true,
        type: 'datetime',
        datetimePresentation: 'date',
        invalidDates: [this.selectedDateControl.value],
        iconSrc: '/assets/icon/duplicate.svg',
      })
      .subscribe((copyToDate: string) => {
        this.alertService.createConfirmAlert(
          'nutrition.copy-meals-confirm',
          () => {
            this.mealDiaryService
              .copyMealRecords(this.mealDiary.id, copyToDate)
              .subscribe(() => {
                this.selectedDateControl.setValue(copyToDate);

                this.toastService.showInfoToast('nutrition.copy-meals-success');
              });
          },
          {
            date: this.dateService.getLocalizedMediumDate(
              this.selectedDateControl.value,
            ),
            copyToDate: this.dateService.getLocalizedMediumDate(copyToDate),
          },
        );
      });
  }

  async handleQuickAdd() {
    if (
      await this.subscriptionPlanService.showPaidFeatureModalIfNeeded(
        SubscriptionPlanType.ADVANCED,
      )
    ) {
      return;
    }

    this.modalService
      .createBreakpointModal<FoodRecordRequest>({
        component: QuickAddModalComponent,
        componentProps: {
          date: this.selectedDateControl.value,
          time: this.dateService.getCurrentTime(),
        },
      })
      .pipe(
        switchMap((quickAdd: FoodRecordRequest | MealDiary) => {
          if ('date' in quickAdd) {
            const lastOrderNumber = quickAdd.mealRecords.length;
            this.handleMealRecordUpdate(quickAdd, lastOrderNumber);
            this.toastService.showInfoToast(
              'nutrition.ai-meal-recognition-success',
            );
            return EMPTY;
          }

          let mealRecordNumberObservable: Observable<number>;
          if (this.mealDiary?.mealRecords?.length) {
            mealRecordNumberObservable = this.modalService.create<number>({
              component: MealRecordSelectorModalComponent,
              componentProps: {
                date: this.selectedDateControl.value,
                mealRecords: this.mealDiary?.mealRecords,
                onMealRecordCreate: (mealRecord: MealRecord) => {
                  this.handleMealRecordCreated(mealRecord);
                },
              },
            });
          } else {
            mealRecordNumberObservable = this.mealDiaryService
              .createMealRecordCreationModal(
                this.mealDiary?.mealRecords?.length + 1 || 1,
                this.selectedDateControl.value,
              )
              .pipe(map((mealRecord) => mealRecord.orderNumber));
          }

          return mealRecordNumberObservable.pipe(
            map((orderNumber) => {
              return {
                foodRecord: quickAdd,
                orderNumber,
              };
            }),
          );
        }),
        switchMap(({ foodRecord, orderNumber }) => {
          return this.mealDiaryService
            .createFoodRecord(
              this.selectedDateControl.value,
              orderNumber,
              foodRecord,
            )
            .pipe(
              map((mealDiary) => {
                return {
                  mealDiary,
                  orderNumber,
                };
              }),
            );
        }),
      )
      .subscribe(({ mealDiary, orderNumber }) => {
        this.handleMealRecordUpdate(mealDiary, orderNumber);
      });

    // this.modalService
    //   .create<MealDiary>(
    //     {
    //       component: QuickAddModalComponent,
    //       componentProps: {
    //         date: this.selectedDateControl.value,
    //         mealRecords: this.mealDiary?.mealRecords,
    //         mealOrderNumber: mealRecord?.orderNumber,
    //         onChange: (mealDiary: MealDiary) => {
    //           this.mealDiary = mealDiary;
    //         },
    //         onMealRecordCreate: (newMealRecord: MealRecord) => {
    //           this.handleMealRecordCreated(newMealRecord);
    //         },
    //       },
    //     },
    //     'fullscreen',
    //   )
    //   .subscribe((mealDiary: MealDiary) => {
    //     this.mealDiary = mealDiary;
    //   });
  }

  async handleExploreIdeas() {
    if (
      await this.subscriptionPlanService.showPaidFeatureModalIfNeeded(
        SubscriptionPlanType.ADVANCED,
      )
    ) {
      return;
    }

    this.modalService
      .create({
        component: MealIdeasExploreModalComponent,
        componentProps: {},
      })
      .subscribe(() => {
        this.ionViewDidEnter();
      });
  }

  private getCaloriesPercentage() {
    const calories = this.mealDiary?.nutritionInfo?.calories;

    const percentage = (calories / this.caloriesGoal) * 100;

    return Math.min(percentage, 100) || 0;
  }

  private buildChart() {
    this.chartOptions = {
      tooltip: {
        enabled: false,
      },

      series: [this.getCaloriesPercentage()],
      chart: {
        height: 350,
        type: 'radialBar',
        offsetY: -20,
        sparkline: {
          enabled: true,
        },
        dropShadow: {
          enabled: true,
          blur: 4,
          left: 0,
          top: 0,
          opacity: 0.5,
          color: '#fff',
        },
      },
      plotOptions: {
        radialBar: {
          hollow: {
            size: '70%',
          },
          startAngle: -90,
          endAngle: 90,
          track: {
            background: '#1F1F1F',
            strokeWidth: '97%',
            margin: 5,
          },
          dataLabels: {
            name: {
              show: false,
            },
            value: {
              show: false,
            },
          },
        },
      },
      grid: {
        padding: {
          top: -10,
        },
      },
      fill: { colors: ['#F5F5FF'] },
      stroke: {
        lineCap: 'round',
      },
    };
  }
}
