import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';
import { TraineeNutritionPageRoutingModule } from './trainee-nutrition-page-routing.module';
import { TraineeNutritionPage } from './trainee-nutrition.page';
import { TranslateModule } from '@ngx-translate/core';
import { RelativeDatePipeModule } from '../../../shared/pipes/relative-date/relative-date-pipe.module';
import { NutritionInfoChipsModule } from '../../../nutrition/components/nutrition-info-chips/nutrition-info-chips.module';
import { TimeAgoPipeModule } from '../../../shared/pipes/time-ago/time-ago-pipe.module';
import { HeaderModule } from '../../../shared/components/header/header.module';
import { PageTitleModule } from '../../../shared/components/page-title/page-title.module';
import { ContentScrollDirectiveModule } from '../../../shared/directives/content-scroll/content-scroll-directive.module';
import { MealRecordItemV2ComponentModule } from '../../../nutrition/components/meal-record-item-v2/meal-record-item-v2.module';
import { ChartModule } from '../../../shared/components/chart/chart.module';
import { MacronutrientChartComponentModule } from '../../../nutrition/components/macronutrient-chart/macronutrient-chart.module';
import { DateHeaderModule } from '../../../shared/components/date-header/date-header.module';
import { ButtonModule } from '../../../shared/components/button/button.module';
import { LoadingSpinnerComponentModule } from '../../../shared/components/loading-spinner/loading-spinner.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    TraineeNutritionPageRoutingModule,
    TranslateModule,
    RelativeDatePipeModule,
    NutritionInfoChipsModule,
    TimeAgoPipeModule,
    HeaderModule,
    PageTitleModule,
    ContentScrollDirectiveModule,
    MealRecordItemV2ComponentModule,
    ChartModule,
    MacronutrientChartComponentModule,
    DateHeaderModule,
    ButtonModule,
    LoadingSpinnerComponentModule,
  ],
  declarations: [TraineeNutritionPage],
})
export class TraineeNutritionModule {}
