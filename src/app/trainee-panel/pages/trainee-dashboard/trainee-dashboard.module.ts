import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';
import { TraineeDashboardPageRoutingModule } from './trainee-dashboard-routing.module';
import { TraineeDashboardPage } from './trainee-dashboard.page';
import { PieChartModule } from '@swimlane/ngx-charts';
import { WeightModalModule } from '../../../training/components/weight-modal/weight-modal.module';
import { TranslateModule } from '@ngx-translate/core';
import { CardComponentModule } from '../../../shared/components/card/card.module';
import { DayOfWeekPipeModule } from '../../../shared/pipes/day-of-week/day-of-week-pipe.module';
import { TimeFormatPipeModule } from '../../../shared/pipes/time-format/time-format-pipe.module';
import { SleepQualityTimeFormModule } from '../../../training/components/sleep-quality-time-form/sleep-quality-time-form.module';
import { WhoopChartModule } from '../../../training/components/whoop-chart/whoop-chart.module';
import { WhoopDashboardModule } from '../../../training/components/whoop-dashboard/whoop-dashboard.module';
import { LoadingSpinnerComponentModule } from '../../../shared/components/loading-spinner/loading-spinner.module';
import { HasSubscriptionPlanDirectiveModule } from '../../../payments/directives/has-subscription-plan-directive.module';
import { HeaderModule } from '../../../shared/components/header/header.module';
import { PageTitleModule } from '../../../shared/components/page-title/page-title.module';
import { DateHeaderModule } from '../../../shared/components/date-header/date-header.module';
import { ContentScrollDirectiveModule } from '../../../shared/directives/content-scroll/content-scroll-directive.module';
import { CardV2ComponentModule } from '../../../shared/components/card-v2/card-v2.module';
import { SleepQualityCardModule } from '../../../training/components/sleep-quality-card/sleep-quality-card.module';
import { ExerciseRecordFormModule } from '../../../training/components/exercise-record-form/exercise-record-form.module';
import { BodyWeightRecordCardModule } from '../../../training/components/body-weight-record-card/body-weight-record-card.module';
import { NextWorkoutCardModule } from '../../../training/components/next-workout-card/next-workout-card.module';
import { ActiveWorkoutCardModule } from '../../../training/components/active-workout-card/active-workout-card.module';
import { DiscountsCardComponentModule } from '../../../nutrition/components/discounts-card/discounts-card.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    TraineeDashboardPageRoutingModule,
    PieChartModule,
    WeightModalModule,
    TranslateModule,
    CardComponentModule,
    DayOfWeekPipeModule,
    TimeFormatPipeModule,
    ReactiveFormsModule,
    SleepQualityTimeFormModule,
    WhoopChartModule,
    WhoopDashboardModule,
    LoadingSpinnerComponentModule,
    HasSubscriptionPlanDirectiveModule,
    HeaderModule,
    PageTitleModule,
    DateHeaderModule,
    ContentScrollDirectiveModule,
    CardV2ComponentModule,
    SleepQualityCardModule,
    ExerciseRecordFormModule,
    BodyWeightRecordCardModule,
    NextWorkoutCardModule,
    ActiveWorkoutCardModule,
    DiscountsCardComponentModule,
  ],
  declarations: [TraineeDashboardPage],
})
export class TraineeDashboardModule {}
