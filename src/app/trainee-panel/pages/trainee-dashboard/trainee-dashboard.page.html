<div
  [class.no-border-radius]="isLoading || nextWorkoutLoading"
  class="hero-block"
>
  <mpg-header></mpg-header>
  <mpg-date-header
    (onCollapse)="scrollable = true"
    (onExpand)="scrollable = false"
  ></mpg-date-header>
</div>
<div class="fake-background" style="top: 130px"></div>
<ion-content [scrollY]="scrollable" mpgContentScroll>
  <mpg-loading-spinner
    [isLoading]="isLoading || nextWorkoutLoading"
  ></mpg-loading-spinner>
  <ion-refresher (ionRefresh)="handleRefresh($event)" slot="fixed">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>

  <mpg-page-title [border]="false" title="dashboard"></mpg-page-title>
  <ion-row>
    @if (activeWorkoutRecord && !nextWorkoutLoading) {
    <ion-col offset-lg="3" size="12" size-lg="6">
      <mpg-active-workout-card
        (onOpen)="handleActiveWorkoutOpen()"
        [workoutRecord]="activeWorkoutRecord"
      ></mpg-active-workout-card>
    </ion-col>
    }
    <ion-col offset-lg="3" size="12" size-lg="6">
      <mpg-body-weight-record-card></mpg-body-weight-record-card>
    </ion-col>
    <ion-col offset-lg="3" size="12" size-lg="6">
      <mpg-sleep-quality-card></mpg-sleep-quality-card>
    </ion-col>
    @if ((nextWorkout && !activeWorkoutRecord) || nextWorkoutLoading) {
    <ion-col offset-lg="3" size="12" size-lg="6">
      <mpg-next-workout-card
        (onLoadingChange)="handleNextWorkoutLoadingChange($event)"
        [workout]="nextWorkout"
      ></mpg-next-workout-card>
    </ion-col>
    }
    <ion-col offset-lg="3" size="12" size-lg="6">
      <mpg-discounts-card></mpg-discounts-card>
    </ion-col>
    <div class="bottom-placeholder"></div>
  </ion-row>
</ion-content>
