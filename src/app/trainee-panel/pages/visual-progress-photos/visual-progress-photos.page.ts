import { Component, OnInit } from '@angular/core';
import { ViewDidEnter } from '@ionic/angular';
import {
  Locale,
  LocalizationService,
  ModalService,
  PopoverService,
} from '../../../shared/services';
import {
  BodyMeasurementService,
  BodyPhotoService,
} from '../../../training/services';
import { BodyPhoto, BodyPhotoHistory } from '../../../training/models';
import { Button, Page } from '../../../shared/models';
import { EMPTY, Observable } from 'rxjs';
import { PlatformService } from '../../../shared/services/platform.service';
import { tap } from 'rxjs/operators';

@Component({
  selector: 'mpg-visual-progress-photos',
  templateUrl: './visual-progress-photos.page.html',
  styleUrls: ['./visual-progress-photos.page.scss'],
})
export class VisualProgressPhotosPage implements OnInit, ViewDidEnter {
  history: Page<BodyPhotoHistory>;
  page = 1;
  locale$: Observable<Locale>;
  mode: string;

  sliderView = false;
  sliderPhotoHistory: BodyPhotoHistory;

  constructor(
    private modalService: ModalService,
    private bodyPhotoService: BodyPhotoService,
    private bodyMeasurementService: BodyMeasurementService,
    private localizationService: LocalizationService,
    private popoverService: PopoverService,
    private platformService: PlatformService,
  ) {}

  onModalChange = (date: string, photos: BodyPhoto[]) => {
    const index = this.history.content.findIndex((h) => h.date === date);
    if (index < 0) {
      this.history.content = this.history.content
        .concat({ date, photos })
        .sort((a, b) => b.date.localeCompare(a.date));
      return;
    } else {
      this.history.content[index].photos = photos;
    }

    this.history.content = this.history.content.filter(
      (h) => h.photos.length > 0,
    );
  };

  ngOnInit(): void {
    this.locale$ = this.localizationService.locale$;
    this.mode = this.platformService.mode;
  }

  handleRefresh(event: any) {
    this.ionViewDidEnter();
    event.target.complete();
  }

  ionViewDidEnter() {
    this.fetchHistory(this.page).subscribe();
  }

  handleUploadPhotos(date?: string) {
    this.bodyPhotoService.showBodyPhotosModal({
      date,
      onChange: this.onModalChange,
    });
  }

  getIconButtons(photoHistory: BodyPhotoHistory): Button[] {
    return [
      {
        label: 'buttons.edit',
        handler: () => {
          this.handleUploadPhotos(photoHistory.date);
        },
        icon: 'camera',
      },
      {
        label: 'measurements',
        handler: () => {
          this.bodyMeasurementService
            .createBodyMeasurementsModal(photoHistory.date)
            .subscribe();
        },
        iconSrc: '/assets/icon/meter.svg',
        color: 'secondary',
      },
    ];
  }

  handleOpenPhoto(url: string) {
    this.modalService.showPhoto(url);
  }

  handlePopoverMenu(event: MouseEvent) {
    this.popoverService.handlePopoverMenu(event, [
      {
        label: 'visual-progress.slider-view',
        handler: () => {
          this.sliderView = true;
          this.sliderPhotoHistory =
            this.history.content[this.history.content.length - 1];
        },
        disabled: () => this.sliderView,
      },
      {
        label: 'visual-progress.normal-view',
        handler: () => {
          this.sliderView = false;
        },
        disabled: () => !this.sliderView,
      },
    ]);
  }

  handleSliderChange(event: any) {
    const step = event.detail.value;
    const index = this.history.content.length - 1 - step;
    this.sliderPhotoHistory = this.history.content[index];
  }

  handleInfiniteScroll(event: any) {
    this.fetchHistory(++this.page)
      .pipe(
        tap(() => {
          event.target.complete();
        }),
      )
      .subscribe();
  }

  private fetchHistory(page: number) {
    if (this.page === 1 && this.history) {
      return EMPTY;
    }

    return this.bodyPhotoService.getHistory(page).pipe(
      tap((history) => {
        if (!this.history) {
          this.history = history;
          return;
        }

        this.history = {
          ...history,
          content: this.history.content.concat(history.content),
        };
      }),
    );
  }
}
