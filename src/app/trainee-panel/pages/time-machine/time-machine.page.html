<ion-header>
  <ion-toolbar color="dark">
    <ion-buttons slot="start">
      <ion-menu-button></ion-menu-button>
    </ion-buttons>
    <ion-title> {{ "time-machine" | translate }}</ion-title>
  </ion-toolbar>
  <ion-toolbar color="dark">
    <div class="margin-auto flex-space-around ion-padding-horizontal">
      <ion-text>{{ "from" | translate }}</ion-text>
      <ion-datetime-button datetime="from"></ion-datetime-button>
      <ion-text>{{ "to" | translate }}</ion-text>
      <ion-datetime-button datetime="to"></ion-datetime-button>
    </div>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-row>
    <ion-col offset-lg="3" size="12" size-lg="6">
      <mpg-card [title]="'training.compare-prs' | translate">
        <ion-toolbar color="dark">
          <form [formGroup]="exerciseFormGroup">
            <ion-select
              [mode]="mode"
              [selectedText]="'exercises.' + exerciseFormGroup.controls.exercise.getRawValue()?.id | translate : {fallback: exerciseFormGroup.controls.exercise.getRawValue()?.name}"
              class="centered"
              formControlName="exercise"
              interface="popover"
              labelPlacement="stacked"
              >
              @for (exercise of doneExercises; track exercise; let first = $first) {
                <ion-select-option
                  [value]="exercise"
                  >{{ "exercises." + exercise.id | translate: { fallback:
                  exercise.name } }}
                </ion-select-option>
              }
            </ion-select>
          </form>
        </ion-toolbar>
        <div class="period">
          <ion-chip class="dark"
            >{{ datesFormGroup.controls.from.getRawValue() | date : 'mediumDate'
            : undefined : (locale$ | async) }}
          </ion-chip>
          <ion-chip class="dark"
            >{{ datesFormGroup.controls.to.getRawValue() | date : 'mediumDate' :
            undefined : (locale$ | async) }}
          </ion-chip>
        </div>
        @if (prSetComparisons.length > 0) {
          <ion-row class="border-bottom">
            <ion-col size="3">
              <ion-text color="dark"
                ><h2>{{ "training.reps" | translate }}</h2></ion-text
                >
              </ion-col>
              <ion-col size="3">
                <ion-text color="dark"
                  ><h2>{{ "weight.kg" | translate }}</h2></ion-text
                  >
                </ion-col>
                <ion-col size="3">
                  <ion-text color="dark"
                    ><h2>{{ "weight.kg" | translate }}</h2></ion-text
                    >
                  </ion-col>
                  <ion-col size="3">
                    <ion-text color="dark"><h2>%</h2></ion-text>
                  </ion-col>
                </ion-row>
              }
              @if (prSetComparisons.length === 0) {
                <div class="ion-padding">
                  <ion-text color="dark"
                    ><h2>{{ 'training.no-prs-made' | translate }}</h2></ion-text
                    >
                  </div>
                }
                @for (
                  prSetComparison of prSetComparisons; track
                  prSetComparison; let last = $last; let index = $index) {
                  <ion-row
          [ngClass]="{
            'border-bottom': !last
          }"
                    >
                    <ion-col class="flex-centered" size="3">
                      <ion-text color="dark"
                        ><h2>{{ prSetComparison.reps }}</h2></ion-text
                        >
                      </ion-col>
                      <ion-col
                        (click)="handleSetRecordClick(prSetComparison.prevSet)"
                        [ngClass]="{clickable: prSetComparison.prevSet}"
                        size="3"
                        >
                        <ion-text color="dark"
                          ><h2>
                          {{ prSetComparison.prevSet?.weightInKg || '-' }}
                        </h2></ion-text
                        >
                      </ion-col>
                      <ion-col
                        (click)="handleSetRecordClick(prSetComparison.currentSet)"
                        [ngClass]="{clickable: prSetComparison.currentSet}"
                        size="3"
                        >
                        <ion-text color="dark"
                          ><h2>
                          {{ prSetComparison.currentSet?.weightInKg || '-' }}
                        </h2></ion-text
                        >
                      </ion-col>
                      <ion-col size="3">
                        <ion-text color="dark"
                          ><h2>
                          {{ prSetComparison.diff === null ? '-' : prSetComparison.diff +
                          '%' }}
                        </h2></ion-text
                        >
                      </ion-col>
                    </ion-row>
                  }
                </mpg-card>
              </ion-col>
            </ion-row>
          </ion-content>

          <form [formGroup]="datesFormGroup">
            <ion-modal [keepContentsMounted]="true">
              <ng-template>
                <ion-datetime
                  [locale]="locale$ | async"
                  [max]="datesFormGroup.controls.to.getRawValue() || undefined"
                  formControlName="from"
                  id="from"
                  presentation="date"
                ></ion-datetime>
              </ng-template>
            </ion-modal>
            <ion-modal [keepContentsMounted]="true">
              <ng-template>
                <ion-datetime
                  [locale]="locale$ | async"
                  [min]="datesFormGroup.controls.from.getRawValue() || undefined"
                  formControlName="to"
                  id="to"
                  presentation="date"
                ></ion-datetime>
              </ng-template>
            </ion-modal>
          </form>
