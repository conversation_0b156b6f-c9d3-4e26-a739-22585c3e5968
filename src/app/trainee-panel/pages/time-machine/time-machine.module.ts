import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { TimeMachineRoutingModule } from './time-machine-routing.module';
import { TranslateModule } from '@ngx-translate/core';
import { CardComponentModule } from '../../../shared/components/card/card.module';
import { MaxLengthDirectiveModule } from '../../../shared/directives/max-length/max-length-directive.module';
import { TimeMachinePage } from './time-machine.page';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    TimeMachineRoutingModule,
    TranslateModule,
    CardComponentModule,
    MaxLengthDirectiveModule,
    ReactiveFormsModule,
  ],
  declarations: [TimeMachinePage],
})
export class TimeMachineModule {}
