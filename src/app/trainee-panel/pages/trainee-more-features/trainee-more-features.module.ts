import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { TraineeMoreFeaturesPageRoutingModule } from './trainee-more-features-routing.module';

import { TraineeMoreFeaturesPage } from './trainee-more-features.page';
import { HeaderModule } from '../../../shared/components/header/header.module';
import { PageTitleModule } from '../../../shared/components/page-title/page-title.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    TraineeMoreFeaturesPageRoutingModule,
    HeaderModule,
    PageTitleModule,
  ],
  declarations: [TraineeMoreFeaturesPage],
})
export class TraineeMoreFeaturesPageModule {}
