import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';
import { TraineePanelTabsPage } from './trainee-panel-tabs.page';
import { TraineePanelTabsPageRoutingModule } from './trainee-panel-tabs-routing.module';
import { TranslateModule } from '@ngx-translate/core';
import { NavbarDirectiveModule } from '../../../shared/directives/navbar/navbar-directive.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    TraineePanelTabsPageRoutingModule,
    TranslateModule,
    NavbarDirectiveModule,
  ],
  declarations: [TraineePanelTabsPage],
})
export class TraineePanelTabsPageModule {}
