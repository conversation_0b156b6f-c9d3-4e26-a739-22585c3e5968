<mpg-header></mpg-header>
<ion-toolbar color="dark">
  <ion-segment
    (ionChange)="handleSegmentChange($event)"
    [value]="selectedSegment"
    >
    <ion-segment-button value="split">
      <ion-label>{{ 'training.split' | translate }}</ion-label>
    </ion-segment-button>
    <ion-segment-button value="history">
      <ion-label>{{ 'history' | translate }}</ion-label>
    </ion-segment-button>
  </ion-segment>
</ion-toolbar>
<ion-content mpgContentScroll>
  <ion-refresher (ionRefresh)="handleRefresh($event)" slot="fixed">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>
  @if (selectedSegment === 'split') {
    <ion-row>
      @for (workout of activeTrainingSplit?.workouts; track workout) {
        <ion-col
          offset-lg="3"
          size="12"
          size-lg="6"
          >
          <mpg-workout-card
            [button]="getWorkoutButton(workout)"
            [chipTitle]="workout.id === nextWorkout?.workout.id ? 'training.next' : undefined"
            [isEditable]="false"
            [title]="((workout.id === nextWorkout?.workout.id ? 'training.workout-short' : 'training.workout') | translate) + ' ' + workout.orderNumber"
            [workout]="workout"
          ></mpg-workout-card>
        </ion-col>
      }
    </ion-row>
    @if (activeTrainingSplit?.workouts?.length === 0) {
      <ion-row class="h-full">
        <ion-col class="flex-column-centered" offset-lg="3" size="12" size-lg="6">
          <ion-card>
            <ion-card-content>
              <ion-text class="ion-text-center" color="dark">
                <p>{{ 'training.no-workouts' | translate }}</p>
              </ion-text>
            </ion-card-content>
          </ion-card>
        </ion-col>
      </ion-row>
    }
  }
  @if (selectedSegment === 'history') {
    <ion-row>
      @for (workoutRecord of workoutRecordHistory?.content; track workoutRecord) {
        <ion-col
          offset-lg="3"
          size="12"
          size-lg="6"
          >
          <mpg-workout-record-card
            (onPRMessageClear)="handlePRMessageClear()"
            (onWorkoutRecordDelete)="handleWorkoutRecordDelete($event)"
            [finished]="finishedRecordId === workoutRecord.id"
            [title]="('training.workout' | translate) + ' ' + workoutRecord.workout.orderNumber"
            [workoutRecord]="workoutRecord"
            >
          </mpg-workout-record-card>
        </ion-col>
      }
    </ion-row>
    <ion-infinite-scroll
      (ionInfinite)="handleInfiniteScroll($event)"
      [disabled]="workoutRecordHistory?.last"
      threshold="100px"
      >
      <ion-infinite-scroll-content
        class="mpg-infinite-scroll-content"
        loadingSpinner="bubbles"
        loadingText="Loading..."
        >
      </ion-infinite-scroll-content>
    </ion-infinite-scroll>
  }
</ion-content>
