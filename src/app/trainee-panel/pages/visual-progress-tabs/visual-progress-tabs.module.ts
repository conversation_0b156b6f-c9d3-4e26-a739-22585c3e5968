import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';
import { VisualProgressTabsPage } from './visual-progress-tabs.page';
import { VisualProgressTabsPageRoutingModule } from './visual-progress-tabs-routing.module';
import { TranslateModule } from '@ngx-translate/core';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    VisualProgressTabsPageRoutingModule,
    TranslateModule,
  ],
  declarations: [VisualProgressTabsPage],
})
export class VisualProgressTabsPageModule {
  static readonly DEFAULT_URL = '/trainee/visual-progress/tabs/photos';
}
