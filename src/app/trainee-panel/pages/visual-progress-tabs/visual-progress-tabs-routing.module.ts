import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { VisualProgressTabsPage } from './visual-progress-tabs.page';

const DEFAULT_URL = '/trainee/visual-progress/tabs/photos';

const routes: Routes = [
  {
    path: 'tabs',
    component: VisualProgressTabsPage,
    children: [
      {
        path: '',
        redirectTo: DEFAULT_URL,
        pathMatch: 'full',
      },
      {
        path: 'photos',
        loadChildren: () =>
          import(
            '../visual-progress-photos/visual-progress-photos.module'
          ).then((m) => m.VisualProgressPhotosModule),
      },
      {
        path: 'measurements',
        loadChildren: () =>
          import(
            '../visual-progress-measurements/visual-progress-measurements.module'
          ).then((m) => m.VisualProgressMeasurementsModule),
      },
    ],
  },
  {
    path: '',
    redirectTo: DEFAULT_URL,
    pathMatch: 'full',
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class VisualProgressTabsPageRoutingModule {}
