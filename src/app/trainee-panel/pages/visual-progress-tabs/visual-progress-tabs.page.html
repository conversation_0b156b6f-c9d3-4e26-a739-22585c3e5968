<ion-tabs>
  <ion-tab-bar color="dark" slot="bottom">
    <ion-tab-button tab="photos">
      <ion-icon name="camera"></ion-icon>
      <ion-label>{{ 'photos' | translate }}</ion-label>
    </ion-tab-button>
    <ion-tab-button tab="measurements">
      <ion-icon src="/assets/icon/meter.svg"></ion-icon>
      <ion-label>{{ 'measurements' | translate }}</ion-label>
    </ion-tab-button>
  </ion-tab-bar>
</ion-tabs>
