.header {
  margin-top: 1px;
  padding-top: 16px;
  box-shadow: 0 -0.25px 0 var(--color-background-primary-black-300);

  ion-label {
    font-weight: 350;
    font-size: 30px;
    color: var(--color-background-primary-black-400);
    cursor: pointer;

    &:first-of-type {
      color: var(--color-background-secondary-white-600);
      margin-right: 16px;
    }
  }
}

.training-split-selector {
  position: relative;

  .split {
    margin-top: 16px;
    position: relative;
    border-radius: var(--radius-medium-small);
    background: var(--color-background-primary-black-700);
    height: 48px;
    padding: 4px 32px;
    color: var(--color-background-secondary-white-600);
    font-size: 20px;
    font-weight: 350;
    width: calc(100% - 20px);

    &::before {
      content: "";
      position: absolute;
      top: -1px;
      left: -1px;
      right: -1px;
      bottom: -1px;
      background: linear-gradient(45deg, var(--color-background-primary-black-700), var(--color-background-primary-black-400));
      border-radius: inherit;
      z-index: -1;
    }
  }

  .change {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);

    ion-icon {
      font-size: 24px;
    }
  }
}
