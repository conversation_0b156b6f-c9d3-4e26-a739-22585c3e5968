import { Component, OnInit } from '@angular/core';
import { NavController, ViewDidEnter } from '@ionic/angular';
import { TrainingSplitService } from '../../../training/services';
import { TrainingSplit } from '../../../training/models';
import { ModalService } from '../../../shared/services';
import { TrainingSplitsModalComponent } from '../../../training/components/training-splits-modal/training-splits-modal.component';

@Component({
  selector: 'mpg-trainee-training-split',
  templateUrl: './trainee-training-split.page.html',
  styleUrls: ['./trainee-training-split.page.scss'],
})
export class TraineeTrainingSplitPage implements OnInit, ViewDidEnter {
  activeTrainingSplit: TrainingSplit;

  constructor(
    private navController: NavController,
    private trainingSplitService: TrainingSplitService,
    private modalService: ModalService,
  ) {}

  ngOnInit() {}

  ionViewDidEnter() {
    this.trainingSplitService.getActive().subscribe((trainingSplit) => {
      this.activeTrainingSplit = trainingSplit;
    });
  }

  handleNavigateToSessions() {
    this.navController.navigateForward('/trainee/tabs/training/sessions', {
      animated: false,
    });
  }

  handleTrainingSplits() {
    this.modalService
      .create({
        component: TrainingSplitsModalComponent,
        componentProps: {},
      })
      .subscribe();
  }

  handleModifySplit() {
    this.trainingSplitService
      .createModifyModal(this.activeTrainingSplit)
      .subscribe(() => {
        this.ionViewDidEnter();
      });
  }
}
