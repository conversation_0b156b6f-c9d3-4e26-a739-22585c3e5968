import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { TraineeTrainingSplitPageRoutingModule } from './trainee-training-split-routing.module';

import { TraineeTrainingSplitPage } from './trainee-training-split.page';
import { HeaderModule } from '../../../shared/components/header/header.module';
import { PageTitleModule } from '../../../shared/components/page-title/page-title.module';
import { TranslateModule } from '@ngx-translate/core';
import { WorkoutCardV2ComponentModule } from '../../../training/components/workout-card-v2/workout-card-v2.module';
import { ButtonModule } from '../../../shared/components/button/button.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    TraineeTrainingSplitPageRoutingModule,
    HeaderModule,
    PageTitleModule,
    TranslateModule,
    WorkoutCardV2ComponentModule,
    ButtonModule,
  ],
  declarations: [TraineeTrainingSplitPage],
})
export class TraineeTrainingSplitPageModule {}
