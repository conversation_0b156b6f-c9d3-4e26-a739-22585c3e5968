import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MyPOSModalModule } from '../components/mypos-modal/mypos-modal.module';
import { SubscriptionPlanFormModalModule } from '../components/subscription-plan-form-modal/subscription-plan-form-modal.module';
import { BillingInformationFormModalModule } from '../components/billing-information-form-modal/billing-information-form-modal.module';
import { InvoiceFormModalModule } from '../components/invoice-form-modal/invoice-form-modal.module';
import { VerificationCodeModalModule } from '../components/verification-code-modal/verification-code-modal.module';
import { SubscriptionFormModalModule } from '../components/subscription-form-modal/subscription-form-modal.module';
import { PaidFeatureModalModule } from '../components/paid-feature-modal/paid-feature-modal.module';

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    MyPOSModalModule,
    VerificationCodeModalModule,
    SubscriptionFormModalModule,
    SubscriptionPlanFormModalModule,
    BillingInformationFormModalModule,
    InvoiceFormModalModule,
    PaidFeatureModalModule,
  ],
})
export class PaymentsModalsModule {}
