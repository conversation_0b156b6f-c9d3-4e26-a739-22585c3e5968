import {
  Currency,
  SubscriptionPlanPeriodType,
  SubscriptionPlanType,
  SubscriptionStatus,
} from '../enumerations';

export interface SubscriptionPlan {
  id: string;
  price: number;
  currency: Currency;
  type: SubscriptionPlanType;
  periodType: SubscriptionPlanPeriodType;
}

export interface Subscription {
  id: string;
  userId: string;
  subscriptionPlan: SubscriptionPlan;
  createdOn: string;
  trialEnd: string;
  currentPeriodStart: string;
  currentPeriodEnd: string;
  status: SubscriptionStatus;
}

export interface SubscriptionCreateRequest {
  userId: string;
  subscriptionPlanId: string;
  trialEnd: string;
}

export interface SubscriptionEditRequest {
  subscriptionPlanId?: string;
  trialEnd?: string;
}

export interface SubscriptionPublicEditRequest {
  subscriptionPlanId: string;
}

export interface SubscriptionPlanRequest {
  price: number;
  currency: Currency;
}

export interface ManualSubscriptionPlanRequest {
  userId: string;
  type: SubscriptionPlanType;
}

export interface SubscriptionPlanOffer {
  id: string;
  campaignName: string;
  validFrom: string;
  validTo: string;
  price: number;
  currency: Currency;
  period: string;
  productName: string;
  subscriptionPlan: SubscriptionPlan;
}

export interface PurchasedSubscriptionPlanOffer {
  id: string;
  userId: string;
  purchasedAt: string;
  subscriptionPlanOffer: SubscriptionPlanOffer;
}
