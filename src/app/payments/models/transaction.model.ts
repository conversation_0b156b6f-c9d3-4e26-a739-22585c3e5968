import { Currency } from '../enumerations';
import { Invoice } from './invoice.model';

export interface Transaction {
  id: number;
  accountNumber: string;
  ruid: string;
  referenceNumberType: number;
  billingDescriptor: string;
  pan: string;
  description: string;
  paymentReference: string;
  transactionType: string;
  transactionCurrency: Currency;
  transactionAmount: number;
  originalCurrency: Currency;
  originalAmount: number;
  date: string;
  sign: string;
  referenceNumber: string;
  terminalId: string;
  serialNumber: string;
  invoice: Invoice;
}
