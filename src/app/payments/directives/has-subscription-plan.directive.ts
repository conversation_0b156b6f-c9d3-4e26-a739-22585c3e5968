import { Directive, inject, Input } from '@angular/core';
import { NgIf } from '@angular/common';
import { UserService } from '../../auth/services';
import { SubscriptionPlanType } from '../enumerations';

@Directive({
  selector: '[mpgHasSubscriptionPlan]',
  hostDirectives: [NgIf],
})
export class HasSubscriptionPlanDirective {
  private readonly ngIfDirective = inject(NgIf);

  constructor(private userService: UserService) {}

  @Input() set mpgHasSubscriptionPlan(subscriptionPlan: SubscriptionPlanType) {
    this.userService.subscriptionPlan$.subscribe((userSubscriptionPlan) => {
      this.ngIfDirective.ngIf =
        subscriptionPlan === userSubscriptionPlan ||
        userSubscriptionPlan === SubscriptionPlanType.PREMIUM;
    });
  }
}
