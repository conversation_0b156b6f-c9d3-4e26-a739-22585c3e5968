import { Component, Input, OnInit } from '@angular/core';
import { SubscriptionPlanType } from '../../enumerations';
import { Button } from '../../../shared/models';

@Component({
  selector: 'mpg-subscription-plan-card',
  templateUrl: './subscription-plan-card.component.html',
  styleUrls: ['./subscription-plan-card.component.scss'],
})
export class SubscriptionPlanCardComponent implements OnInit {
  @Input() type: SubscriptionPlanType;
  @Input() yearly = false;
  @Input() button: Button;

  SubscriptionPlanType = SubscriptionPlanType;

  constructor() {}

  ngOnInit() {}
}
