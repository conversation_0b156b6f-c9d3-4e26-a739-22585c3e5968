import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { InvoiceFormModalComponent } from './invoice-form-modal.component';
import { ModalLayoutModule } from '../../../shared/components/modal-layout/modal-layout.module';
import { TranslateModule } from '@ngx-translate/core';
import { TimeFormatPipeModule } from '../../../shared/pipes/time-format/time-format-pipe.module';
import { EntitySelectModule } from '../../../shared/components/entity-select/entity-select.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ReactiveFormsModule,
    ModalLayoutModule,
    TranslateModule,
    TimeFormatPipeModule,
    EntitySelectModule,
  ],
  declarations: [InvoiceFormModalComponent],
  exports: [InvoiceFormModalComponent],
})
export class InvoiceFormModalModule {}
