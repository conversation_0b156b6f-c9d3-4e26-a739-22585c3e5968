import { Component, Input, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import {
  DateService,
  Locale,
  LocalizationService,
  ModalService,
} from '../../../shared/services';
import { Button } from '../../../shared/models';
import {
  BillingInformation,
  Invoice,
  InvoiceCreateRequest,
} from '../../models';
import { BillingInformationService, InvoiceService } from '../../services';
import { PlatformService } from '../../../shared/services/platform.service';
import { Observable } from 'rxjs';
import { Currency, PaymentMethod } from '../../enumerations';

@Component({
  selector: 'mpg-invoice-form-modal',
  templateUrl: './invoice-form-modal.component.html',
  styleUrls: ['./invoice-form-modal.component.scss'],
})
export class InvoiceFormModalComponent implements OnInit {
  @Input() invoice?: Invoice;
  @Input() latestInvoiceNumber = 0;

  locale$: Observable<Locale>;
  mode: string;
  currencies = Object.values(Currency);
  paymentMethods = Object.values(PaymentMethod);
  invoiceFormGroup: FormGroup;
  modalButton: Button = {
    label: 'buttons.submit',
    handler: () => this.handleSubmit(),
    disabled: () => this.invoiceFormGroup?.invalid,
  };

  private billingInformation: BillingInformation;

  constructor(
    private fb: FormBuilder,
    private modalService: ModalService,
    private billingInformationService: BillingInformationService,
    private invoiceService: InvoiceService,
    private localizationService: LocalizationService,
    private platformService: PlatformService,
    private dateService: DateService,
  ) {}

  formatter = (billingInformation: BillingInformation) => {
    if (!billingInformation.companyName) {
      return `${billingInformation.firstName} ${billingInformation.middleName} ${billingInformation.lastName}`;
    }

    return billingInformation.companyName;
  };

  searchObservableGetter = (search: string) => {
    return this.billingInformationService.search({ search });
  };

  ngOnInit() {
    this.invoiceFormGroup = this.fb.group({
      date: [
        this.invoice?.date || this.dateService.getCurrentDate(),
        Validators.required,
      ],
      number: [
        this.invoice?.number || this.latestInvoiceNumber + 1,
        Validators.required,
      ],
      productName: [this.invoice?.productName || null, Validators.required],
      amount: [this.invoice?.amount || null, Validators.required],
      currency: [this.invoice?.currency || Currency.BGN, Validators.required],
      paymentReference: [this.invoice?.paymentReference || null],
      paymentMethod: [
        this.invoice?.paymentMethod || PaymentMethod.CARD,
        Validators.required,
      ],
      billingInformationId: [
        this.invoice?.billingInformation?.id || null,
        Validators.required,
      ],
    });

    this.locale$ = this.localizationService.locale$;
    this.mode = this.platformService.mode;
  }

  handleSubmit() {
    if (this.invoiceFormGroup.invalid) {
      return;
    }

    const formValue: InvoiceCreateRequest = {
      ...this.invoiceFormGroup.value,
    };

    if (this.invoice?.id) {
      this.invoiceService.edit(this.invoice.id, formValue).subscribe(() => {
        this.modalService.closeTopModal({
          ...this.invoice,
          ...formValue,
          billingInformation:
            this.billingInformation || this.invoice.billingInformation,
        });
      });

      return;
    }

    this.invoiceService.create(formValue).subscribe((invoice) => {
      this.modalService.closeTopModal(invoice);
    });
  }

  handleNewBillingInformation(billingInformation: BillingInformation) {
    this.billingInformation = billingInformation;
  }

  getBillingInformationDisplayValue(): string {
    return this.billingInformation && this.formatter(this.billingInformation);
  }
}
