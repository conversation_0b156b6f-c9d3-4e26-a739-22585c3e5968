import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { ModalLayoutModule } from '../../../shared/components/modal-layout/modal-layout.module';
import { CardComponentModule } from '../../../shared/components/card/card.module';
import { TranslateModule } from '@ngx-translate/core';
import { SwiperDirectiveModule } from '../../../shared/directives/swiper/swiper-directive.module';
import { FoodItemModule } from '../../../nutrition/components/food-item/food-item.module';
import { SelectAllDirectiveModule } from '../../../shared/directives/select-all/select-all-directive.module';
import { TimeFormatPipeModule } from '../../../shared/pipes/time-format/time-format-pipe.module';
import { PaidFeatureModalComponent } from './paid-feature-modal.component';
import { SubscriptionPlanCardModule } from '../subscription-plan-card/subscription-plan-card.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ModalLayoutModule,
    CardComponentModule,
    TranslateModule,
    ReactiveFormsModule,
    SwiperDirectiveModule,
    FoodItemModule,
    SelectAllDirectiveModule,
    TimeFormatPipeModule,
    SubscriptionPlanCardModule,
  ],
  declarations: [PaidFeatureModalComponent],
  exports: [PaidFeatureModalComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class PaidFeatureModalModule {}
