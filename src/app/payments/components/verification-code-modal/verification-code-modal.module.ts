import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { ModalLayoutModule } from '../../../shared/components/modal-layout/modal-layout.module';
import { CardComponentModule } from '../../../shared/components/card/card.module';
import { TranslateModule } from '@ngx-translate/core';
import { VerificationCodeModalComponent } from './verification-code-modal.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ModalLayoutModule,
    CardComponentModule,
    TranslateModule,
    ReactiveFormsModule,
  ],
  declarations: [VerificationCodeModalComponent],
  exports: [VerificationCodeModalComponent],
})
export class VerificationCodeModalModule {}
