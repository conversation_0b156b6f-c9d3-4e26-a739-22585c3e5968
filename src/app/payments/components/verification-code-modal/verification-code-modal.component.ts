import { Component, OnInit } from '@angular/core';
import { ModalService } from '../../../shared/services';
import { Button } from '../../../shared/models';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';

@Component({
  selector: 'mpg-verification-code-modal',
  templateUrl: './verification-code-modal.component.html',
  styleUrls: ['./verification-code-modal.component.scss'],
})
export class VerificationCodeModalComponent implements OnInit {
  verificationCodeFormGroup: FormGroup;
  modalButton: Button = {
    label: 'buttons.submit',
    handler: () => {
      if (this.verificationCodeFormGroup.invalid) {
        return;
      }

      this.modalService.closeTopModal(this.verificationCodeFormGroup.value);
    },
    disabled: () => this.verificationCodeFormGroup?.invalid,
  };

  constructor(
    private modalService: ModalService,
    private fb: FormBuilder,
  ) {}

  ngOnInit(): void {
    this.verificationCodeFormGroup = this.fb.group({
      code: [null, Validators.required],
    });
  }
}
