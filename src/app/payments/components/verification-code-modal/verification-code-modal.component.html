<mpg-modal-layout [button]="modalButton" title="payments.verification-code">
  <form [formGroup]="verificationCodeFormGroup">
    <ion-item>
      <ion-label class="ion-text-center ion-text-wrap info flex-column-centered"
        >{{ "payments.verification-code-info" | translate }}
      </ion-label>
    </ion-item>
    <ion-item>
      <ion-input
        [label]="'payments.verification-code-placeholder' | translate"
        color="dark"
        formControlName="code"
        inputmode="numeric"
        labelPlacement="floating"
        maxlength="6"
        pattern="[0-9]*"
        type="number"
      ></ion-input>
    </ion-item>
  </form>
</mpg-modal-layout>
