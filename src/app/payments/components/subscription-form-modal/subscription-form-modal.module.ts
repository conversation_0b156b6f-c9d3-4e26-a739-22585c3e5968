import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { ModalLayoutModule } from '../../../shared/components/modal-layout/modal-layout.module';
import { CardComponentModule } from '../../../shared/components/card/card.module';
import { TranslateModule } from '@ngx-translate/core';
import { SubscriptionFormModalComponent } from './subscription-form-modal.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ModalLayoutModule,
    CardComponentModule,
    TranslateModule,
    ReactiveFormsModule,
  ],
  declarations: [SubscriptionFormModalComponent],
  exports: [SubscriptionFormModalComponent],
})
export class SubscriptionFormModalModule {}
