import { Component, OnInit } from '@angular/core';
import { ModalService, ToastService } from '../../../shared/services';
import { SubscriptionPlanService } from '../../services';
import { But<PERSON> } from '../../../shared/models';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Currency } from '../../enumerations';
import { catchError } from 'rxjs/operators';
import { PlatformService } from '../../../shared/services/platform.service';

@Component({
  selector: 'mpg-subscription-plan-form-modal',
  templateUrl: './subscription-plan-form-modal.component.html',
  styleUrls: ['./subscription-plan-form-modal.component.scss'],
})
export class SubscriptionPlanFormModalComponent implements OnInit {
  currencies = Object.values(Currency);
  mode: string;
  subscriptionPlanFormGroup: FormGroup;
  modalButton: Button = {
    label: 'buttons.submit',
    handler: () => {
      this.subscriptionPlanService
        .create(this.subscriptionPlanFormGroup.value)
        .pipe(catchError((err) => this.toastService.handleError(err)))
        .subscribe((subscriptionPlan) => {
          this.toastService.showInfoToast(
            'payments.subscription-plan-create-success',
          );
          this.modalService.closeTopModal(subscriptionPlan);
        });
    },
    disabled: () => this.subscriptionPlanFormGroup.invalid,
  };

  constructor(
    private modalService: ModalService,
    private subscriptionPlanService: SubscriptionPlanService,
    private toastService: ToastService,
    private fb: FormBuilder,
    private platformService: PlatformService,
  ) {}

  ngOnInit(): void {
    this.subscriptionPlanFormGroup = this.fb.group({
      price: [1, Validators.required],
      currency: [Currency.BGN, Validators.required],
    });

    this.mode = this.platformService.mode;
  }
}
