<mpg-modal-layout
  [button]="modalButton"
  title="payments.create-subscription-plan"
  >
  <form [formGroup]="subscriptionPlanFormGroup">
    <ion-item>
      <ion-input
        formControlName="price"
        label="Price"
        labelPlacement="floating"
        type="decimal"
      ></ion-input>
    </ion-item>
    <ion-item>
      <ion-select
        [label]="'payments.currency' | translate"
        [mode]="mode"
        class="centered"
        formControlName="currency"
        interface="popover"
        labelPlacement="stacked"
        >
        @for (currency of currencies; track currency) {
          <ion-select-option
            [value]="currency"
            >
            {{ currency }}
          </ion-select-option>
        }
      </ion-select>
    </ion-item>
  </form>
</mpg-modal-layout>
