import { Component, Input, OnInit } from '@angular/core';
import { SubscriptionPlanService } from '../../services';
import { UserService } from '../../../auth/services';
import { Observable } from 'rxjs';
import { SubscriptionPlanType } from '../../enumerations';

@Component({
  selector: 'mpg-data-history-limit-item',
  templateUrl: './data-history-limit-item.component.html',
  styleUrls: ['./data-history-limit-item.component.scss'],
})
export class DataHistoryLimitItemComponent implements OnInit {
  @Input() standalone = false;

  subscriptionPlan$: Observable<SubscriptionPlanType>;

  SubscriptionPlanType = SubscriptionPlanType;

  constructor(
    private subscriptionPlanService: SubscriptionPlanService,
    private userService: UserService,
  ) {}

  ngOnInit() {
    this.subscriptionPlan$ = this.userService.subscriptionPlan$;
  }

  handlePaidFeature() {
    this.subscriptionPlanService.showPaidFeatureModal();
  }
}
