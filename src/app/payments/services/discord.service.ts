import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../../environments/environment';
import { Observable } from 'rxjs';
import { DiscordJoinRequest } from '../models';

@Injectable({
  providedIn: 'root',
})
export class DiscordService {
  static readonly BASE_URL = `${environment.PAYMENTS_SERVICE_API_URL}/v1/discord`;

  constructor(private http: HttpClient) {}

  join(model: DiscordJoinRequest): Observable<void> {
    return this.http.post<void>(`${DiscordService.BASE_URL}/join`, model);
  }
}
