import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../../environments/environment';
import { Transaction } from '../models';
import { Page } from '../../shared/models';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class TransactionService {
  static readonly BASE_URL = `${environment.PAYMENTS_SERVICE_API_URL}/v1/transactions`;

  constructor(private http: HttpClient) {}

  getAll(): Observable<Page<Transaction>> {
    return this.http.get<Page<Transaction>>(`${TransactionService.BASE_URL}`);
  }
}
