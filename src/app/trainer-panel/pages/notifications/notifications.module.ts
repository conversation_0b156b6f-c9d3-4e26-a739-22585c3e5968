import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { NotificationsPageRoutingModule } from './notifications-routing.module';

import { NotificationsPage } from './notifications.page';
import { TranslateModule } from '@ngx-translate/core';
import { DayOfWeekPipeModule } from '../../../shared/pipes/day-of-week/day-of-week-pipe.module';
import { TimeFormatPipeModule } from '../../../shared/pipes/time-format/time-format-pipe.module';
import { MyTraineeCardModule } from '../../../training/components/my-trainee-card/my-trainee-card.module';
import { TimeAgoPipeModule } from '../../../shared/pipes/time-ago/time-ago-pipe.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    NotificationsPageRoutingModule,
    ReactiveFormsModule,
    TranslateModule,
    DayOfWeekPipeModule,
    TimeFormatPipeModule,
    MyTraineeCardModule,
    TimeAgoPipeModule,
  ],
  declarations: [NotificationsPage],
})
export class NotificationsPageModule {}
