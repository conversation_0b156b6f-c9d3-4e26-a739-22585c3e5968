import { Component, OnInit } from '@angular/core';
import { IonItemSliding, ViewDidEnter } from '@ionic/angular';
import {
  Locale,
  LocalizationService,
  ModalService,
  PopoverService,
  RedirectionService,
} from '../../../shared/services';
import { Page } from '../../../shared/models';
import { switchMap, tap } from 'rxjs/operators';
import { Observable } from 'rxjs';
import { NotificationsService } from '../../../notifications/services';
import { Notification } from '../../../notifications/models';
import { DetailedTrainee, Trainee } from '../../../training/models';
import { TraineeService } from '../../../training/services';
import { NotificationType } from '../../../notifications/enumerations';

@Component({
  selector: 'mpg-my-trainees',
  templateUrl: './notifications.page.html',
  styleUrls: ['./notifications.page.scss'],
})
export class NotificationsPage implements OnInit, ViewDidEnter {
  notificationsPage: Page<Notification>;
  locale$: Observable<Locale>;
  selectedTrainee: Trainee;
  NotificationType = NotificationType;

  private page = 1;

  constructor(
    private popoverService: PopoverService,
    private notificationsService: NotificationsService,
    private localizationService: LocalizationService,
    private modalService: ModalService,
    private traineeService: TraineeService,
    private redirectionService: RedirectionService,
  ) {}

  get selectedTraineeDisplayValue() {
    if (!this.selectedTrainee) {
      return '';
    }

    return this.selectedTrainee.firstName + ' ' + this.selectedTrainee.lastName;
  }

  ngOnInit() {
    this.locale$ = this.localizationService.locale$;
  }

  ionViewDidEnter() {
    this.page = 1;
    this.fetchNotifications()
      .pipe(
        switchMap(() => {
          return this.notificationsService.readAll();
        }),
      )
      .subscribe();
  }

  handleRefresh(event: any) {
    this.ionViewDidEnter();
    event.target.complete();
  }

  handleInfiniteScroll(event: any) {
    this.page++;
    this.fetchNotifications().subscribe(() => {
      event.target.complete();
    });
  }

  handlePopoverMenu(event: MouseEvent) {
    this.popoverService.handlePopoverMenu(event, []);
  }

  handleNotification(notification: Notification) {
    this.notificationsService.handleNotification(notification);
  }

  handleSearchClick() {
    this.modalService
      .createEntitySelector<DetailedTrainee>({
        title: 'Select trainee',
        formatter: (myTrainee: DetailedTrainee) => {
          return myTrainee.trainee.firstName + ' ' + myTrainee.trainee.lastName;
        },
        searchObservableGetter: (search) => {
          return this.traineeService.getAll({
            page: 1,
            search,
            fields: 'trainee',
          });
        },
        size: 'medium',
      })
      .pipe(
        tap((myTrainee) => {
          this.selectedTrainee = myTrainee.trainee;
          this.page = 1;
          this.notificationsPage = undefined;
        }),
        switchMap(() => this.fetchNotifications()),
      )
      .subscribe(() => {});
  }

  handleSearchClear() {
    this.selectedTrainee = undefined;
    this.page = 1;
    this.notificationsPage = undefined;
    this.fetchNotifications().subscribe();
  }

  handleSendNotification(
    itemSliding: IonItemSliding,
    notification: Notification,
  ) {
    this.notificationsService
      .getSendNotificationButton([notification.data.traineeId])
      .handler();
    itemSliding.close();
  }

  handleSelectTrainee(itemSliding: IonItemSliding, notification: Notification) {
    const traineeId = notification.data.traineeId;
    this.traineeService
      .getById(traineeId)
      .pipe(
        tap((trainee) => {
          this.selectedTrainee = trainee;
          this.page = 1;
          this.notificationsPage = undefined;
        }),
        switchMap(() => this.fetchNotifications()),
      )
      .subscribe();
  }

  handleTraineeDetails(
    itemSliding: IonItemSliding,
    notification: Notification,
  ) {
    itemSliding.close();

    const traineeId = notification.data.traineeId;
    this.redirectionService.navigate(
      `/trainer/trainee-details/tabs/${traineeId}/fitness-state`,
    );
  }

  handleImpersonateTrainee(
    itemSliding: IonItemSliding,
    notification: Notification,
  ) {
    itemSliding.close();

    const traineeId = notification.data.traineeId;
    this.redirectionService.fetchAndNavigate('/trainee', traineeId);
  }

  private fetchNotifications(): Observable<Page<Notification>> {
    return this.notificationsService
      .getAllForTrainer(this.page, this.selectedTrainee?.id)
      .pipe(
        tap((notificationsPage) => {
          if (!this.notificationsPage || this.page === 1) {
            this.notificationsPage = notificationsPage;
            return;
          }

          this.notificationsPage = {
            ...notificationsPage,
            content: this.notificationsPage.content.concat(
              notificationsPage.content,
            ),
          };
        }),
      );
  }
}
