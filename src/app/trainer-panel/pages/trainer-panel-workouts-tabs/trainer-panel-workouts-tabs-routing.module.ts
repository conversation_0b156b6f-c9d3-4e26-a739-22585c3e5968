import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { TrainerPanelWorkoutsTabsPage } from './trainer-panel-workouts-tabs.page';
import { TrainerPanelRoutingModule } from '../../trainer-panel-routing.module';

const routes: Routes = [
  {
    path: '',
    component: TrainerPanelWorkoutsTabsPage,
    children: [
      {
        path: '',
        redirectTo: TrainerPanelRoutingModule.DEFAULT_WORKOUTS_TABS_URL,
        pathMatch: 'full',
      },
      {
        path: 'my-workouts',
        loadChildren: () =>
          import('../my-workouts/my-workouts.module').then(
            (m) => m.MyWorkoutsPageModule,
          ),
      },
      {
        path: 'training-splits/:trainingSplitId',
        loadChildren: () =>
          import(
            '../../../training/pages/training-split-details/training-split-details.module'
          ).then((m) => m.TrainingSplitDetailsPageModule),
      },
      {
        path: 'my-training-splits',
        loadChildren: () =>
          import('../my-training-splits/my-training-splits.module').then(
            (m) => m.MyTrainingSplitsPageModule,
          ),
      },
    ],
  },
  {
    path: '',
    redirectTo: TrainerPanelRoutingModule.DEFAULT_WORKOUTS_TABS_URL,
    pathMatch: 'full',
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class TrainerPanelWorkoutsTabsPageRoutingModule {}
