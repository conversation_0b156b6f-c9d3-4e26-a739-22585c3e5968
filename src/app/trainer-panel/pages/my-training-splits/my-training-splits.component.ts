import { Component } from '@angular/core';
import { TrainingSplit } from '../../../training/models';
import { ViewWillEnter } from '@ionic/angular';
import { switchMap } from 'rxjs/operators';
import { CreateTrainingSplitModalComponent } from '../../../training/components/create-training-split-modal/create-training-split-modal.component';
import { Button } from '../../../shared/models';
import { Router } from '@angular/router';
import { AlertService, ModalService } from '../../../shared/services';
import {
  TraineeService,
  TrainingSplitService,
} from '../../../training/services';

@Component({
  selector: 'mpg-my-training-splits',
  templateUrl: './my-training-splits.component.html',
  styleUrls: ['./my-training-splits.component.scss'],
})
export class MyTrainingSplitsPage implements ViewWillEnter {
  trainingSplits: TrainingSplit[] = [];

  constructor(
    private modalService: ModalService,
    private trainingSplitService: TrainingSplitService,
    private router: Router,
    private alertService: AlertService,
    private traineeService: TraineeService,
  ) {}

  ionViewWillEnter(): void {
    this.fetchTrainingSplits();
  }

  handleCreateTrainingSplit() {
    this.modalService
      .create({
        component: CreateTrainingSplitModalComponent,
        canDismiss: true,
      })
      .subscribe(() => this.fetchTrainingSplits());
  }

  handleTrainingSplitDelete() {
    this.fetchTrainingSplits();
  }

  handleTrainingSplitEdit() {}

  getPopoverMenuButtons(trainingSplit: TrainingSplit): Button[] {
    return [
      {
        label: 'buttons.copy',
        handler: () => {
          this.trainingSplitService.copyAndRedirect(trainingSplit.id);
        },
        disabled: () => trainingSplit.workouts.length === 0,
      },
      {
        label: 'buttons.delete',
        handler: () => {
          this.alertService.createDeleteAlert(() => {
            this.trainingSplitService.delete(trainingSplit.id).subscribe(() => {
              this.fetchTrainingSplits();
            });
          });
        },
      },
      {
        label: 'buttons.change-name',
        handler: () => {
          this.trainingSplitService
            .createChangeNameModal(trainingSplit)
            .subscribe((name) => {
              this.trainingSplits = this.trainingSplits.map((t) => {
                if (t.id !== trainingSplit.id) {
                  return t;
                }

                return { ...t, name };
              });
            });
        },
      },
    ];
  }

  getButton(trainingSplit: TrainingSplit): Button {
    return {
      label: 'details',
      handler: () => {
        this.router.navigate([
          '/trainer/workouts-tabs/training-splits',
          trainingSplit.id,
        ]);
      },
    };
  }

  private fetchTrainingSplits() {
    this.traineeService.trainerId$
      .pipe(
        switchMap((trainerId) => {
          return this.trainingSplitService.getAll({
            trainerId,
            traineeId: 'null',
          });
        }),
      )
      .subscribe((trainingSplits) => {
        this.trainingSplits = trainingSplits;
      });
  }
}
