import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'mpg-trainee-details-tabs',
  templateUrl: './trainee-details-tabs.page.html',
  styleUrls: ['./trainee-details-tabs.page.scss'],
})
export class TraineeDetailsTabsPage implements OnInit {
  id: string;
  tab: string;

  constructor(private route: ActivatedRoute) {}

  ngOnInit() {
    this.id = this.route.snapshot.firstChild.paramMap.get('id');
    this.tab = this.route.snapshot.firstChild.routeConfig.path.replace(
      ':id/',
      ''
    );
  }
}
