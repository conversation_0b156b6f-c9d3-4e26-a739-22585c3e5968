import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { MyTraineesPageRoutingModule } from './my-trainees-routing.module';

import { MyTraineesPage } from './my-trainees.page';
import { TranslateModule } from '@ngx-translate/core';
import { DayOfWeekPipeModule } from '../../../shared/pipes/day-of-week/day-of-week-pipe.module';
import { TimeFormatPipeModule } from '../../../shared/pipes/time-format/time-format-pipe.module';
import { MyTraineeCardModule } from '../../../training/components/my-trainee-card/my-trainee-card.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    MyTraineesPageRoutingModule,
    ReactiveFormsModule,
    TranslateModule,
    DayOfWeekPipeModule,
    TimeFormatPipeModule,
    MyTraineeCardModule,
  ],
  declarations: [MyTraineesPage],
})
export class MyTraineesPageModule {}
