import { Component } from '@angular/core';
import {
  CourseService,
  TraineeGroupService,
  TraineeService,
} from '../../../training/services';
import { ViewDidEnter } from '@ionic/angular';
import { CreateTraineeModalComponent } from '../../../training/components/create-trainee-modal/create-trainee-modal.component';
import {
  Course,
  DetailedTrainee,
  TraineeGroup,
} from '../../../training/models';
import { ModalService, PopoverService } from '../../../shared/services';
import { Page } from '../../../shared/models';
import { take, tap } from 'rxjs/operators';
import { Observable, switchMap } from 'rxjs';
import { NotificationsService } from '../../../notifications/services';

@Component({
  selector: 'mpg-my-trainees',
  templateUrl: './my-trainees.page.html',
  styleUrls: ['./my-trainees.page.scss'],
})
export class MyTraineesPage implements ViewDidEnter {
  traineesPage: Page<DetailedTrainee>;
  traineeGroups: TraineeGroup[] = [];
  trainerCourses: Course[] = [];
  selectMode = false;

  selectedTraineeIds: string[] = [];
  selectedTraineeGroupId = 'all';
  private search: string;
  private page = 1;

  constructor(
    private traineeService: TraineeService,
    private modalService: ModalService,
    private popoverService: PopoverService,
    private notificationsService: NotificationsService,
    private traineeGroupService: TraineeGroupService,
    private courseService: CourseService,
  ) {}

  ionViewDidEnter() {
    this.page = 1;
    this.search = undefined;
    this.fetchTrainees().subscribe();
    this.traineeGroupService.getAll().subscribe((traineeGroups) => {
      this.traineeGroups = traineeGroups;
    });

    this.traineeService.trainerId$
      .pipe(
        take(1),
        switchMap((trainerId) => {
          return this.courseService.getAll({ trainerId });
        }),
      )
      .subscribe((courses) => {
        this.trainerCourses = courses;
      });
  }

  handleRefresh(event: any) {
    this.ionViewDidEnter();
    event.target.complete();
  }

  handleSearch(event: any) {
    this.page = 1;
    this.search = event.detail.value;
    this.fetchTrainees().subscribe();
  }

  handleCreateTrainee() {
    this.modalService
      .create({
        component: CreateTraineeModalComponent,
        canDismiss: true,
      })
      .subscribe();
  }

  handleInfiniteScroll(event: any) {
    this.page++;
    this.fetchTrainees().subscribe(() => {
      event.target.complete();
    });
  }

  handlePopoverMenu(event: MouseEvent) {
    this.popoverService.handlePopoverMenu(event, [
      {
        label: 'training.trainee-groups',
        handler: () => {
          this.traineeGroupService.showModal(
            this.traineeGroups,
            (newTraineeGroups: TraineeGroup[]) => {
              this.traineeGroups = newTraineeGroups;
              this.selectedTraineeGroupId = 'all';
              this.handleTraineeGroupChange();
            },
          );
        },
      },
      {
        label: 'training.select-trainees',
        handler: () => {
          this.selectMode = true;
        },
        disabled: () => this.selectMode,
      },
      {
        label: 'buttons.cancel-select',
        handler: () => {
          this.selectMode = false;
          this.selectedTraineeIds = [];
        },
        disabled: () => !this.selectMode,
      },
      {
        label: 'buttons.select-all',
        handler: () => {
          this.selectedTraineeIds = this.traineesPage.content.map(
            (t) => t.trainee.id,
          );
        },
        disabled: () => !this.selectMode,
      },
      {
        label: 'buttons.deselect-all',
        handler: () => {
          this.selectedTraineeIds = [];
        },
        disabled: () => !this.selectMode,
      },
      this.notificationsService.getSendNotificationButton(
        this.selectedTraineeIds,
      ),
    ]);
  }

  handleSelect(selected: boolean, traineeId: string) {
    if (!selected) {
      this.selectedTraineeIds = this.selectedTraineeIds.filter(
        (id) => id !== traineeId,
      );
      return;
    }

    if (this.selectedTraineeIds.includes(traineeId)) {
      return;
    }

    this.selectedTraineeIds = this.selectedTraineeIds.concat(traineeId);
  }

  handleTraineeGroupChange() {
    this.page = 1;
    this.fetchTrainees().subscribe();
  }

  private fetchTrainees(): Observable<Page<DetailedTrainee>> {
    return this.traineeService
      .getAll({
        page: this.page,
        search: this.search,
        groupId: this.selectedTraineeGroupId,
      })
      .pipe(
        tap((traineesPage) => {
          if (!this.traineesPage || this.page === 1) {
            this.traineesPage = traineesPage;
            return;
          }

          this.traineesPage = {
            ...traineesPage,
            content: this.traineesPage.content.concat(traineesPage.content),
          };
        }),
      );
  }
}
