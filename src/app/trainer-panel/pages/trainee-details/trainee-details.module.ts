import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { TraineeDetailsPageRoutingModule } from './trainee-details-routing.module';

import { TraineeDetailsPage } from './trainee-details.page';
import { CreateFitnessStateModalModule } from '../../../training/components/create-fitness-state-modal/create-fitness-state-modal.module';
import { FitnessStateModalModule } from '../../../training/components/fitness-state-modal/fitness-state-modal.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    TraineeDetailsPageRoutingModule,
    ReactiveFormsModule,
    CreateFitnessStateModalModule,
    FitnessStateModalModule,
  ],
  declarations: [TraineeDetailsPage],
})
export class TraineeDetailsPageModule {}
