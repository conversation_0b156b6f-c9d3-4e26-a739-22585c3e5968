import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { MyWorkoutsPageRoutingModule } from './my-workouts-routing.module';

import { MyWorkoutsPage } from './my-workouts.page';
import { CardComponentModule } from '../../../shared/components/card/card.module';
import { WorkoutTableModule } from '../../../training/components/workout-table/workout-table.module';
import { WorkoutCardModule } from '../../../training/components/workout-card/workout-card.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    MyWorkoutsPageRoutingModule,
    CardComponentModule,
    WorkoutTableModule,
    WorkoutCardModule,
  ],
  declarations: [MyWorkoutsPage],
})
export class MyWorkoutsPageModule {}
