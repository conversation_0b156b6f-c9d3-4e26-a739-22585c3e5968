<ion-header>
  <ion-toolbar color="dark">
    <ion-buttons slot="start">
      <ion-menu-button></ion-menu-button>
    </ion-buttons>
    <ion-title> My Workouts</ion-title>
  </ion-toolbar>
</ion-header>

<ion-fab #fab horizontal="end" slot="fixed" vertical="bottom">
  <ion-fab-button>
    <ion-icon name="add"></ion-icon>
  </ion-fab-button>
  <ion-fab-list side="top">
    <ion-fab-button (click)="handleCreateWorkout()" data-desc="Create Workout">
      <ion-icon name="person-add"></ion-icon>
    </ion-fab-button>
  </ion-fab-list>
</ion-fab>
<ion-content (click)="fab.close()">
  <ion-row>
    @for (workout of workouts; track workout) {
      <ion-col
        offset-lg="3"
        size="12"
        size-lg="6"
        >
        <mpg-workout-card
          (onWorkoutDelete)="handleWorkoutDelete()"
          [allExercises]="allExercises"
          [workout]="workout"
        ></mpg-workout-card>
      </ion-col>
    }
  </ion-row>
</ion-content>
