.btn {
  margin-top: 32px;
  margin-bottom: 32px;
}

.date {
  margin-top: 32px;
  color: var(--color-background-secondary-white-600);
  font-size: 20px;
  font-weight: 300;
  text-align: start;
}

.timeline {
  position: relative;
  width: 100%;
  margin: 16px auto 0;

  &.loading {
    height: 200px;
  }
}

.timeline::before {
  content: "";
  position: absolute;
  top: 0;
  left: 15px;
  width: 1px; /* Line thickness */
  height: calc(100% + 50px);
  background: repeating-linear-gradient(
      to bottom,
      var(--color-background-primary-black-400), /* Dash color */
      var(--color-background-primary-black-400) 12px, /* Dash length */
      transparent 12px, /* Space after dash */
      transparent 24px /* Total pattern size */
  );
  z-index: -1;
}

.event {
  position: relative;
  padding: 16px;
  width: calc(100% - 48px);
  margin-top: 16px;
  margin-left: 48px;

  border-radius: var(--radius-medium);
  background: var(--color-background-primary-black-500);
}

.event-time {
  position: absolute;
  left: -48px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-background-secondary-white-600);
  background-color: var(--color-background-primary-black-400);
  padding: 4px;
  font-size: var(--typefaces-size-xl);
  font-weight: 400;
  border-radius: var(--radius-small);
}

.event-header {

  ion-icon {
    color: var(--color-background-secondary-white-600);
    font-size: 24px;
    margin-right: 10px;
  }

  ion-label {
    color: var(--color-background-secondary-white-600);
    font-size: var(--typefaces-size-2xl, 16px);
    font-weight: 400;
  }


}
