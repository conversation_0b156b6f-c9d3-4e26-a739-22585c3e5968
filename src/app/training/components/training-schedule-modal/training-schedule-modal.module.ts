import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { TrainingScheduleModalComponent } from './training-schedule-modal.component';
import { ModalLayoutV2Module } from '../../../shared/components/modal-layout-v2/modal-layout-v2.module';
import { ButtonModule } from '../../../shared/components/button/button.module';
import { TranslateModule } from '@ngx-translate/core';
import { LoadingSpinnerComponentModule } from '../../../shared/components/loading-spinner/loading-spinner.module';
import { TrainingScheduleCalendarComponentModule } from '../training-schedule-calendar/training-schedule-calendar.module';
import { ChipComponentModule } from '../../../shared/components/chip/chip.module';
import { NutritionInfoTotalComponentModule } from '../../../nutrition/components/nutrition-info-total/nutrition-info-total.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ModalLayoutV2Module,
    ButtonModule,
    TranslateModule,
    LoadingSpinnerComponentModule,
    TrainingScheduleCalendarComponentModule,
    ChipComponentModule,
    NutritionInfoTotalComponentModule,
  ],
  declarations: [TrainingScheduleModalComponent],
  exports: [TrainingScheduleModalComponent],
})
export class TrainingScheduleModalModule {}
