<mpg-modal-layout-v2 icon="calendar-number" title="training.training-schedule">
  <div class="btn">
    <mpg-button (click)="handleScheduleClick()"
      >{{ "schedule" | translate }}
    </mpg-button>
  </div>
  <mpg-training-schedule-calendar
    #calendar
    (onDateSelect)="handleDateSelect($event)"
  ></mpg-training-schedule-calendar>

  <div class="date">
    {{ dateSubject$ | async | date: "d MMM, EEE" }}
  </div>
  <div [class.loading]="isLoading" class="timeline">
    <mpg-loading-spinner
      [fullScreen]="false"
      [isLoading]="isLoading"
    ></mpg-loading-spinner>
    @if (!isLoading) { @if (hasLabels) {
    <div class="event">
      <div class="event-header flex-start flex-wrap">
        @if (daySummary?.onCreatine) {
        <mpg-chip icon="pulse" label="creatine-tracker.creatine"></mpg-chip>
        } @if (daySummary?.bodyWeightInKg) {
        <mpg-chip
          iconSrc="/assets/icon/body-outline.svg"
          [label]="
            (daySummary.bodyWeightInKg | number: '1.0-2') +
            ' ' +
            ('weight.kg' | translate)
          "
        ></mpg-chip>
        } @if (daySummary?.sleepQuality) {
        <mpg-chip
          iconSrc="/assets/icon/sleep-tracking.svg"
          [label]="daySummary.sleepQuality + '%'"
        ></mpg-chip>
        } @if (daySummary?.hasBodyPhotos) {
        <mpg-chip icon="images" label="photos"></mpg-chip>
        } @if (daySummary?.hasBodyMeasurements) {
        <mpg-chip
          iconSrc="/assets/icon/meter.svg"
          label="measurements"
        ></mpg-chip>
        } @if (daySummary?.nutritionInfo) {
        <mpg-chip
          iconSrc="/assets/icon/progress.svg"
          [label]="daySummary.nutritionInfo.calories + ' kcal'"
        ></mpg-chip>
        }
      </div>
    </div>
    } @for (event of daySummary?.events; track event) {
    <div class="event">
      <div class="event-time">{{ event.time | slice: 0 : 5 }}</div>
      <div class="flex-space-between">
        <div
          class="event-header flex-start"
          *ngIf="getEventInfo(event) as eventInfo"
        >
          <ion-icon
            [src]="eventInfo.iconSrc"
            [name]="eventInfo.icon"
          ></ion-icon>
          <ion-label>{{ eventInfo.label | translate }}</ion-label>
        </div>
        <div class="event-metadata flex-grow flex-end">
          @if (event.type === DaySummaryEventType.MEAL) {
          <mpg-nutrition-info-total
            [showCalories]="false"
            [padding]="false"
            [nutritionInfo]="event.payload.nutritionInfo"
          ></mpg-nutrition-info-total>
          }
        </div>
      </div>
    </div>
    } }
  </div>
</mpg-modal-layout-v2>
