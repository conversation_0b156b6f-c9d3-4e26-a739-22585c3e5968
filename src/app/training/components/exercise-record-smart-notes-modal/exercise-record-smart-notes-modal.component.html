<mpg-modal-layout-v2
  [scrollY]="true"
  [showFooter]="false"
  [title]="'exercises.' + exerciseRecord.exercise.exercise.id | translate: { fallback: exerciseRecord.exercise.exercise.name }"
  subtitle="smart-notes.label"
>
  <div class="chat-container">
    <div class="set-selector flex-start">
      @for (setRecord of exerciseRecord.setRecords; track setRecord) {
        <div
          class="set flex-grow flex"
          (click)="handleSetRecordClick(setRecord)"
        >
          <ion-ripple-effect></ion-ripple-effect>
          <ion-label
            [class.selected]="setRecord === selectedSetRecord"
          > {{ setRecord.orderNumber }}
          </ion-label>
        </div>
      }
    </div>
  </div>

  <mpg-smart-notes-chat
    [allowFileUpload]="true"
    [allowVoiceRecording]="true"
    [createNote]="createSmartNote"
    [getMediaUploadUrl]="getMediaUploadUrl"
    [serviceBaseUrl]="trainingServiceUrl"
    [smartNotes]="smartNotes"
  ></mpg-smart-notes-chat>
</mpg-modal-layout-v2>
