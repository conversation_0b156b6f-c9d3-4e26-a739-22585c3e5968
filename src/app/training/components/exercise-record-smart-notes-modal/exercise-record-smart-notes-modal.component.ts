import { Component, ElementRef, Input, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { WorkoutExerciseRecord, WorkoutExerciseSetRecord } from '../../models';
import { SmartNote } from '../../../shared/models';
import { environment } from '../../../../environments/environment';

interface MessageGroup {
  creatorId: string;
  creatorName: string;
  timestamp: string;
  isCurrentUser: boolean;
  messages: SmartNote[];
}

@Component({
  selector: 'mpg-exercise-record-smart-notes-modal',
  templateUrl: './exercise-record-smart-notes-modal.component.html',
  styleUrls: ['./exercise-record-smart-notes-modal.component.scss']
})
export class ExerciseRecordSmartNotesModalComponent
  implements OnInit, OnDestroy {
  @Input() exerciseRecord: WorkoutExerciseRecord;
  @Input() selectedSetRecord: WorkoutExerciseSetRecord;
  @ViewChild('fileInput') fileInput: ElementRef<HTMLInputElement>;
  @ViewChild('messagesArea') messagesArea: ElementRef<HTMLDivElement>;

  message: string;
  smartNotes: SmartNote[] = [];
  isLoading = false;
  currentUserId: string;
  trainingServiceUrl = environment.TRAINING_SERVICE_API_URL;


}
