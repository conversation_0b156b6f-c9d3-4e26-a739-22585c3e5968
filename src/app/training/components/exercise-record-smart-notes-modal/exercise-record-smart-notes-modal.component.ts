import { Component, Input, OnInit } from '@angular/core';
import { WorkoutExerciseRecord, WorkoutExerciseSetRecord } from '../../models';
import {
  SmartNote,
  SmartNoteCreateRequest,
  StorageObject,
  StorageObjectUploadUrlRequest
} from '../../../shared/models';
import { environment } from '../../../../environments/environment';
import { Observable } from 'rxjs';
import { SmartNoteService } from '../../../shared/services';
import { WorkoutRecordService } from '../../services';
import { tap } from 'rxjs/operators';

interface MessageGroup {
  creatorId: string;
  creatorName: string;
  timestamp: string;
  isCurrentUser: boolean;
  messages: SmartNote[];
}

@Component({
  selector: 'mpg-exercise-record-smart-notes-modal',
  templateUrl: './exercise-record-smart-notes-modal.component.html',
  styleUrls: ['./exercise-record-smart-notes-modal.component.scss']
})
export class ExerciseRecordSmartNotesModalComponent implements OnInit {
  @Input() exerciseRecord: WorkoutExerciseRecord;
  @Input() selectedSetRecord: WorkoutExerciseSetRecord;

  smartNotes: SmartNote[] = [];
  trainingServiceUrl = environment.TRAINING_SERVICE_API_URL;

  constructor(
    private smartNoteService: SmartNoteService,
    private workoutRecordService: WorkoutRecordService
  ) {
  }

  ngOnInit() {
    this.loadSmartNotes();
  }

  handleSetRecordClick(setRecord: WorkoutExerciseSetRecord) {
    this.selectedSetRecord = setRecord;
    this.loadSmartNotes();
  }

  createSmartNote = (
    request: SmartNoteCreateRequest
  ): Observable<SmartNote> => {
    const workoutRecordId =
      this.selectedSetRecord.exerciseRecord.workoutRecord.id;
    const exerciseRecordId = this.selectedSetRecord.exerciseRecord.id;
    const setRecordId = this.selectedSetRecord.id;

    return this.workoutRecordService
      .createSetRecordSmartNote(
        workoutRecordId,
        exerciseRecordId,
        setRecordId,
        request
      )
      .pipe(
        tap((smartNote) => {
          this.smartNotes = [...this.smartNotes, smartNote];
          this.selectedSetRecord.smartNotes = [...this.smartNotes];
        })
      );
  };

  getMediaUploadUrl = (
    model: StorageObjectUploadUrlRequest
  ): Observable<StorageObject> => {
    return this.smartNoteService.getMediaUploadUrl(
      environment.TRAINING_SERVICE_API_URL
    )(model);
  };

  private loadSmartNotes(): void {
    if (!this.selectedSetRecord) {
      this.selectedSetRecord = this.exerciseRecord.setRecords[0];
    }

    // Use functional approach to ensure immutability
    this.smartNotes = this.selectedSetRecord.smartNotes
      ? [...this.selectedSetRecord.smartNotes]
      : [];
  }
}
