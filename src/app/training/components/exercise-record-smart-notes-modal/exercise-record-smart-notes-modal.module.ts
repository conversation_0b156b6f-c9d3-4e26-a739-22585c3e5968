import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { TranslateModule } from '@ngx-translate/core';

import { ExerciseRecordSmartNotesModalComponent } from './exercise-record-smart-notes-modal.component';
import { ModalLayoutV2Module } from '../../../shared/components/modal-layout-v2/modal-layout-v2.module';
import { VideoPlayerDirectiveModule } from '../../../shared/directives/video-player/video-player-directive.module';
import { SmartNotesChatModule } from '../../../shared/components/smart-notes-chat/smart-notes-chat.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    TranslateModule,
    ModalLayoutV2Module,
    VideoPlayerDirectiveModule,
    SmartNotesChatModule,
  ],
  declarations: [ExerciseRecordSmartNotesModalComponent],
  exports: [ExerciseRecordSmartNotesModalComponent],
})
export class ExerciseRecordSmartNotesModalModule {}
