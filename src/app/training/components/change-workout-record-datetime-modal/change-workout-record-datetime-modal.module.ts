import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { ModalLayoutModule } from '../../../shared/components/modal-layout/modal-layout.module';
import { ChangeWorkoutRecordDatetimeModalComponent } from './change-workout-record-datetime-modal.component';
import { TranslateModule } from '@ngx-translate/core';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ReactiveFormsModule,
    ModalLayoutModule,
    TranslateModule,
  ],
  declarations: [ChangeWorkoutRecordDatetimeModalComponent],
  exports: [ChangeWorkoutRecordDatetimeModalComponent],
})
export class ChangeWorkoutRecordDatetimeModalModule {}
