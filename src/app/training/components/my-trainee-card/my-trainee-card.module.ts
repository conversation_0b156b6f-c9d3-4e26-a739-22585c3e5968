import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MyTraineeCardComponent } from './my-trainee-card.component';
import { CardComponentModule } from '../../../shared/components/card/card.module';
import { WorkoutTableModule } from '../workout-table/workout-table.module';
import { WorkoutRecordTableModule } from '../workout-record-table/workout-record-table.module';
import { TranslateModule } from '@ngx-translate/core';
import { IonicModule } from '@ionic/angular';
import { DayOfWeekPipeModule } from '../../../shared/pipes/day-of-week/day-of-week-pipe.module';
import { TimeFormatPipeModule } from '../../../shared/pipes/time-format/time-format-pipe.module';
import { SleepQualityChipModule } from '../sleep-quality-chip/sleep-quality-chip.module';

@NgModule({
  declarations: [MyTraineeCardComponent],
  exports: [MyTraineeCardComponent],
  imports: [
    CommonModule,
    CardComponentModule,
    WorkoutTableModule,
    WorkoutRecordTableModule,
    TranslateModule,
    IonicModule,
    DayOfWeekPipeModule,
    TimeFormatPipeModule,
    SleepQualityChipModule,
  ],
})
export class MyTraineeCardModule {}
