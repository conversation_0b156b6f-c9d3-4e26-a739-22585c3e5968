@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.pr-message {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  position: absolute;
  background: linear-gradient(to left, var(--ion-color-dark), var(--ion-color-primary), var(--ion-color-primary-shade));
  z-index: 1000;
  animation: fadeIn 2s ease-in-out;
  padding: 2rem;
}

.hidden {
  display: none;
}
