import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { WorkoutRecordCardComponent } from './workout-record-card.component';
import { CardComponentModule } from '../../../shared/components/card/card.module';
import { WorkoutTableModule } from '../workout-table/workout-table.module';
import { WorkoutRecordTableModule } from '../workout-record-table/workout-record-table.module';
import { TranslateModule } from '@ngx-translate/core';
import { IonicModule } from '@ionic/angular';
import { WorkoutRecordNotesModule } from '../workout-record-notes/workout-record-notes.module';
import { SleepQualityChipModule } from '../sleep-quality-chip/sleep-quality-chip.module';
import { WorkoutRecordChipsModule } from '../workout-record-chips/workout-record-chips.module';

@NgModule({
  declarations: [WorkoutRecordCardComponent],
  exports: [WorkoutRecordCardComponent],
  imports: [
    CommonModule,
    CardComponentModule,
    WorkoutTableModule,
    WorkoutRecordTableModule,
    TranslateModule,
    IonicModule,
    WorkoutRecordNotesModule,
    SleepQualityChipModule,
    WorkoutRecordChipsModule,
  ],
})
export class WorkoutRecordCardModule {}
