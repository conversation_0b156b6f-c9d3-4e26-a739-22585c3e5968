import { Component, Input, OnInit } from '@angular/core';
import { ModalController } from '@ionic/angular';

import { Locale, LocalizationService } from '../../../shared/services';
import { BodyWeightRecord } from '../../models';
import { But<PERSON> } from '../../../shared/models';
import { Observable } from 'rxjs';

@Component({
  selector: 'mpg-weight-modal',
  templateUrl: './weight-modal.component.html',
  styleUrls: ['./weight-modal.component.scss'],
})
export class WeightModalComponent implements OnInit {
  weight: number;
  @Input() bodyWeightRecord: BodyWeightRecord;
  locale$: Observable<Locale>;
  modalButton: Button = {
    label: 'buttons.submit',
    handler: () => this.handleSubmit(),
    disabled: () => !this.weight,
  };

  constructor(
    private modalController: ModalController,
    private localizationService: LocalizationService,
  ) {}

  ngOnInit() {
    this.locale$ = this.localizationService.locale$;
    this.weight = this.bodyWeightRecord.bodyWeightInKg;
  }

  handleSubmit(): void {
    this.modalController.dismiss(this.weight);
  }
}
