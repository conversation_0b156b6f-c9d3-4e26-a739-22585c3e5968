import { NgModule } from '@angular/core';
import { WeightModalComponent } from './weight-modal.component';
import { IonicModule } from '@ionic/angular';
import { FormsModule } from '@angular/forms';
import { SelectAllDirectiveModule } from '../../../shared/directives/select-all/select-all-directive.module';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { ModalLayoutModule } from '../../../shared/components/modal-layout/modal-layout.module';

@NgModule({
  declarations: [WeightModalComponent],
  exports: [WeightModalComponent],
  imports: [
    IonicModule,
    FormsModule,
    SelectAllDirectiveModule,
    CommonModule,
    TranslateModule,
    ModalLayoutModule,
  ],
})
export class WeightModalModule {}
