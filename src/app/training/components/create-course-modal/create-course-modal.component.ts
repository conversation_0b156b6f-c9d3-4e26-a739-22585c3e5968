import { Component, OnInit } from '@angular/core';
import { CourseCreateRequest } from '../../models';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { CourseService } from '../../services';
import { Button } from '../../../shared/models';
import { ModalService } from '../../../shared/services';

@Component({
  selector: 'mpg-create-course-modal',
  templateUrl: './create-course-modal.component.html',
  styleUrls: ['./create-course-modal.component.scss'],
})
export class CreateCourseModalComponent implements OnInit {
  createCourseFormGroup: FormGroup;
  modalButton: Button = {
    label: 'Submit',
    handler: () => this.handleSubmit(),
    disabled: () => this.createCourseFormGroup?.invalid,
  };

  constructor(
    private fb: FormBuilder,
    private courseService: CourseService,
    private modalService: ModalService,
  ) {}

  ngOnInit() {
    this.createCourseFormGroup = this.fb.group({
      name: [null, [Validators.required]],
      url: [null, [Validators.required]],
    });
  }

  handleSubmit() {
    if (this.createCourseFormGroup.invalid) {
      return;
    }

    const formValue: CourseCreateRequest = this.createCourseFormGroup.value;

    this.courseService.create(formValue).subscribe((course) => {
      this.modalService.closeTopModal(course);
    });
  }
}
