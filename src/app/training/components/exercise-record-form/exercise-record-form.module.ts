import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { ExerciseRecordFormComponent } from './exercise-record-form.component';
import { ModalLayoutModule } from '../../../shared/components/modal-layout/modal-layout.module';
import { MaxLengthDirectiveModule } from '../../../shared/directives/max-length/max-length-directive.module';
import { TranslateModule } from '@ngx-translate/core';
import { LoadingCheckmarkModule } from '../../../shared/components/loading-checkmark/loading-checkmark.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ReactiveFormsModule,
    ModalLayoutModule,
    MaxLengthDirectiveModule,
    TranslateModule,
    LoadingCheckmarkModule,
  ],
  declarations: [ExerciseRecordFormComponent],
  exports: [ExerciseRecordFormComponent],
})
export class ExerciseRecordFormModule {}
