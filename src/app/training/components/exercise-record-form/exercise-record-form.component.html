<input
  #videoInput
  (change)="handleVideoSelect($event)"
  accept="video/*"
  class="invisible"
  type="file"
  />
@for (setRecord of exerciseRecord.setRecords; track setRecord; let i = $index) {
  <ion-item-sliding #itemSliding [disabled]="setRecordVideo || setRecord.video">
    <ion-item
      [formGroup]="setRecordsFormGroupMap[setRecord.id]"
      class="transparent no-end-padding"
      >
      <ion-col class="flex-centered" size="1">
        @if (setRecordsFormGroupMap[setRecord.id].invalid) {
          <ion-label
            >{{ i + 1 }}.
          </ion-label>
        }
        @if (setRecordsFormGroupMap[setRecord.id].valid) {
          <mpg-loading-checkmark
            [color]="color"
          [isLoading]="
            setRecordsLoadingMap && setRecordsLoadingMap[setRecord.id]
          "
          ></mpg-loading-checkmark>
        }
      </ion-col>
      <ion-col class="flex-centered" size="6">
        <ion-input
          aria-labelledby="asd"
          class="ion-text-center bolder small"
          formControlName="weightInKg"
          maxlength="3"
          mpgMaxLength
          type="number"
          >
          <ion-chip class="label" slot="label">
            <ion-label>{{ "training.weight" | translate }}</ion-label>
          </ion-chip>
        </ion-input>
      </ion-col>
      <ion-col class="flex-centered" size="5">
        <ion-input
          aria-labelledby="asd"
          class="ion-text-center bolder small"
          formControlName="reps"
          maxlength="3"
          mpgMaxLength
          type="number"
          >
          <ion-chip class="label" slot="label">
            <ion-label>{{ "training.reps" | translate }}</ion-label>
          </ion-chip>
        </ion-input>
      </ion-col>
    </ion-item>
    <ion-item-options>
      <ion-item-option
        (click)="
          videoInput.click();
          this.selectedSetRecord = setRecord;
          itemSliding.close()
        "
        >
        <ion-icon color="light" name="videocam" slot="icon-only"></ion-icon>
      </ion-item-option>
    </ion-item-options>
  </ion-item-sliding>
  @if (setRecord.video) {
    <ion-item
      (click)="handleSetRecordVideoClick(setRecord)"
      [button]="true"
      class="transparent w-full ion-text-center"
      >
      <ion-col class="flex-centered" size="1">
        <ion-icon color="dark" name="videocam"></ion-icon>
      </ion-col>
      <ion-col class="flex-centered" size="11">
        <ion-label>{{ "video" | translate }}</ion-label>
      </ion-col>
    </ion-item>
  }
  @if (setRecordVideo && selectedSetRecord?.id === setRecord.id) {
    <ion-item>
      @if (setRecordVideo.progress < 100) {
        <ion-progress-bar
          [value]="setRecordVideo.progress / 100"
        ></ion-progress-bar>
      }
    </ion-item>
  }
}
