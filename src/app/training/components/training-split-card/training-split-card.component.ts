import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { TrainingSplit } from '../../models';
import { Observable, switchMap } from 'rxjs';
import {
  Locale,
  LocalizationService,
  ToastService,
} from '../../../shared/services';
import { Button } from '../../../shared/models';
import { TraineeService, TrainingSplitService } from '../../services';

@Component({
  selector: 'mpg-training-split-card',
  templateUrl: './training-split-card.component.html',
  styleUrls: ['./training-split-card.component.scss'],
})
export class TrainingSplitCardComponent implements OnInit {
  @Input() trainingSplit: TrainingSplit;
  @Input() isSelected = false;
  @Output() onUpdate = new EventEmitter<void>();

  locale$: Observable<Locale>;
  options: Button[] = [
    {
      label: 'buttons.change-name',
      handler: () => {
        this.trainingSplitService
          .createChangeNameModal(this.trainingSplit)
          .subscribe((name) => {
            this.trainingSplit.name = name;
            this.onUpdate.emit();
          });
      },
      icon: 'pencil',
    },
    {
      label: 'training.set-current-split',
      handler: () => {
        this.trainingSplitService
          .edit(this.trainingSplit.id, {
            isActive: true,
          })
          .subscribe(() => {
            this.trainingSplit.isActive = true;
            this.onUpdate.emit();
            this.toastService.showInfoToast(
              'training.set-current-split-success',
            );
          });
      },
      iconSrc: '/assets/icon/full-pin.svg',
      disabled: () => {
        return this.trainingSplit.isActive;
      },
    },
    {
      label: 'buttons.archive',
      handler: () => {
        this.trainingSplitService
          .delete(this.trainingSplit.id)
          .subscribe(() => {
            this.onUpdate.emit();
          });
      },
      icon: 'file-tray-full',
      disabled: () => {
        return this.trainingSplit.isActive;
      },
    },
    {
      label: 'buttons.duplicate',
      handler: () => {
        this.traineeService.traineeId$
          .pipe(
            switchMap((traineeId) => {
              return this.trainingSplitService.copy(this.trainingSplit.id, {
                traineeId,
                isActive: false,
              });
            }),
          )
          .subscribe((t: TrainingSplit) => {
            this.onUpdate.emit();
          });
      },
      iconSrc: '/assets/icon/duplicate.svg',
    },
  ];

  constructor(
    private localizationService: LocalizationService,
    private trainingSplitService: TrainingSplitService,
    private traineeService: TraineeService,
    private toastService: ToastService,
  ) {
    this.locale$ = this.localizationService.locale$;
  }

  ngOnInit() {}
}
