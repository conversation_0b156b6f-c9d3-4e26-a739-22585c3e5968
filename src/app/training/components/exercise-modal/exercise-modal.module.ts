import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { ExerciseModalComponent } from './exercise-modal.component';
import { ModalLayoutModule } from '../../../shared/components/modal-layout/modal-layout.module';
import { TranslateModule } from '@ngx-translate/core';
import { NgApexchartsModule } from 'ng-apexcharts';
import { VideoPlayerDirectiveModule } from '../../../shared/directives/video-player/video-player-directive.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ModalLayoutModule,
    TranslateModule,
    NgApexchartsModule,
    VideoPlayerDirectiveModule,
  ],
  declarations: [ExerciseModalComponent],
  exports: [ExerciseModalComponent],
})
export class ExerciseModalModule {}
