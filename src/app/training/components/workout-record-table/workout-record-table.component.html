@if (!workoutRecord.exerciseRecords?.length) {
  <div>
    <ion-text class="ion-margin-bottom ion-padding" color="dark"
      ><h2>No exercises in record.</h2></ion-text
      >
    </div>
  }
  @if (workoutRecord.exerciseRecords?.length) {
    <div>
      <ion-row class="border-bottom">
        <ion-col class="ion-padding" size="2">
          <ion-text color="dark"><h2>№</h2></ion-text>
        </ion-col>
        <ion-col class="ion-padding" size="4">
          <ion-text color="dark"
            ><h2>{{ "training.exercise" | translate }}</h2></ion-text
            >
          </ion-col>
          <ion-col class="ion-padding" size="6">
            <ion-text color="dark"
              ><h2>{{ "training.sets" | translate }}</h2></ion-text
              >
            </ion-col>
          </ion-row>
          @for (
            exerciseRecord of workoutRecord.exerciseRecords; track
            exerciseRecord; let last = $last; let index = $index) {
            <ion-row
      [ngClass]="{
        'border-bottom':
          !last &&
          !lastInGroup(exerciseRecord, index) &&
          !hasNotes(exerciseRecord) &&
          !hasVideos(exerciseRecord)
      }"
              >
              <ion-item-sliding #itemSliding>
                <ion-item
          [lines]="
            hasNotes(exerciseRecord) || hasVideos(exerciseRecord)
              ? 'none'
              : undefined
          "
                  class="ion-no-padding ion-text-center"
                  >
                  <ion-col size="2">
                    <ion-text color="dark"
                      ><h2>
                      {{ getOrderNumber(exerciseRecord.exercise) }}
                    </h2>
                  </ion-text>
                </ion-col>
                <ion-col size="4">
                  <ion-text color="dark"
                    ><h2>
                    {{
                    "exercises." + exerciseRecord.exercise.exercise.id
                    | translate
                    : { fallback: exerciseRecord.exercise.exercise.name }
                    }}
                  </h2>
                </ion-text>
              </ion-col>
              <ion-col size="6">
                @if (
                  !exerciseRecord.isSkipped &&
                  exerciseRecord.personalRecordsCount === 0
                  ) {
                  <ion-text
                    color="dark"
                    ><h2>{{ exerciseRecord.setRecordsAsText }}</h2></ion-text
                    >
                  }
                  @if (exerciseRecord.isSkipped) {
                    <ion-chip class="dark"
                      >{{ "skipped" | translate }}
                    </ion-chip>
                  }
                  @if (exerciseRecord.personalRecordsCount > 0) {
                    <ion-chip
                      (click)="handlePersonalRecordsClick(exerciseRecord)"
                      class="dark overflow-auto pr-container"
                      >{{ exerciseRecord.setRecordsAsText }}
                      <ion-chip class="pr gold"> PR </ion-chip>
                    </ion-chip>
                  }
                </ion-col>
              </ion-item>
              <ion-item-options>
                <ion-item-option
            (click)="
              handleExerciseRecordPopoverMenu(
                $event,
                itemSliding,
                exerciseRecord,
                workoutRecord
              )
            "
                  class="transparent"
                  >
                  <ion-icon
                    color="dark"
                    name="ellipsis-vertical"
                    slot="icon-only"
                  ></ion-icon>
                </ion-item-option>
              </ion-item-options>
            </ion-item-sliding>
          </ion-row>
          @if (hasVideos(exerciseRecord)) {
            <ion-row
      [ngClass]="{
        'border-bottom':
          !last &&
          !lastInGroup(exerciseRecord, index) &&
          !hasNotes(exerciseRecord)
      }"
              >
              <ion-item
                class="w-full ion-no-padding ion-text-center small"
                lines="none"
                >
                <ion-col class="flex-centered ion-no-padding" size="12">
                  @for (setRecord of getVideoSetRecords(exerciseRecord); track setRecord) {
                    <ion-chip
                      (click)="handleVideoClick(setRecord)"
                      class="dark"
                      >
                      <ion-icon color="light" name="videocam"></ion-icon>
                      <ion-label
                        >{{ "training.set" | translate }}
                        {{ setRecord.orderNumber }}
                      </ion-label>
                    </ion-chip>
                  }
                </ion-col>
              </ion-item>
            </ion-row>
          }
          @if (hasNotes(exerciseRecord)) {
            <ion-row
      [ngClass]="{
        'border-bottom': !last && !lastInGroup(exerciseRecord, index)
      }"
              >
              <ion-item
                (click)="handleNotesClick(exerciseRecord)"
                [button]="true"
                [ngClass]="{ small: hasVideos(exerciseRecord) }"
                class="w-full ion-no-padding ion-text-center"
                >
                <ion-col size="2">
                  <ion-icon color="dark" name="document-text"></ion-icon>
                </ion-col>
                <ion-col class="flex-column-space-around" size="10">
                  @for (note of exerciseRecord.notes; track note) {
                    <ion-text
                      >{{ note.content }}
                    </ion-text>
                  }
                </ion-col>
              </ion-item>
            </ion-row>
          }
        }
      </div>
    }
