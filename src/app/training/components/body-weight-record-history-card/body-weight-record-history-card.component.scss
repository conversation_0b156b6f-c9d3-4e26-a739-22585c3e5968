.container {
  color: var(--color-background-secondary-white-600);
  margin-top: 20px;
  margin-bottom: 20px;

  .header {
    margin-bottom: 24px;
  }

  .stat {
    font-size: var(--typefaces-size-xl);
  }

  hr {
    height: 1px;
    background-color: var(--color-background-primary-black-200);
    border-radius: var(--radius-medium-small);
    border: 0;
    width: 95%;
    margin-top: 12px;
    margin-bottom: 12px;
  }

  .record-container {
    flex-grow: 1;

    &:not(:last-of-type) {
      margin-right: 5px;
    }
  }

  .day {
    margin-bottom: 10px;
    font-size: var(--typefaces-size-xl);
  }

  .date {
    margin-top: 8px;
    margin-bottom: 10px;
    font-size: var(--typefaces-size-xl);
  }

  .weight {
    color: var(--color-background-primary-black-200);
    background: var(--color-background-primary-black-500);
    border-radius: var(--radius-medium-small);
    cursor: pointer;
    font-size: var(--typefaces-size-xl);
    width: 100%;
    padding-top: 5px;
    padding-bottom: 5px;


    &:hover {
      background: var(--color-background-primary-black-400);
    }
  }
}
