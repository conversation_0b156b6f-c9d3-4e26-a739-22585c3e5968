import { Component, EventEmitter, Input, Output } from '@angular/core';
import { BodyWeightRecord, WeeklyBodyWeightRecord } from '../../models';

@Component({
  selector: 'mpg-body-weight-record-history-card',
  templateUrl: './body-weight-record-history-card.component.html',
  styleUrls: ['./body-weight-record-history-card.component.scss'],
})
export class BodyWeightRecordHistoryCardComponent {
  @Input() weeklyBodyWeightRecord: WeeklyBodyWeightRecord;
  @Input() highlightedDate: string;
  @Output() onRecordClick = new EventEmitter<RecordClickEvent>();

  constructor() {}

  handleRecordClick(record: BodyWeightRecord) {
    this.onRecordClick.emit({
      record,
      index: this.weeklyBodyWeightRecord.records.indexOf(record),
    });
  }
}

export type RecordClickEvent = {
  record: BodyWeightRecord;
  index: number;
};
