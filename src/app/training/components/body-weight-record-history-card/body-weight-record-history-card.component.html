<div class="container">
  <div class="header flex-space-between">
    <div class="stat-container">
      <ion-label class="stat">
        {{ "avg" | translate }}
        {{
        weeklyBodyWeightRecord?.averageBodyWeight
        ? weeklyBodyWeightRecord.averageBodyWeight +
        " " +
        ("weight.kg" | translate)
        : "N/A"
        }}
      </ion-label>
    </div>
    <div class="stat-container">
      <ion-label class="stat">
        @if (
          weeklyBodyWeightRecord.percentageDiff &&
          weeklyBodyWeightRecord.percentageDiff !== 0
          ) {
          <ion-icon
          name="arrow-{{
            weeklyBodyWeightRecord?.percentageDiff > 0 ? 'up' : 'down'
          }}-outline"
          ></ion-icon>
        }
        {{ "diff" | translate }}
        {{
        weeklyBodyWeightRecord?.percentageDiff ||
        weeklyBodyWeightRecord.percentageDiff === 0
        ? weeklyBodyWeightRecord.percentageDiff + " %"
        : "N/A"
        }}
      </ion-label>
    </div>
  </div>
  <div class="days-container flex-space-between">
    @for (record of weeklyBodyWeightRecord?.records; track record) {
      <div
        class="record-container flex-column-space-around"
        >
        <ion-label class="day"
          >{{ "days." + record?.dayOfWeek | translate | slice: 0 : 1 }}
        </ion-label>
        <ion-label class="date">{{ record?.date | date: "dd/MM" }}</ion-label>
        <div
          (click)="handleRecordClick(record)"
          [ngClass]="{ empty: !record.bodyWeightInKg }"
          class="weight flex-centered ion-activatable overflow-hidden"
          >
          <ion-ripple-effect></ion-ripple-effect>
          <ion-label>{{ record.bodyWeightInKg || "-" }}</ion-label>
        </div>
      </div>
    }
  </div>
</div>
