ion-label {
  color: var(--color-background-secondary-white-600);
  font-size: 24px;
  font-weight: 350;
  text-align: center;

  &.muscle {
    font-size: 20px;
    font-weight: 300;
    color: var(--color-background-secondary-white-900);
    border-radius: var(--radius-medium);
    border: 1px solid var(--color-background-primary-black-400);
    padding: 2px 12px;
    flex-grow: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.horizontal-separator {
  width: 100%;
  height: 1px;
  background-color: var(--color-background-primary-black-400);
  margin: 4px 0;
}

