<mpg-modal-layout-v2 [button]="button" icon="filter" title="filters">
  @if (showDeselect) {
  <div
    (click)="handleDeselect()"
    class="deselect flex-centered ion-activatable overflow-hidden"
    header-actions
  >
    <ion-ripple-effect></ion-ripple-effect>
    {{ "buttons.deselect-all" | translate }}
  </div>
  } @for (section of sections; track section) {
  <div class="flex-start">
    <ion-label class="section">{{ section.title | translate }}</ion-label>
  </div>
  @for (muscleGroup of section.muscleGroups; track muscleGroup) {
  <div
    class="muscle-group"
    [class.selected]="filter[muscleGroup]"
    (click)="handleFilter(muscleGroup)"
  >
    <ion-icon
      [src]="'/assets/icon/muscle-groups/' + muscleGroup + '.svg'"
    ></ion-icon>
    <ion-label>{{ "muscle-groups." + muscleGroup | translate }}</ion-label>
  </div>
  } }
  <div class="empty"></div>
</mpg-modal-layout-v2>
