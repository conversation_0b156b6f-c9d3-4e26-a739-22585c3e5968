import { Component, Input, OnInit } from '@angular/core';
import {
  AlertService,
  DateService,
  Locale,
  LocalizationService,
  ModalService,
  PopoverService,
} from '../../../shared/services';
import { Observable, of, switchMap } from 'rxjs';
import { FormBuilder, FormControl, Validators } from '@angular/forms';
import { FileService } from '../../../shared/services/file.service';
import { BodyPhotoType } from '../../enumerations';
import { BodyPhotoService } from '../../services';
import { environment } from '../../../../environments/environment';
import { BodyPhoto } from '../../models';
import { StorageObjectInProgress } from '../../../shared/models';

@Component({
  selector: 'mpg-body-photos-modal',
  templateUrl: './body-photos-modal.component.html',
  styleUrls: ['./body-photos-modal.component.scss'],
})
export class BodyPhotosModalComponent implements OnInit {
  @Input() date: string;
  @Input() traineeId: string;
  @Input() onChange: (date: string, photos: BodyPhoto[]) => void;

  locale$: Observable<Locale>;
  dateFormControl: FormControl<string>;
  selectedPhotoType: BodyPhotoType;
  photosByTypeMap: Record<BodyPhotoType, BodyPhoto[]> = {
    FRONT: [],
    BACK: [],
    SIDEWAYS: [],
  };
  storageObject: StorageObjectInProgress;
  photoTypes: string[];

  constructor(
    private localizationService: LocalizationService,
    private fb: FormBuilder,
    private dateService: DateService,
    private fileService: FileService,
    private popoverService: PopoverService,
    private bodyPhotoService: BodyPhotoService,
    private alertService: AlertService,
    private modalService: ModalService,
  ) {}

  ngOnInit(): void {
    this.photoTypes = Object.keys(BodyPhotoType);
    this.locale$ = this.localizationService.locale$;
    this.dateFormControl = this.fb.nonNullable.control(
      this.date || this.dateService.getCurrentDate(),
      Validators.required,
    );
    this.dateFormControl.valueChanges.subscribe(() => {
      this.fetchPhotos();
    });

    this.fetchPhotos();
  }

  handleImageSelect(event: any) {
    this.fileService
      .uploadFile(
        event,
        this.bodyPhotoService.getPhotoUploadUrl({
          type: this.selectedPhotoType,
        }),
        environment.TRAINING_SERVICE_API_URL,
      )
      .pipe(
        switchMap((storageObject) => {
          if (storageObject.progress < 100) {
            return of(storageObject);
          }

          return this.bodyPhotoService.create({
            date: this.dateFormControl.value,
            photoId: storageObject.id,
            type: this.selectedPhotoType,
          });
        }),
      )
      .subscribe((res) => {
        if ('progress' in res) {
          this.storageObject = res;
          return;
        }

        this.photosByTypeMap[res.type] =
          this.photosByTypeMap[res.type].concat(res);
        this.storageObject = undefined;
        this.emitModalChange();
      });
  }

  handleDelete(photo: BodyPhoto) {
    this.alertService.createDeleteAlert(() => {
      this.bodyPhotoService.delete(photo.id).subscribe(() => {
        this.photosByTypeMap[photo.type] = this.photosByTypeMap[
          photo.type
        ].filter((p) => p.id !== photo.id);
        this.emitModalChange();
      });
    });
  }

  selectType(type: string) {
    this.selectedPhotoType = BodyPhotoType[type];
  }

  handleOpenPhoto(url: string) {
    this.modalService.showPhoto(url);
  }

  private fetchPhotos() {
    this.bodyPhotoService
      .getAll(this.dateFormControl.value, this.traineeId)
      .subscribe((bodyPhotos) => {
        this.photosByTypeMap = {
          FRONT: bodyPhotos.filter((b) => b.type === BodyPhotoType.FRONT),
          BACK: bodyPhotos.filter((b) => b.type === BodyPhotoType.BACK),
          SIDEWAYS: bodyPhotos.filter((b) => b.type === BodyPhotoType.SIDEWAYS),
        };
      });
  }

  private emitModalChange() {
    if (!this.onChange) {
      return;
    }

    this.onChange(this.dateFormControl.value, [
      ...this.photosByTypeMap.FRONT,
      ...this.photosByTypeMap.BACK,
      ...this.photosByTypeMap.SIDEWAYS,
    ]);
  }
}
