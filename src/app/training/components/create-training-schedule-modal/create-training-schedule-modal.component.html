<mpg-modal-layout
  [button]="modalButton"
  title="training.create-training-schedule"
  >
  <form [formGroup]="createTrainingScheduleFormGroup">
    <ion-item>
      <ion-select [label]="'forms.type' | translate" formControlName="type">
        <ion-select-option [value]="TrainingScheduleType.PATTERN">{{
          "training.training-schedule-type.PATTERN" | translate
        }}</ion-select-option>
        <ion-select-option [value]="TrainingScheduleType.DAYS_OF_THE_WEEK">{{
          "training.training-schedule-type.DAYS_OF_THE_WEEK" | translate
        }}</ion-select-option>
      </ion-select>
    </ion-item>
    @if (daysOfTheWeekScheduleFormGroup.enabled) {
      <form
        [formGroup]="daysOfTheWeekScheduleFormGroup"
        >
        <ion-item>
          <ion-toggle formControlName="monday">{{
            "days.MONDAY" | translate
          }}</ion-toggle>
        </ion-item>
        <ion-item>
          <ion-toggle formControlName="tuesday">{{
            "days.TUESDAY" | translate
          }}</ion-toggle>
        </ion-item>
        <ion-item>
          <ion-toggle formControlName="wednesday">{{
            "days.WEDNESDAY" | translate
          }}</ion-toggle>
        </ion-item>
        <ion-item>
          <ion-toggle formControlName="thursday">{{
            "days.THURSDAY" | translate
          }}</ion-toggle>
        </ion-item>
        <ion-item>
          <ion-toggle formControlName="friday">{{
            "days.FRIDAY" | translate
          }}</ion-toggle>
        </ion-item>
        <ion-item>
          <ion-toggle formControlName="saturday">{{
            "days.SATURDAY" | translate
          }}</ion-toggle>
        </ion-item>
        <ion-item>
          <ion-toggle formControlName="sunday">{{
            "days.SUNDAY" | translate
          }}</ion-toggle>
        </ion-item>
      </form>
    }
    @if (daysOfTheWeekScheduleFormGroup.disabled) {
      <div>
        @for (
          char of getPatternChars(); track
          char; let index = $index; let first = $first) {
          <ion-item-sliding
            #itemSliding
            [disabled]="first"
            >
            <ion-item>
              <ion-label class="ion-text-center"
                >{{ index + 1 }}.
                {{
                char === "1"
                ? ("training.training-day" | translate)
                : ("training.rest-day" | translate)
                }}</ion-label
                >
              </ion-item>
              <ion-item-options>
                <ion-item-option
                  (click)="handleDeletePatternChar(index); itemSliding.close()"
                  color="primary"
                  >
                  <ion-icon name="trash-outline" slot="icon-only"></ion-icon>
                </ion-item-option>
              </ion-item-options>
            </ion-item-sliding>
          }
          @if (getPatternChars().length > 0) {
            <ion-item>
              <ion-label class="ion-text-center flex-centered">
                <ion-icon class="repeat" name="repeat"></ion-icon>
                <ion-text>{{ "training.repeat" | translate }}</ion-text>
              </ion-label>
            </ion-item>
          }
          <div class="modal-buttons-container ion-justify-content-center">
            <ion-fab-button
              (click)="handleAddPatternDay($event)"
              class="ion-margin-end"
              >
              <ion-icon name="add"></ion-icon>
            </ion-fab-button>
            <ion-fab-button
              (click)="handlePatternTooltip($event)"
              color="secondary"
              >
              <ion-icon name="information-circle-outline"></ion-icon>
            </ion-fab-button>
          </div>
        </div>
      }
    </form>
  </mpg-modal-layout>
