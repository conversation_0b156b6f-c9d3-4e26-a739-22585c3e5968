import { Component, Input, OnInit } from '@angular/core';
import { Button } from '../../../shared/models';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TrainingSchedule, TrainingScheduleRequest } from '../../models';
import { TrainingScheduleType } from '../../enumerations/training-schedule-type.enum';
import { TraineeService, TrainingScheduleService } from '../../services';
import { ModalService } from '../../../shared/services/modal.service';
import { MPGValidators } from '../../../shared/validators/validators';
import { PopoverService } from '../../../shared/services';

@Component({
  selector: 'mpg-create-training-schedule-modal',
  templateUrl: './create-training-schedule-modal.component.html',
  styleUrls: ['./create-training-schedule-modal.component.scss'],
})
export class CreateTrainingScheduleModalComponent implements OnInit {
  @Input() trainingSchedule?: TrainingSchedule;

  TrainingScheduleType = TrainingScheduleType;
  patternScheduleFormGroup: FormGroup;
  daysOfTheWeekScheduleFormGroup: FormGroup;
  createTrainingScheduleFormGroup: FormGroup;
  modalButton: Button = {
    label: 'buttons.submit',
    handler: () => this.handleSubmit(),
    disabled: () => this.createTrainingScheduleFormGroup.invalid,
  };

  constructor(
    private fb: FormBuilder,
    private trainingScheduleService: TrainingScheduleService,
    private traineeService: TraineeService,
    private modalService: ModalService,
    private popoverService: PopoverService
  ) {}

  ngOnInit() {
    this.patternScheduleFormGroup = this.fb.group({
      pattern: [
        this.trainingSchedule?.patternSchedule?.pattern || '1',
        [Validators.required, Validators.pattern(/^(1|([01]*0))$/)],
      ],
    });

    this.daysOfTheWeekScheduleFormGroup = this.fb.group(
      {
        monday: [
          this.trainingSchedule?.daysOfTheWeekSchedule?.monday || false,
          Validators.required,
        ],
        tuesday: [
          this.trainingSchedule?.daysOfTheWeekSchedule?.tuesday || false,
          Validators.required,
        ],
        wednesday: [
          this.trainingSchedule?.daysOfTheWeekSchedule?.wednesday || false,
          Validators.required,
        ],
        thursday: [
          this.trainingSchedule?.daysOfTheWeekSchedule?.thursday || false,
          Validators.required,
        ],
        friday: [
          this.trainingSchedule?.daysOfTheWeekSchedule?.friday || false,
          Validators.required,
        ],
        saturday: [
          this.trainingSchedule?.daysOfTheWeekSchedule?.saturday || false,
          Validators.required,
        ],
        sunday: [
          this.trainingSchedule?.daysOfTheWeekSchedule?.sunday || false,
          Validators.required,
        ],
      },
      { validators: MPGValidators.atLeastOneTrue }
    );

    this.createTrainingScheduleFormGroup = this.fb.group({
      type: [
        this.trainingSchedule?.type || TrainingScheduleType.PATTERN,
        Validators.required,
      ],
      patternSchedule: this.patternScheduleFormGroup,
      daysOfTheWeekSchedule: this.daysOfTheWeekScheduleFormGroup,
    });

    this.enableTrainingScheduleTypeInForm(
      this.createTrainingScheduleFormGroup.value.type
    );

    this.createTrainingScheduleFormGroup.controls.type.valueChanges.subscribe(
      (type: TrainingScheduleType) => {
        this.enableTrainingScheduleTypeInForm(type);
      }
    );
  }

  handleSubmit() {
    if (this.createTrainingScheduleFormGroup.invalid) {
      return;
    }

    const formValue: TrainingScheduleRequest =
      this.createTrainingScheduleFormGroup.value;

    this.trainingScheduleService
      .create(formValue)
      .subscribe((trainingSchedule) => {
        this.modalService.closeTopModal(trainingSchedule);
      });
  }

  handleAddPatternDay(event: MouseEvent) {
    this.popoverService.handlePopoverMenu(event, [
      {
        label: 'training.training-day',
        handler: () => {
          this.patternScheduleFormGroup.controls.pattern.setValue(
            this.patternScheduleFormGroup.value.pattern + '1'
          );
        },
      },
      {
        label: 'training.rest-day',
        handler: () => {
          this.patternScheduleFormGroup.controls.pattern.setValue(
            this.patternScheduleFormGroup.value.pattern + '0'
          );
        },
        disabled: () => {
          return this.patternScheduleFormGroup.value.pattern === '';
        },
      },
    ]);
  }

  getPatternChars(): string[] {
    return this.patternScheduleFormGroup.value.pattern.split('');
  }

  handlePatternTooltip(event: MouseEvent) {
    this.popoverService.showTooltip(event, 'training.pattern-tooltip');
  }

  handleDeletePatternChar(index: number) {
    const currentPattern = this.patternScheduleFormGroup.value.pattern;
    this.patternScheduleFormGroup.controls.pattern.setValue(
      currentPattern.substring(0, index) + currentPattern.substring(index + 1)
    );
  }

  private enableTrainingScheduleTypeInForm(type: TrainingScheduleType) {
    if (type === TrainingScheduleType.PATTERN) {
      this.createTrainingScheduleFormGroup.controls.patternSchedule.enable();
      this.createTrainingScheduleFormGroup.controls.daysOfTheWeekSchedule.disable();
    } else {
      this.createTrainingScheduleFormGroup.controls.daysOfTheWeekSchedule.enable();
      this.createTrainingScheduleFormGroup.controls.patternSchedule.disable();
    }
  }
}
