import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { TraineeInvitationModalComponent } from './trainee-invitation-modal.component';
import { ModalLayoutModule } from '../../../shared/components/modal-layout/modal-layout.module';
import { WorkoutTableModule } from '../workout-table/workout-table.module';
import { CardComponentModule } from '../../../shared/components/card/card.module';
import { TranslateModule } from '@ngx-translate/core';
import { TrainingVolumeTableModule } from '../training-volume-table/training-volume-table.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ModalLayoutModule,
    WorkoutTableModule,
    CardComponentModule,
    TranslateModule,
    TrainingVolumeTableModule,
  ],
  declarations: [TraineeInvitationModalComponent],
  exports: [TraineeInvitationModalComponent],
})
export class TraineeInvitationModalModule {}
