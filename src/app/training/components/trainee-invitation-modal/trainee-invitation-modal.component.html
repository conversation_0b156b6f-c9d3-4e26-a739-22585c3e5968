<mpg-modal-layout
  [iconButtons]="modalButtons"
  [title]="'training.trainee-info' | translate"
>
  <ion-card-content class="light-background ion-no-padding">
    <ion-item>
      <ion-label>Instagram</ion-label>
      <ion-label slot="end">{{ invitation.instagram }}</ion-label>
    </ion-item>
    <ion-item>
      <ion-label>Email</ion-label>
      <ion-label slot="end">{{ invitation.email }}</ion-label>
    </ion-item>
    <ion-item>
      <ion-label>First Name</ion-label>
      <ion-label slot="end">{{ invitation.firstName }}</ion-label>
    </ion-item>
    <ion-item>
      <ion-label>Last Name</ion-label>
      <ion-label slot="end">{{ invitation.lastName }}</ion-label>
    </ion-item>
    <ion-item>
      <ion-label>Gender</ion-label>
      <ion-label slot="end">{{ invitation.gender }}</ion-label>
    </ion-item>
    <ion-item>
      <ion-label>Height</ion-label>
      <ion-label slot="end">{{ invitation.heightInCm }}</ion-label>
    </ion-item>
    <ion-item>
      <ion-label>Birthdate</ion-label>
      <ion-label slot="end">{{ invitation.birthdate }}</ion-label>
    </ion-item>
    <ion-item>
      <ion-label>Occupation</ion-label>
      <ion-label slot="end">{{ invitation.occupation }}</ion-label>
    </ion-item>
    <ion-item>
      <ion-label>Created On</ion-label>
      <ion-label slot="end"
        >{{ invitation.createdOn | date: "medium" }}
      </ion-label>
    </ion-item>
  </ion-card-content>
</mpg-modal-layout>
