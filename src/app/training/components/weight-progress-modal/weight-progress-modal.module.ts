import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { WeightProgressModalComponent } from './weight-progress-modal.component';
import { ModalLayoutModule } from '../../../shared/components/modal-layout/modal-layout.module';
import { ModalLayoutV2Module } from '../../../shared/components/modal-layout-v2/modal-layout-v2.module';
import { ChartModule } from '../../../shared/components/chart/chart.module';
import { PeriodSelectorModule } from '../../../shared/components/period-selector/period-selector.module';
import { BodyWeightRecordHistoryCardComponentModule } from '../body-weight-record-history-card/body-weight-record-history-card.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ModalLayoutModule,
    ModalLayoutV2Module,
    ChartModule,
    PeriodSelectorModule,
    BodyWeightRecordHistoryCardComponentModule,
  ],
  declarations: [WeightProgressModalComponent],
  exports: [WeightProgressModalComponent],
})
export class WeightProgressModalModule {}
