import { Component, OnInit } from '@angular/core';
import { BodyWeightService } from '../../services';
import { DateService } from '../../../shared/services';
import { StatsPeriod } from '../../../shared/enumerations';
import { ChartSeries, Stats } from '../../../shared/models';
import { WeeklyBodyWeightRecord } from '../../models';
import { ApexOptions } from 'ng-apexcharts';
import { BehaviorSubject, switchMap } from 'rxjs';
import { tap } from 'rxjs/operators';
import { RecordEditEvent } from '../body-weight-record-history-table/body-weight-record-history-table.component';

@Component({
  selector: 'mpg-weight-progress-modal',
  templateUrl: './weight-progress-modal.component.html',
  styleUrls: ['./weight-progress-modal.component.scss'],
})
export class WeightProgressModalComponent implements OnInit {
  stats: Stats<WeeklyBodyWeightRecord>;
  chartOptions: ApexOptions = {
    yaxis: {
      labels: {
        show: false,
      },
    },
  };
  isLoading = false;
  endDateSubject$: BehaviorSubject<string>;

  constructor(
    private bodyWeightService: BodyWeightService,
    private dateService: DateService,
  ) {}

  ngOnInit(): void {
    this.endDateSubject$ = new BehaviorSubject<string>(
      this.dateService.getCurrentDate(),
    );

    this.endDateSubject$
      .pipe(
        tap(() => {
          this.isLoading = true;
        }),
        switchMap((endDate) => {
          return this.bodyWeightService.getHistoryStats({
            period: StatsPeriod.MONTH,
            endDate,
          });
        }),
      )

      .subscribe((stats) => {
        this.stats = stats;
        this.buildChartSeries();
        this.isLoading = false;
      });
  }

  handlePrevEndDate() {
    if (this.isLoading) {
      return;
    }

    this.endDateSubject$.next(this.stats.prevEndDate);
  }

  handleNextEndDate() {
    if (this.isLoading) {
      return;
    }

    this.endDateSubject$.next(this.stats.nextEndDate);
  }

  handleRecordClick(
    { record, index }: RecordEditEvent,
    weeklyRecord: WeeklyBodyWeightRecord,
    weeklyRecordIndex: number,
  ) {
    this.bodyWeightService
      .getBodyWeightRecordInput(record)
      .pipe(
        switchMap((weight) => {
          return this.bodyWeightService.putBodyWeightRecord(
            { bodyWeightInKg: weight },
            record.date,
          );
        }),
        tap((updatedRecord) => {
          weeklyRecord.records[index].bodyWeightInKg =
            updatedRecord.bodyWeightInKg;

          this.updateWeeklyRecordAverage(weeklyRecord);

          if (weeklyRecordIndex > 0) {
            this.updateWeeklyRecordPercentageDiff(
              this.stats.data[weeklyRecordIndex - 1],
              weeklyRecord,
            );
          }

          if (weeklyRecordIndex !== this.stats.data.length - 1) {
            this.updateWeeklyRecordPercentageDiff(
              weeklyRecord,
              this.stats.data[weeklyRecordIndex + 1],
            );
          }
        }),
      )
      .subscribe();
  }

  private updateWeeklyRecordAverage(weeklyRecord: WeeklyBodyWeightRecord) {
    const bodyWeightRecordsArr = weeklyRecord.records
      .map((r) => r.bodyWeightInKg)
      .filter((r) => !!r);

    const average =
      bodyWeightRecordsArr.reduce((prev, curr) => prev + curr) /
      bodyWeightRecordsArr.length;

    weeklyRecord.averageBodyWeight = +average.toFixed(2);
  }

  private updateWeeklyRecordPercentageDiff(
    newerWeeklyRecord: WeeklyBodyWeightRecord,
    prevWeeklyRecord: WeeklyBodyWeightRecord,
  ) {
    if (!newerWeeklyRecord || !newerWeeklyRecord.averageBodyWeight) {
      return;
    }

    if (!prevWeeklyRecord || !prevWeeklyRecord.averageBodyWeight) {
      return;
    }

    newerWeeklyRecord.percentageDiff = +(
      ((newerWeeklyRecord.averageBodyWeight -
        prevWeeklyRecord.averageBodyWeight) /
        prevWeeklyRecord.averageBodyWeight) *
      100
    ).toFixed(2);
  }

  private buildChartSeries() {
    const series: ChartSeries = this.stats.data
      .filter((r) => r.averageBodyWeight)
      .map((r, index) => {
        let displayValue = `${r.averageBodyWeight}`;
        if (r.percentageDiff && index > 0) {
          displayValue += ` [${r.percentageDiff}%]`;
        }

        return {
          x: new Date(r.records[r.records.length - 1].date).getTime(),
          y: r.averageBodyWeight,
          displayValue,
        };
      });

    this.chartOptions.series = [
      {
        name: 'AVG',
        data: series,
      },
    ];
  }
}
