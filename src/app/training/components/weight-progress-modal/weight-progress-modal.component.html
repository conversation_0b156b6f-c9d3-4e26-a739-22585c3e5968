<mpg-modal-layout-v2
  iconSrc="/assets/icon/trending-up.svg"
  title="weight.weight-progress"
  >
  <mpg-chart [chartOptions]="chartOptions" [useDisplayValue]="true"></mpg-chart>
  <mpg-period-selector
    (onNext)="handleNextEndDate()"
    (onPrev)="handlePrevEndDate()"
    [endDate]="stats?.endDate"
    [isLoading]="isLoading"
  ></mpg-period-selector>
  @for (weeklyBodyWeightRecord of stats?.data; track weeklyBodyWeightRecord; let index = $index) {
    <mpg-body-weight-record-history-card
      (onRecordClick)="handleRecordClick($event, weeklyBodyWeightRecord, index)"
      [weeklyBodyWeightRecord]="weeklyBodyWeightRecord"
    ></mpg-body-weight-record-history-card>
  }
</mpg-modal-layout-v2>
