import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { SetRecordVideoModalComponent } from './set-record-video-modal.component';
import { TranslateModule } from '@ngx-translate/core';
import { ModalLayoutModule } from '../../../shared/components/modal-layout/modal-layout.module';
import { VideoPlayerDirectiveModule } from '../../../shared/directives/video-player/video-player-directive.module';
import { EntityListModule } from '../../../shared/components/entity-list/entity-list.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    TranslateModule,
    ModalLayoutModule,
    VideoPlayerDirectiveModule,
    EntityListModule,
  ],
  declarations: [SetRecordVideoModalComponent],
  exports: [SetRecordVideoModalComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class SetRecordVideoModalModule {}
