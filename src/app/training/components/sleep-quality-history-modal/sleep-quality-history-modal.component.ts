import { Component, Input, OnInit } from '@angular/core';
import { SleepQualityService } from '../../services';
import { SleepQualityRecord } from '../../models';
import { <PERSON><PERSON>, Page } from '../../../shared/models';
import {
  Locale,
  LocalizationService,
  ModalService,
} from '../../../shared/services';
import { tap } from 'rxjs/operators';
import { Observable } from 'rxjs';
import { ApexOptions } from 'ng-apexcharts';
import { UserService } from '../../../auth/services';
import { SubscriptionPlanType } from '../../../payments/enumerations';

@Component({
  selector: 'mpg-sleep-quality-history-modal',
  templateUrl: './sleep-quality-history-modal.component.html',
  styleUrls: ['./sleep-quality-history-modal.component.scss'],
})
export class SleepQualityHistoryModalComponent implements OnInit {
  @Input() traineeId: string;
  @Input() highlightedDate: string;

  showCharts = false;

  recordsPage: Page<SleepQualityRecord>;
  locale$: Observable<Locale>;
  button: Button;
  subscriptionPlan: SubscriptionPlanType;
  SubscriptionPlanType = SubscriptionPlanType;
  chartOptions: ApexOptions = {
    tooltip: {
      enabled: false,
    },

    chart: {
      height: 370,
      type: 'line',
      toolbar: {
        show: false,
      },
      fontFamily: 'Montserrat',
    },
    series: [],
    xaxis: {
      type: 'datetime',
      labels: {
        format: 'dd-MM',
      },
      tooltip: {
        enabled: false,
      },
    },
    yaxis: {
      labels: {
        show: false,
      },
      min: 1,
      max: 100,
    },
    grid: {
      padding: {
        left: 30,
        right: 40,
      },
    },
    dataLabels: {
      enabled: true,
    },
    colors: ['#ca3535'],
  };
  private page = 1;

  constructor(
    private sleepQualityService: SleepQualityService,
    private localizationService: LocalizationService,
    private modalService: ModalService,
    private userService: UserService,
  ) {}

  get popoverMenuIconButton(): Button {
    if (!this.showCharts) {
      return {
        label: 'analytics',
        handler: () => {
          this.showCharts = true;
        },
        icon: 'bar-chart',
      };
    }

    return {
      label: 'list',
      handler: () => {
        this.showCharts = false;
      },
      icon: 'list',
    };
  }

  ngOnInit(): void {
    this.locale$ = this.localizationService.locale$;
    this.button = this.modalService.closeButton;
    this.userService.subscriptionPlan$.subscribe((subscriptionPlan) => {
      this.subscriptionPlan = subscriptionPlan;
    });

    this.fetchSleepQualityRecordsPage(this.page).subscribe();
  }

  handleInfiniteScroll(event: any) {
    this.fetchSleepQualityRecordsPage(++this.page)
      .pipe(
        tap(() => {
          event.target.complete();
        }),
      )
      .subscribe();
  }

  isHighlighted(sleepQuality: SleepQualityRecord) {
    return sleepQuality.date === this.highlightedDate;
  }

  handleWhoopRecord(sleepQuality: SleepQualityRecord) {
    this.sleepQualityService.showWhoopModal(sleepQuality);
  }

  private fetchSleepQualityRecordsPage(page: number) {
    return this.sleepQualityService
      .getHistory({
        page,
        traineeId: this.traineeId,
        maxDate: this.highlightedDate,
      })
      .pipe(
        tap((recordsPage) => {
          if (!this.recordsPage) {
            this.recordsPage = recordsPage;
            return;
          }

          this.recordsPage = {
            ...recordsPage,
            content: this.recordsPage.content.concat(recordsPage.content),
          };
        }),
        tap(() => this.buildChartData()),
      );
  }

  private buildChartData() {
    const records = this.recordsPage.content;

    const data = records.map((r) => {
      return {
        x: new Date(r.date).getTime(),
        y: r.rating,
      };
    });

    this.chartOptions.series = [{ data }];
  }
}
