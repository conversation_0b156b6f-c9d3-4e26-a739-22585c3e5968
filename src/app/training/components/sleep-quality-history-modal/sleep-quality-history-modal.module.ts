import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { SleepQualityHistoryModalComponent } from './sleep-quality-history-modal.component';
import { ModalLayoutModule } from '../../../shared/components/modal-layout/modal-layout.module';
import { TranslateModule } from '@ngx-translate/core';
import { NgApexchartsModule } from 'ng-apexcharts';
import { DataHistoryLimitItemModule } from '../../../payments/components/data-history-limit-item/data-history-limit-item.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ModalLayoutModule,
    TranslateModule,
    NgApexchartsModule,
    DataHistoryLimitItemModule,
  ],
  declarations: [SleepQualityHistoryModalComponent],
  exports: [SleepQualityHistoryModalComponent],
})
export class SleepQualityHistoryModalModule {}
