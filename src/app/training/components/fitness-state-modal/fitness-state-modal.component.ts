import { Component, Input, OnInit } from '@angular/core';
import { startCase } from 'lodash';
import { KeyValue } from '@angular/common';
import { NutritionService } from '../../../nutrition/services/nutrition.service';
import { FitnessState, Trainee } from '../../models';
import { NutritionPlanCalculationRequest } from '../../../nutrition/models';
import { Button } from '../../../shared/models';

@Component({
  selector: 'mpg-fitness-state-modal',
  templateUrl: './fitness-state-modal.component.html',
  styleUrls: ['./fitness-state-modal.component.scss'],
})
export class FitnessStateModalComponent implements OnInit {
  private static readonly FITNESS_STATE_NAME_METRIC_MAP: {
    [k: string]: { name?: string; metric?: string };
  } = {
    bodyWeightInKg: {
      name: 'Bodyweight',
    },
    bodyFatPercentage: {
      name: 'Body fat',
      metric: '%',
    },
    physicalActivityLevel: {
      name: 'PAL',
    },
    physicalActivityFactor: {
      name: 'PAL Factor',
    },
    thermicEffectOfFoodFactor: {
      name: 'TEF Factor',
    },
    trainingSessionDurationInMin: {
      name: 'Training Duration',
    },
    basalMetabolicRate: {
      name: 'BMR',
    },
    trainingEnergyExpenditure: {
      name: 'Training EE',
    },
    restingEnergyExpenditure: {
      name: 'Resting EE',
    },
    energyExpenditureOnTrainingDays: {
      name: 'Training Days EE',
    },
    maintenanceEnergyIntake: {
      name: 'Maintenance EI',
    },
    averageTargetEnergyIntake: {
      name: 'Target EI',
    },
  };
  private static readonly FITNESS_STATE_NAME_METRIC_MAP_KEYS = Object.keys(
    FitnessStateModalComponent.FITNESS_STATE_NAME_METRIC_MAP
  );
  @Input() fitnessState: FitnessState;
  @Input() trainee: Trainee;
  @Input() editHandler: () => void;
  fitnessStateMap: {};
  modalButton: Button = {
    label: 'Calculate Nutrition Plans',
    handler: () => this.handleCreateNutritionPlan(),
  };
  modalPopoverMenuButtons: Button[] = [
    {
      label: 'Edit',
      handler: () => this.editHandler(),
    },
    {
      label: 'Show History',
      handler: () => this.editHandler(),
    },
  ];

  constructor(private nutritionService: NutritionService) {}

  private static getFitnessStateKeyName(key: string) {
    let startCaseKey = startCase(key);

    const index = startCaseKey.lastIndexOf(' In ');

    if (index !== -1) {
      startCaseKey = startCaseKey.substring(0, index);
    }

    return startCaseKey;
  }

  private static getFitnessStateKeyMetric(key: string) {
    const startCaseKey = startCase(key);

    const index = startCaseKey.lastIndexOf(' In ');
    let metric = '';

    if (index !== -1) {
      metric = ' ' + startCaseKey.substring(index + 4);
    }

    return metric;
  }

  ngOnInit() {
    this.fitnessStateMap = this.makeFitnessStateMap(this.fitnessState);
  }

  fitnessStateMapOrder(
    a: KeyValue<string, string>,
    b: KeyValue<string, string>
  ): number {
    return (
      FitnessStateModalComponent.FITNESS_STATE_NAME_METRIC_MAP_KEYS.indexOf(
        a.key
      ) -
      FitnessStateModalComponent.FITNESS_STATE_NAME_METRIC_MAP_KEYS.indexOf(
        b.key
      )
    );
  }

  async handleCreateNutritionPlan() {
    const request: NutritionPlanCalculationRequest = {
      ...this.fitnessState,
      gender: this.trainee.gender,
    };

    this.nutritionService
      .calculateNutritionPlans(request)
      .subscribe(console.log);
  }

  private makeFitnessStateMap(model: FitnessState) {
    return Object.keys(model)
      .filter((k) => !['createdOn', 'id'].includes(k))
      .reduce((acc, k) => {
        const nameMetric =
          FitnessStateModalComponent.FITNESS_STATE_NAME_METRIC_MAP[k];
        const name =
          nameMetric && nameMetric.name
            ? nameMetric.name
            : FitnessStateModalComponent.getFitnessStateKeyName(k);

        const metric =
          nameMetric && nameMetric.metric
            ? nameMetric.metric
            : FitnessStateModalComponent.getFitnessStateKeyMetric(k);

        let modelValue = model[k];
        if (!isNaN(modelValue) && !Number.isInteger(modelValue)) {
          modelValue = modelValue.toFixed(2);
        }

        acc[name] = (modelValue + ' ' + metric.toUpperCase()).trim();

        return acc;
      }, {});
  }
}
