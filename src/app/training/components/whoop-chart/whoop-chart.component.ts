import { Component, Input, OnInit } from '@angular/core';
import { ApexOptions } from 'ng-apexcharts';

@Component({
  selector: 'mpg-whoop-chart',
  templateUrl: './whoop-chart.component.html',
  styleUrls: ['./whoop-chart.component.scss'],
})
export class WhoopChartComponent implements OnInit {
  @Input() type: 'sleep' | 'recovery';
  @Input() percentage: number;

  chartOptions: ApexOptions;

  constructor() {}

  ngOnInit(): void {
    this.chartOptions = {
      series: [this.percentage],
      chart: { type: 'radialBar', height: 160 },
      plotOptions: {
        radialBar: {
          track: {
            strokeWidth: '97%',
            margin: 0,
          },
          hollow: {
            size: '70%',
          },
          dataLabels: {
            show: false,
          },
        },
      },
      colors: [this.getColor()],
    };
  }

  private getColor() {
    if (this.type === 'sleep') {
      return '#7BA1BB';
    }

    if (this.percentage <= 33) {
      return '#FF0026';
    } else if (this.percentage <= 66) {
      return '#FFDE00';
    } else {
      return '#16EC06';
    }
  }
}
