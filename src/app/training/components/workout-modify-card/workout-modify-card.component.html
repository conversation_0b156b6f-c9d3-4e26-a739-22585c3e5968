<div class="card-container">
  <div class="header flex-space-between margin-bottom-sm">
    <div
      (click)="showOrderNumberControls = !showOrderNumberControls"
      class="button flex-centered order-number ion-activatable overflow-hidden"
    >
      <ion-ripple-effect></ion-ripple-effect>
      <ion-label class="order-number">{{ workout.orderNumber }}.</ion-label>
    </div>
    <ion-input [formControl]="nameFormControl" class="workout">
      @if (nameChanged) {
      <div class="ion-padding-end" slot="end">
        <mpg-loading-checkmark [isLoading]="isLoading"></mpg-loading-checkmark>
      </div>
      }
    </ion-input>
    <div (click)="handleRemove()" class="button remove">
      <ion-icon name="remove-circle"></ion-icon>
    </div>
    <div
      (click)="handleAddExercise()"
      class="button add ion-activatable overflow-hidden"
    >
      <ion-ripple-effect></ion-ripple-effect>
      <ion-icon name="add"></ion-icon>
    </div>
  </div>
  <div
    [class.visible]="showOrderNumberControls"
    class="actions flex-space-between"
  >
    @if (!isMoveUpDisabled()) {
    <div
      class="action ion-activatable overflow-hidden"
      (click)="handleMoveUp()"
    >
      <ion-ripple-effect></ion-ripple-effect>
      <ion-icon name="arrow-up"></ion-icon>
    </div>
    } @if (!isMoveDownDisabled()) {
    <div
      class="action ion-activatable overflow-hidden"
      (click)="handleMoveDown()"
    >
      <ion-ripple-effect></ion-ripple-effect>
      <ion-icon name="arrow-down"></ion-icon>
    </div>
    }
  </div>
  @for (exercise of workout.exercises; track exercise; let index = $index) {
  <mpg-workout-exercise-card
    (click)="handleSelect($event, exercise)"
    [workout]="workout"
    [workoutExercise]="exercise"
    [index]="index"
    [isSelected]="exercise === selectedExercise"
  ></mpg-workout-exercise-card>
  <div class="empty"></div>
  }
</div>
