import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { WorkoutModifyCardComponent } from './workout-modify-card.component';
import { WorkoutExerciseCardComponentModule } from '../workout-exercise-card/workout-exercise-card.module';
import { LoadingCheckmarkModule } from '../../../shared/components/loading-checkmark/loading-checkmark.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    WorkoutExerciseCardComponentModule,
    ReactiveFormsModule,
    LoadingCheckmarkModule,
  ],
  declarations: [WorkoutModifyCardComponent],
  exports: [WorkoutModifyCardComponent],
})
export class WorkoutModifyCardComponentModule {}
