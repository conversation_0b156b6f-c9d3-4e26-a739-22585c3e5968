.card-container {
  position: relative;
  padding: 12px;
  border-radius: var(--radius-medium-small);
  background: var(--color-background-primary-black-600);
  box-shadow: 0 0 0 4px var(--color-background-primary-black-700);
  border: 1px solid transparent;
  transition: border 0.3s ease-in-out;

  &.selected {
    border: 1px solid var(--text-field-stroke-hover-blue-200);
  }
}

.close {
  position: absolute;
  top: 8px;
  right: 8px;
  color: var(--color-background-secondary-white-600);
  font-size: 20px;
}

.exercise {
  border-radius: var(--radius-medium-small);
  border: 0.5px solid var(--color-background-primary-black-400);
  background: var(--color-background-primary-black-500);
  padding: 4px;
  padding-right: 8px;
  width: fit-content;
  max-width: 90%;

  ion-icon {
    font-size: 20px;
    margin-right: 8px;
    min-width: 24px;
  }

  ion-label {
    color: var(--color-background-secondary-white-600);
    font-size: 24px;
    font-weight: 300;
  }
}

.main-info {
  margin-top: 10px;
  color: var(--color-background-secondary-white-600);
  font-size: 20px;
  font-weight: 400;
  padding-left: 12px;
}

.order-number {
  color: var(--color-background-secondary-white-600);
  font-size: var(--typefaces-size-2xl);
  font-weight: 300;

  margin-right: 8px;
}

.actions {
  margin-top: 8px;
}

.action {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 36px;
  border-radius: var(--radius-extra_small);
  border: 0.5px solid var(--color-background-primary-black-400);
  background: var(--color-background-primary-black-500);
  flex-grow: 1;

  &:not(:last-of-type) {
    margin-right: 8px;
  }

  ion-icon {
    font-size: 20px;
    color: var(--color-background-secondary-white-600);
  }
}
