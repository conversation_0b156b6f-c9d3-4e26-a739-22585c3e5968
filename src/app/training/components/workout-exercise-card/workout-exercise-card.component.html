<div [class.selected]="isSelected" class="card-container">
  <ion-icon
    (click)="handleRemove($event)"
    class="close"
    name="close-outline"
  ></ion-icon>
  <div
    (click)="handleChangeExercise($event)"
    class="exercise flex-centered ion-activatable overflow-hidden"
  >
    <ion-ripple-effect></ion-ripple-effect>
    <ion-icon src="/assets/icon/menu-3.svg"></ion-icon>
    <ion-label>
      {{ getOrderNumber() }}.
      {{
        "exercises." + workoutExercise.exercise.id
          | translate: { fallback: workoutExercise.exercise.name }
      }}
    </ion-label>
  </div>

  <div class="main-info margin-bottom-sm flex-space-between">
    <ion-label
      >{{ workoutExercise.sets }} x {{ getRepRange() }}
      {{ "training.reps" | translate | uppercase }}
    </ion-label>
  </div>

  <div class="labels">
    <mpg-chip
      [value]="'training.sets-type.' + workoutExercise.setsType"
      key="training.sets-type.label"
    ></mpg-chip>
  </div>

  @if (isSelected) {
  <div class="actions flex-space-between">
    @if (!isAttachWithAboveDisabled()) {
    <div
      class="action ion-activatable overflow-hidden"
      (click)="handleAttachWithAbove()"
    >
      <ion-ripple-effect></ion-ripple-effect>
      <ion-icon name="link"></ion-icon>
    </div>
    } @if (!isDetachDisabled()) {
    <div
      class="action ion-activatable overflow-hidden"
      (click)="handleDetach()"
    >
      <ion-ripple-effect></ion-ripple-effect>
      <ion-icon src="/assets/icon/detach.svg"></ion-icon>
    </div>
    } @if (!isMoveUpDisabled()) {
    <div
      class="action ion-activatable overflow-hidden"
      (click)="handleMoveUp()"
    >
      <ion-ripple-effect></ion-ripple-effect>
      <ion-icon name="arrow-up"></ion-icon>
    </div>
    } @if (!isMoveDownDisabled()) {
    <div
      class="action ion-activatable overflow-hidden"
      (click)="handleMoveDown()"
    >
      <ion-ripple-effect></ion-ripple-effect>
      <ion-icon name="arrow-down"></ion-icon>
    </div>
    }
  </div>
  }
</div>
