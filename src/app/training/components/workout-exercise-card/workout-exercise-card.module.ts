import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { WorkoutExerciseCardComponent } from './workout-exercise-card.component';
import { TranslateModule } from '@ngx-translate/core';
import { ChipComponentModule } from '../../../shared/components/chip/chip.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    TranslateModule,
    ChipComponentModule,
  ],
  declarations: [WorkoutExerciseCardComponent],
  exports: [WorkoutExerciseCardComponent],
})
export class WorkoutExerciseCardComponentModule {}
