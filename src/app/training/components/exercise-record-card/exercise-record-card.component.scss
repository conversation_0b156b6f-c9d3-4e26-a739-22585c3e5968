.container {
  color: var(--color-background-secondary-white-500);
  background: var(--color-background-primary-black-700);
  padding: 16px;
}

.order-number {
  font-size: 30px;
  margin-left: 8px;
  margin-right: 16px;
  font-weight: 350;
}

.exercise {
  flex-grow: 1;
  font-size: 16px;
  font-weight: 350;
}

.reps {
  font-size: 20px;
  text-align: center;
  white-space: nowrap;
  margin-right: 8px;

  @media (max-width: 380px) {
    span {
      display: none;
    }
  }
}

.main-content {
  margin-top: 16px;
}

mpg-options-button {
  margin-bottom: 3px;
}

.history {
  color: var(--color-background-secondary-white-600);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;

  ion-icon {
    font-size: 20px;
    cursor: pointer;
  }

  .mini-card {
    border-radius: var(--radius-medium);
    background: var(--color-background-primary-black-500);
    padding-right: 12px;
    padding-top: 12px;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;

    .header {
      font-size: 16px;
      font-weight: 350;
      height: 24px;
    }

    .set-records {
      font-weight: 400;
      font-size: 24px;
    }

    .nav-dots {
      height: 10px;
      margin-top: 8px;
      display: flex;
      justify-content: flex-end;
      align-items: center;

      .nav-dot {
        top: -5px;
        width: 11px;
        height: 11px;
        position: relative;
        border-radius: 100%;
        display: inline-block;
        background-color: var(--color-background-primary-black-200);

        &.active {
          background-color: var(--color-background-secondary-white-600);
        }

        &:not(:last-of-type) {
          margin-right: 6px;
        }
      }
    }
  }
}

@keyframes bounce {
  0% {
    transform: scale(1);
  }
  30% {
    transform: scale(1.1);
  }
  50% {
    transform: scale(0.95);
  }
  70% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.bounce {
  animation: bounce 0.4s ease-in-out;
}

