<div class="container">
  <div class="header flex-space-between">
    <div class="start flex-start">
      <ion-label class="order-number">{{ orderNumber }}</ion-label>
      <ion-label class="exercise"
        >{{
          "exercises." + exerciseRecord.exercise.exercise.id
            | translate
              : {
                  fallback: exerciseRecord.exercise.exercise.name
                }
        }}
      </ion-label>
    </div>
    <div class="end flex-centered">
      <ion-label class="reps">
        @if (!exerciseRecord.isSkipped) {
        {{ repsTarget }}
        <span>{{ "training.reps" | translate | uppercase }}</span>
        } @else {
        <span>{{ "skipped" | translate | uppercase }}</span>
        }
      </ion-label>
      <mpg-options-button [options]="options"></mpg-options-button>
    </div>
  </div>
  <div class="main-content">
    @if (exerciseRecord?.exercise?.setsType !== SetsType.STRAIGHT) {
    <div class="labels margin-bottom-m">
      <mpg-chip
        [value]="'training.sets-type.' + exerciseRecord.exercise.setsType"
        key="training.sets-type.label"
      ></mpg-chip>
    </div>
    }
    <div class="history">
      @if (exerciseRecord?.history?.latestRecordForExercise) {
      <div
        (animationend)="isBouncing = false"
        [class.bounce]="isBouncing"
        class="mini-card flex-grow padding-sm margin-right-m"
      >
        <div class="header flex-space-between margin-bottom-sm">
          <ion-label class="margin-left-sm flex-grow"
            >{{
              (historySegment === "exercise"
                ? "training.last-time"
                : "training.last-workout"
              ) | translate
            }}
          </ion-label>
          <ion-icon
            (click)="handleHistoryExpand()"
            src="/assets/icon/expand.svg"
          ></ion-icon>
        </div>
        <div (click)="handleBounce()" class="content">
          <div class="set-records margin-left-sm">
            @for (set of splitHistorySetRecordsText; track set) {
            {{ set }}<br />
            } @if (isHistoryRecordSkipped) {
            {{ "skipped" | translate }}
            }
          </div>
          @if (shouldShowNavDots()) {
          <div class="nav-dots">
            <div
              [class.active]="historySegment === 'workout-exercise'"
              class="nav-dot"
            ></div>
            <div
              [class.active]="historySegment === 'exercise'"
              class="nav-dot"
            ></div>
          </div>
          }
        </div>
      </div>
      } @if (exerciseRecord?.history?.prRecordForExercise) {
      <div class="mini-card flex-grow padding-sm">
        <div class="header flex-space-between margin-bottom-sm">
          <ion-label class="margin-left-sm"
            >{{ "training.sets-history.pr" | translate }}
          </ion-label>
          <ion-icon
            (click)="handlePRsExpand()"
            src="/assets/icon/expand.svg"
          ></ion-icon>
        </div>
        <div class="content">
          <div class="set-records margin-left-sm">
            @for (set of
            exerciseRecord.history.prRecordForExercise.setRecordsAsText.split(
            ';'); track set) {
            {{ set }}<br />
            }
          </div>
        </div>
      </div>
      }
    </div>

    <mpg-exercise-record-form-v2
      [exerciseRecord]="exerciseRecord"
      [setRecordsFormGroupMap]="setRecordsFormGroupMap"
      [setRecordsLoadingMap]="setRecordsLoadingMap"
      [workoutRecord]="workoutRecord"
    ></mpg-exercise-record-form-v2>
  </div>
</div>
