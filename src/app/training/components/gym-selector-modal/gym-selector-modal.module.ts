import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { GymSelectorModalComponent } from './gym-selector-modal.component';
import { ModalLayoutV2Module } from '../../../shared/components/modal-layout-v2/modal-layout-v2.module';
import { TranslateModule } from '@ngx-translate/core';
import { ButtonModule } from '../../../shared/components/button/button.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ModalLayoutV2Module,
    TranslateModule,
    ButtonModule,
    ReactiveFormsModule,
  ],
  declarations: [GymSelectorModalComponent],
  exports: [GymSelectorModalComponent],
})
export class GymSelectorModalModule {}
