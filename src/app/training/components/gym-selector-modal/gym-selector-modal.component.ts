import { Component, Input, OnInit } from '@angular/core';
import { Button } from '../../../shared/models';
import { Gym } from '../../models';
import {
  AlertService,
  ModalService,
  ToastService,
} from '../../../shared/services';
import { GymService, TraineeService } from '../../services';
import { FormControl, Validators } from '@angular/forms';
import { MPGValidators } from '../../../shared/validators/validators';

@Component({
  selector: 'mpg-gym-selector-modal',
  templateUrl: './gym-selector-modal.component.html',
  styleUrls: ['./gym-selector-modal.component.scss'],
})
export class GymSelectorModalComponent implements OnInit {
  @Input() gyms: Gym[] = [];
  @Input() selectedGym: Gym;
  isLoading = false;

  formControl = new FormControl(null, [
    Validators.required,
    MPGValidators.notIn(() => this.gyms.map((g) => g.name)),
  ]);

  button: Button = {
    label: 'buttons.done',
    handler: () => {
      this.modalService.closeTopModal(this.selectedGym);
    },
  };

  constructor(
    private modalService: ModalService,
    private alertService: AlertService,
    private toastService: ToastService,
    private traineeService: TraineeService,
    private gymService: GymService,
  ) {}

  ngOnInit() {}

  handleGymPin(gym: Gym) {
    if (gym.isDefault) {
      return;
    }

    this.alertService.createConfirmAlert(
      'training.set-default-gym-confirm',
      () => {
        this.traineeService.putDefaultGym(gym.id).subscribe(() => {
          this.gyms.forEach((g) => (g.isDefault = false));
          gym.isDefault = true;
          this.toastService.showInfoToast('training.set-default-gym-success');
        });
      },
    );
  }

  handleDeleteGym(gym: Gym) {
    this.alertService.createDeleteAlert(() => {
      this.gymService.delete(gym.id).subscribe(() => {
        this.gyms = this.gyms.filter((g) => g.id !== gym.id);
      });
    });
  }

  handleCreateGym() {
    this.isLoading = true;
    this.gymService
      .create({ name: this.formControl.value })
      .subscribe((gym) => {
        this.gyms = [...this.gyms, gym];
        this.isLoading = false;
        this.formControl.reset();
      });
  }
}
