import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BodyWeightRecordCardComponent } from './body-weight-record-card.component';
import { CardV2ComponentModule } from '../../../shared/components/card-v2/card-v2.module';
import { IonicModule } from '@ionic/angular';
import { ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { LoadingCheckmarkModule } from '../../../shared/components/loading-checkmark/loading-checkmark.module';
import { ButtonModule } from '../../../shared/components/button/button.module';

@NgModule({
  declarations: [BodyWeightRecordCardComponent],
  exports: [BodyWeightRecordCardComponent],
  imports: [
    CommonModule,
    CardV2ComponentModule,
    IonicModule,
    ReactiveFormsModule,
    TranslateModule,
    LoadingCheckmarkModule,
    ButtonModule,
  ],
})
export class BodyWeightRecordCardModule {}
