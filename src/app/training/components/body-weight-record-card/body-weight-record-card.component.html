<mpg-card-v2
  [(selectedSegmentName)]="selectedSegment"
  [segments]="segments"
  iconSrc="/assets/icon/body-outline.svg"
  title="weight.insights"
>
  @if (selectedSegment === 'main') {
  <div>
    <div class="p-relative flex">
      <ion-input
        [formControl]="weightFormControl"
        [placeholder]="'weight.today-s-weight' | translate"
        type="number"
      >
        @if (weightFormControl.value) {
        <div class="ion-padding-end" slot="end">
          <mpg-loading-checkmark
            [isLoading]="isLoading"
          ></mpg-loading-checkmark>
        </div>
        }
      </ion-input>
      @if (weightFormControl.value) {
      <span [style]="{ left: unitLeft + 'px' }" class="unit">KG</span>
      }
    </div>
    <div class="footer flex-space-between w-full">
      <mpg-button
        (click)="handleWeightHistoryClick()"
        [iconOnly]="true"
        color="dark"
      >
        <ion-icon src="/assets/icon/frame-2.svg"></ion-icon>
      </mpg-button>
      <mpg-button (click)="selectedSegment = 'comparison'" class="avg">
        AVG
        {{
          currentWeek?.averageBodyWeight
            ? (currentWeek?.averageBodyWeight | number: "1.2-2")
            : "N/A"
        }}
        KG
      </mpg-button>
    </div>
  </div>
  } @if (selectedSegment === 'comparison') {
  <div>
    @if (currentWeek?.percentageDiff && currentWeek?.percentageDiff !== 0) {
    <div class="diff-container flex-centered">
      <div
        [ngClass]="{ red: currentWeek?.percentageDiff < 0 }"
        class="diff flex-centered"
      >
        DIFF {{ currentWeek?.percentageDiff }} %
        <ion-icon
          [name]="
            currentWeek?.percentageDiff > 0 ? 'trending-up' : 'trending-down'
          "
        ></ion-icon>
      </div>
    </div>
    }
    <div class="flex-space-between">
      @for (record of previousWeek?.records; track record) {
      <div class="record-container flex-column-space-around">
        <ion-label class="day"
          >{{ "days." + record?.dayOfWeek | translate | slice: 0 : 2 }}
        </ion-label>
        <ion-label class="date">{{ record?.date | date: "dd" }}</ion-label>
        <ion-label class="weight flex-centered">
          <ion-label
            >{{ record.bodyWeightInKg ? record.bodyWeightInKg : "-" }}
          </ion-label>
        </ion-label>
      </div>
      }
    </div>
    <div class="weeks">
      <svg
        class="prev"
        fill="none"
        viewBox="0 0 186 64"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M167.082 0H137.252H2C0.89543 0 0 0.895431 0 2V62C0 63.1046 0.895433 64 2 64H136.427C136.955 64 137.462 63.7911 137.837 63.4188L184.748 16.811C186.372 15.1978 184.451 12.5637 182.419 13.6162L162.98 23.6823C161.279 24.5632 159.432 22.7793 160.254 21.0487L168.888 2.85763C169.518 1.53061 168.551 0 167.082 0Z"
          fill="#FF0F1E"
        />
      </svg>
      <div class="prev-week-text flex-column-start">
        <ion-label class="week">Last week</ion-label>
        <ion-label class="weight">
          <ion-text>{{ previousWeek.averageBodyWeight || "-" }}</ion-text>
          <span class="kg"> KG</span>
        </ion-label>
      </div>
      <svg
        class="current"
        fill="none"
        viewBox="0 0 186 64"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M18.9183 64H48.7483H184C185.105 64 186 63.1046 186 62V2C186 0.895428 185.105 0 184 0H49.5729C49.0447 0 48.538 0.208931 48.1633 0.581196L1.25185 47.189C-0.371887 48.8022 1.5486 51.4363 3.58115 50.3838L23.0199 40.3177C24.721 39.4368 26.5678 41.2207 25.7464 42.9513L17.1115 61.1424C16.4816 62.4694 17.4494 64 18.9183 64Z"
          fill="#007BFF"
        />
      </svg>
      <div class="current-week-text flex-column-end">
        <ion-label class="week">This week</ion-label>
        <ion-label class="weight">
          <ion-text>{{ currentWeek.averageBodyWeight || "-" }}</ion-text>
          <span class="kg"> KG</span>
        </ion-label>
      </div>
    </div>
    <div class="flex-space-between">
      @for (record of currentWeek?.records; track record) {
      <div class="record-container flex-column-space-around">
        <ion-label class="day"
          >{{ "days." + record?.dayOfWeek | translate | slice: 0 : 2 }}
        </ion-label>
        <ion-label class="date">{{ record?.date | date: "dd" }}</ion-label>
        <ion-label class="weight flex-centered">
          <ion-label
            >{{ record.bodyWeightInKg ? record.bodyWeightInKg : "-" }}
          </ion-label>
        </ion-label>
      </div>
      }
    </div>
  </div>
  }
</mpg-card-v2>
