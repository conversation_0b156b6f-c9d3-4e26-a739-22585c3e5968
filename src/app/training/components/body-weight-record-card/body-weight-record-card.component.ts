import { Component, OnInit } from '@angular/core';
import { BodyWeightService } from '../../services';
import { FormControl, Validators } from '@angular/forms';
import { catchError, switchMap, tap } from 'rxjs/operators';
import { combineLatestWith, debounceTime, EMPTY } from 'rxjs';
import { DateService, ModalService } from '../../../shared/services';
import { WeeklyBodyWeightRecord } from '../../models';
import { WeightProgressModalComponent } from '../weight-progress-modal/weight-progress-modal.component';
import { Segment } from '../../../shared/models';

@Component({
  selector: 'mpg-body-weight-record-card',
  templateUrl: './body-weight-record-card.component.html',
  styleUrls: ['./body-weight-record-card.component.scss'],
})
export class BodyWeightRecordCardComponent implements OnInit {
  currentWeight: number;
  weightFormControl = new FormControl(null, Validators.required);
  unitLeft: number;
  isLoading = false;
  currentWeek: WeeklyBodyWeightRecord;
  previousWeek: WeeklyBodyWeightRecord;
  segments: Segment[] = [
    {
      label: 'weight.insights',
      value: 'main',
      iconSrc: '/assets/icon/frame.svg',
    },
    {
      label: 'weight.avg-comparison',
      value: 'comparison',
      iconSrc: '/assets/icon/comparison.svg',
    },
  ];

  selectedSegment = 'main';

  constructor(
    private bodyWeightService: BodyWeightService,
    private dateService: DateService,
    private modalService: ModalService,
  ) {}

  ngOnInit() {
    this.bodyWeightService
      .getBodyWeightRecord()
      .subscribe((bodyWeightRecord) => {
        this.currentWeight = bodyWeightRecord?.bodyWeightInKg;
        if (this.currentWeight) {
          this.weightFormControl.setValue(this.currentWeight);
        }
      });

    this.getWeeklyBodyWeightRecords().subscribe(
      ([currentWeek, previousWeek]) => {
        this.currentWeek = currentWeek;
        this.previousWeek = previousWeek;
      },
    );

    this.weightFormControl.valueChanges
      .pipe(
        tap((value) => {
          const valueStr = '' + value;
          const hasDecimal = valueStr.includes('.') || valueStr.includes(',');
          const decimalCorrection = hasDecimal ? 5 : 0;

          this.unitLeft = 17 + valueStr.length * 9 - decimalCorrection;
        }),
        tap(() => {
          this.isLoading = true;
        }),
        debounceTime(200),
        switchMap((value) => {
          return this.bodyWeightService
            .putBodyWeightRecord({
              bodyWeightInKg: value,
            })
            .pipe(catchError(() => EMPTY));
        }),
        tap(() => {
          this.isLoading = false;
        }),
        tap((value) => {
          this.currentWeight = value.bodyWeightInKg;
        }),
        switchMap(() => {
          return this.getWeeklyBodyWeightRecords();
        }),
      )
      .subscribe(([currentWeek, previousWeek]) => {
        this.currentWeek = currentWeek;
        this.previousWeek = previousWeek;
      });
  }

  handleWeightHistoryClick() {
    this.modalService
      .create({
        component: WeightProgressModalComponent,
        animated: true,
      })
      .subscribe();
  }

  private getWeeklyBodyWeightRecords() {
    return this.bodyWeightService
      .getWeeklyBodyWeightRecord()
      .pipe(
        combineLatestWith(
          this.bodyWeightService.getWeeklyBodyWeightRecord(
            this.dateService.getCurrentDateMinusWeeks(1),
          ),
        ),
      );
  }
}
