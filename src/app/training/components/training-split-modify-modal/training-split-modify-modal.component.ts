import { Component, Input, OnInit } from '@angular/core';
import { TrainingSplit, Workout, WorkoutExercise } from '../../models';
import { But<PERSON> } from '../../../shared/models';
import { TrainingSplitService, WorkoutService } from '../../services';
import { ModalService } from '../../../shared/services';
import { switchMap } from 'rxjs';

@Component({
  selector: 'mpg-training-split-modify-modal',
  templateUrl: './training-split-modify-modal.component.html',
  styleUrls: ['./training-split-modify-modal.component.scss'],
})
export class TrainingSplitModifyModalComponent implements OnInit {
  @Input() trainingSplit: TrainingSplit;
  selectedExercise: WorkoutExercise;
  modalButton: Button;

  constructor(
    private workoutService: WorkoutService,
    private modalService: ModalService,
    private trainingSplitService: TrainingSplitService,
  ) {}

  ngOnInit() {}

  handleDeleteWorkout(workout: Workout) {
    this.trainingSplit.workouts = this.trainingSplit.workouts.filter(
      (w) => w.id !== workout.id,
    );
  }

  handleCreate() {
    this.modalService
      .createSimpleInput({
        title: 'training.create-workout',
        type: 'text',
        label: 'name',
      })
      .pipe(
        switchMap((name: string) => {
          return this.workoutService.create({
            name,
            trainingSplitId: this.trainingSplit.id,
          });
        }),
      )
      .subscribe((workout) => {
        this.trainingSplit.workouts =
          this.trainingSplit.workouts.concat(workout);
      });
  }

  handleWorkoutMoveUp(index: number) {
    const workouts = this.trainingSplit.workouts;
    const current = workouts[index];
    const above = workouts[index - 1];

    current.orderNumber -= 1;
    above.orderNumber += 1;

    this.trainingSplitService
      .editWorkoutsOrder(this.trainingSplit.id, this.trainingSplit)
      .subscribe(() => {
        this.trainingSplit.workouts = [...workouts].sort(
          (a, b) => a.orderNumber - b.orderNumber,
        );
      });
  }

  handleWorkoutMoveDown(index: number) {
    const workouts = this.trainingSplit.workouts;
    const current = workouts[index];
    const below = workouts[index + 1];

    current.orderNumber += 1;
    below.orderNumber -= 1;

    this.trainingSplitService
      .editWorkoutsOrder(this.trainingSplit.id, this.trainingSplit)
      .subscribe(() => {
        this.trainingSplit.workouts = [...workouts].sort(
          (a, b) => a.orderNumber - b.orderNumber,
        );
      });
  }
}
