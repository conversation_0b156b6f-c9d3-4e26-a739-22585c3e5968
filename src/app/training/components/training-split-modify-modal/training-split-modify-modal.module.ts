import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { TrainingSplitModifyModalComponent } from './training-split-modify-modal.component';
import { ModalLayoutV2Module } from '../../../shared/components/modal-layout-v2/modal-layout-v2.module';
import { WorkoutModifyCardComponentModule } from '../workout-modify-card/workout-modify-card.module';
import { TranslateModule } from '@ngx-translate/core';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ModalLayoutV2Module,
    WorkoutModifyCardComponentModule,
    TranslateModule,
  ],
  declarations: [TrainingSplitModifyModalComponent],
  exports: [TrainingSplitModifyModalComponent],
})
export class TrainingSplitModifyModalModule {}
