import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Button } from '../../../shared/models';
import { ModalService } from '../../../shared/services';
import { TrainingSplitCreateRequest } from '../../models';
import { TraineeService, TrainingSplitService } from '../../services';
import { take } from 'rxjs/operators';
import { switchMap } from 'rxjs';

@Component({
  selector: 'mpg-create-training-split',
  templateUrl: './create-training-split-modal.component.html',
  styleUrls: ['./create-training-split-modal.component.scss'],
})
export class CreateTrainingSplitModalComponent implements OnInit {
  createTrainingSplitFormGroup: FormGroup;
  modalButton: Button = {
    label: 'Submit',
    handler: () => this.handleSubmit(),
    disabled: () => this.createTrainingSplitFormGroup?.invalid,
  };

  constructor(
    private fb: FormBuilder,
    private trainingSplitService: TrainingSplitService,
    private modalService: ModalService,
    private traineeService: TraineeService,
  ) {}

  ngOnInit() {
    this.createTrainingSplitFormGroup = this.fb.group({
      name: [null, [Validators.required]],
    });
  }

  handleSubmit() {
    if (this.createTrainingSplitFormGroup.invalid) {
      return;
    }

    const formValue: TrainingSplitCreateRequest =
      this.createTrainingSplitFormGroup.value;
    this.traineeService.trainerId$
      .pipe(
        take(1),
        switchMap((trainerId) => {
          return this.trainingSplitService.create({
            name: formValue.name,
            trainerId,
          });
        }),
      )
      .subscribe((trainingSplit) => {
        this.modalService.closeTopModal(trainingSplit);
      });
  }
}
