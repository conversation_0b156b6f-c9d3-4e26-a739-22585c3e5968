import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { CreateTrainingSplitModalComponent } from './create-training-split-modal.component';
import { ModalLayoutModule } from '../../../shared/components/modal-layout/modal-layout.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ReactiveFormsModule,
    ModalLayoutModule,
  ],
  declarations: [CreateTrainingSplitModalComponent],
  exports: [CreateTrainingSplitModalComponent],
})
export class CreateTrainingSplitModalModule {}
