import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { ExerciseRecordFormV2Component } from './exercise-record-form-v2.component';
import { ModalLayoutModule } from '../../../shared/components/modal-layout/modal-layout.module';
import { MaxLengthDirectiveModule } from '../../../shared/directives/max-length/max-length-directive.module';
import { TranslateModule } from '@ngx-translate/core';
import { LoadingCheckmarkModule } from '../../../shared/components/loading-checkmark/loading-checkmark.module';
import { ExerciseRecordSmartNotesModalModule } from '../exercise-record-smart-notes-modal/exercise-record-smart-notes-modal.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ReactiveFormsModule,
    ModalLayoutModule,
    MaxLengthDirectiveModule,
    TranslateModule,
    LoadingCheckmarkModule,
    ExerciseRecordSmartNotesModalModule,
  ],
  declarations: [ExerciseRecordFormV2Component],
  exports: [ExerciseRecordFormV2Component],
})
export class ExerciseRecordFormV2Module {}
