.set {
  border-radius: var(--radius-medium-small);
  background: var(--color-background-primary-black-500);
  margin: 16px 0;
  padding: 8px;


  ion-label {
    font-size: 18px;
    margin-left: 10px;
    margin-right: 10px;
  }

  mpg-loading-checkmark {
    margin-top: 2px;
    margin-left: 5px;
  }

  ion-input {
    width: 75px;
    height: 20px !important;
    text-align: center;
    --padding-start: 0 !important;
    --background: var(--color-background-primary-black-500) !important;
    --border-radius: var(--radius-medium) !important;
  }

  ion-icon {
    font-size: 24px;
    display: block;
    --color: var(--color-background-secondary-white-900);

    &.clickable-icon {
      cursor: pointer;
      transition: all 0.2s ease;
      padding: 4px;
      border-radius: 4px;

      &:hover {
        --color: var(--ion-color-primary);
        background: var(--color-background-primary-black-400);
      }

      &:active {
        transform: scale(0.95);
      }
    }
  }

  &:last-of-type {
    margin-bottom: 0;
  }
}
