<input
  #videoInput
  (change)="handleVideoSelect($event)"
  accept="video/*"
  class="invisible"
  type="file"
/>
@for (setRecord of exerciseRecord.setRecords; track setRecord; let i = $index) {
  <div
    [formGroup]="setRecordsFormGroupMap[setRecord.id]"
    class="set flex-space-between"
  >
    @if (setRecordsFormGroupMap[setRecord.id].invalid) {
      <ion-label>{{ setRecord.orderNumber }}</ion-label>
    } @else {
      <mpg-loading-checkmark
        [isLoading]="setRecordsLoadingMap && setRecordsLoadingMap[setRecord.id]"
      ></mpg-loading-checkmark>
    }
    <ion-input
      [placeholder]="'weight.kg' | translate"
      formControlName="weightInKg"
      maxlength="3"
      mpgMaxLength
      type="number"
    ></ion-input>
    <ion-icon src="/assets/icon/cross.svg"></ion-icon>
    <ion-input
      [placeholder]="'training.reps' | translate"
      formControlName="reps"
      maxlength="3"
      mpgMaxLength
      type="number"
    ></ion-input>
    <ion-icon
      name="chevron-forward"
      class="clickable-icon"
      (click)="handleExerciseRecordClick()"
    ></ion-icon>
  </div>
}
