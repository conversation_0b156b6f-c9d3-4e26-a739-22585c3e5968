<div class="card-container">
  <div class="header flex-space-between">
    <div class="flex-start">
      <ion-label class="order-number">{{ workout.orderNumber }}</ion-label>
      <ion-label> {{ workout.name }}</ion-label>
    </div>
    <div>
      <ion-icon
        (click)="selectedSegment = 'exercises'"
        [class.selected]="selectedSegment === 'exercises'"
        src="/assets/icon/treadmill.svg"
      ></ion-icon>
      <ion-icon
        (click)="selectedSegment = 'volume'"
        [class.selected]="selectedSegment === 'volume'"
        [src]="
          selectedSegment === 'volume'
            ? '/assets/icon/volume.svg'
            : '/assets/icon/volume-outline.svg'
        "
      ></ion-icon>
    </div>
  </div>
  <div class="content">
    @if (selectedSegment === 'exercises') { @for (exercise of
    workout?.exercises; track exercise) {
    <div
      class="exercise flex-space-between ion-activatable overflow-hidden"
      (click)="handleExerciseOptions(exercise)"
    >
      <ion-ripple-effect></ion-ripple-effect>
      <div class="flex-start">
        <ion-label class="order-number"
          >{{ getOrderNumber(exercise) }}
        </ion-label>
        <ion-label class="name"
          >{{
            "exercises." + exercise.exercise.id
              | translate: { fallback: exercise.exercise.name }
          }}
        </ion-label>
      </div>
      <ion-icon name="chevron-forward"></ion-icon>
    </div>
    } } @else {
    <div class="padding-m">
      <mpg-training-volume-table
        [trainingVolume]="workout.trainingVolume"
      ></mpg-training-volume-table>
    </div>
    }
  </div>
</div>
