<mpg-modal-layout
  [button]="modalButton"
  title="training.create-training-time-schedule"
>
  <ion-item>
    <ion-label>{{ "days.MONDAY" | translate }}</ion-label>
    <ion-datetime-button datetime="monday"></ion-datetime-button>
  </ion-item>
  <ion-item>
    <ion-label>{{ "days.TUESDAY" | translate }}</ion-label>
    <ion-datetime-button datetime="tuesday"></ion-datetime-button>
  </ion-item>
  <ion-item>
    <ion-label>{{ "days.WEDNESDAY" | translate }}</ion-label>
    <ion-datetime-button datetime="wednesday"></ion-datetime-button>
  </ion-item>
  <ion-item>
    <ion-label>{{ "days.THURSDAY" | translate }}</ion-label>
    <ion-datetime-button datetime="thursday"></ion-datetime-button>
  </ion-item>
  <ion-item>
    <ion-label>{{ "days.FRIDAY" | translate }}</ion-label>
    <ion-datetime-button datetime="friday"></ion-datetime-button>
  </ion-item>
  <ion-item>
    <ion-label>{{ "days.SATURDAY" | translate }}</ion-label>
    <ion-datetime-button datetime="saturday"></ion-datetime-button>
  </ion-item>
  <ion-item>
    <ion-label>{{ "days.SUNDAY" | translate }}</ion-label>
    <ion-datetime-button datetime="sunday"></ion-datetime-button>
  </ion-item>
</mpg-modal-layout>

<form [formGroup]="createTrainingTimeScheduleFormGroup">
  <ion-modal [keepContentsMounted]="true">
    <ng-template>
      <ion-datetime
        [locale]="locale$ | async"
        [minuteValues]="[0, 15, 30, 45]"
        formControlName="monday"
        id="monday"
        presentation="time"
      ></ion-datetime>
    </ng-template>
  </ion-modal>
  <ion-modal [keepContentsMounted]="true">
    <ng-template>
      <ion-datetime
        [locale]="locale$ | async"
        [minuteValues]="[0, 15, 30, 45]"
        formControlName="tuesday"
        id="tuesday"
        presentation="time"
      ></ion-datetime>
    </ng-template>
  </ion-modal>
  <ion-modal [keepContentsMounted]="true">
    <ng-template>
      <ion-datetime
        [locale]="locale$ | async"
        [minuteValues]="[0, 15, 30, 45]"
        formControlName="wednesday"
        id="wednesday"
        presentation="time"
      ></ion-datetime>
    </ng-template>
  </ion-modal>
  <ion-modal [keepContentsMounted]="true">
    <ng-template>
      <ion-datetime
        [locale]="locale$ | async"
        [minuteValues]="[0, 15, 30, 45]"
        formControlName="thursday"
        id="thursday"
        presentation="time"
      ></ion-datetime>
    </ng-template>
  </ion-modal>
  <ion-modal [keepContentsMounted]="true">
    <ng-template>
      <ion-datetime
        [locale]="locale$ | async"
        [minuteValues]="[0, 15, 30, 45]"
        formControlName="friday"
        id="friday"
        presentation="time"
      ></ion-datetime>
    </ng-template>
  </ion-modal>
  <ion-modal [keepContentsMounted]="true">
    <ng-template>
      <ion-datetime
        [locale]="locale$ | async"
        [minuteValues]="[0, 15, 30, 45]"
        formControlName="saturday"
        id="saturday"
        presentation="time"
      ></ion-datetime>
    </ng-template>
  </ion-modal>
  <ion-modal [keepContentsMounted]="true">
    <ng-template>
      <ion-datetime
        [locale]="locale$ | async"
        [minuteValues]="[0, 15, 30, 45]"
        formControlName="sunday"
        id="sunday"
        presentation="time"
      ></ion-datetime>
    </ng-template>
  </ion-modal>
</form>
