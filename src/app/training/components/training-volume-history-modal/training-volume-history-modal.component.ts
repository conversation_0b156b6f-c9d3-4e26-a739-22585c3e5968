import { Component, OnInit } from '@angular/core';
import { TrainingVolumeHistory } from '../../models';
import { WorkoutRecordService } from '../../services';
import {
  DateService,
  Locale,
  LocalizationService,
} from '../../../shared/services';
import {
  BehaviorSubject,
  debounceTime,
  filter,
  Observable,
  switchMap,
} from 'rxjs';
import { tap } from 'rxjs/operators';

@Component({
  selector: 'mpg-training-volume-history-modal',
  templateUrl: './training-volume-history-modal.component.html',
  styleUrls: ['./training-volume-history-modal.component.scss'],
})
export class TrainingVolumeHistoryModalComponent implements OnInit {
  trainingVolumeHistory: TrainingVolumeHistory;
  locale$: Observable<Locale>;
  isLoading = false;
  dateSubject$ = new BehaviorSubject<string>(null);

  constructor(
    private workoutRecordService: WorkoutRecordService,
    private dateService: DateService,
    private localizationService: LocalizationService,
  ) {}

  ngOnInit() {
    this.dateSubject$
      .pipe(
        tap(() => (this.isLoading = true)),
        debounceTime(300),
        filter((date) => !!date),
        switchMap((date) =>
          this.workoutRecordService.getTrainingVolumeHistory(date),
        ),
      )
      .subscribe((trainingVolumeHistory) => {
        this.isLoading = false;
        this.trainingVolumeHistory = trainingVolumeHistory;
      });

    this.dateSubject$.next(this.dateService.getCurrentDate());
    this.locale$ = this.localizationService.locale$;
  }

  moveForward() {
    this.dateSubject$.next(
      this.dateService.getDatePlusDays(this.dateSubject$.value, 7),
    );
  }

  moveBackwards() {
    this.dateSubject$.next(
      this.dateService.getDateMinusDays(this.dateSubject$.value, 7),
    );
  }

  handleDateChange(event: any) {
    this.dateSubject$.next(event.detail.value);
  }
}
