import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { TrainingVolumeHistoryModalComponent } from './training-volume-history-modal.component';
import { ModalLayoutModule } from '../../../shared/components/modal-layout/modal-layout.module';
import { WorkoutTableModule } from '../workout-table/workout-table.module';
import { CardComponentModule } from '../../../shared/components/card/card.module';
import { TranslateModule } from '@ngx-translate/core';
import { TrainingVolumeTableModule } from '../training-volume-table/training-volume-table.module';
import { LoadingSpinnerComponentModule } from '../../../shared/components/loading-spinner/loading-spinner.module';
import { RelativeDatePipeModule } from '../../../shared/pipes/relative-date/relative-date-pipe.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ModalLayoutModule,
    WorkoutTableModule,
    CardComponentModule,
    TranslateModule,
    TrainingVolumeTableModule,
    LoadingSpinnerComponentModule,
    RelativeDatePipeModule,
  ],
  declarations: [TrainingVolumeHistoryModalComponent],
  exports: [TrainingVolumeHistoryModalComponent],
})
export class TrainingVolumeHistoryModalModule {}
