<mpg-modal-layout
  [subtitle]=""
  [title]="'training.training-volume-history' | translate"
  >
  @if (trainingVolumeHistory) {
    <ion-item
      class="no-end-padding ion-no-padding light-background p-sticky z-100"
      >
      <ion-label class="ion-text-center flex-space-around">
        <ion-fab-button (click)="moveBackwards()" color="dark" size="small">
          <ion-icon name="arrow-back"></ion-icon>
        </ion-fab-button>
        <ion-datetime-button class="date-button" color="dark" datetime="datetime">
          <ion-text slot="date-target">
            {{
            trainingVolumeHistory?.startDate
            | date: "mediumDate" : undefined : (locale$ | async)
            }}
            -
            {{
            trainingVolumeHistory?.endDate
            | date: "mediumDate" : undefined : (locale$ | async)
            }}
          </ion-text>
        </ion-datetime-button>
        <ion-fab-button (click)="moveForward()" color="dark" size="small">
          <ion-icon name="arrow-forward"></ion-icon>
        </ion-fab-button>
      </ion-label>
    </ion-item>
  }
  <ion-card-content class="light-background ion-no-padding">
    @if (trainingVolumeHistory?.trainingVolume && !isLoading) {
      <mpg-training-volume-table
        [trainingVolume]="trainingVolumeHistory.trainingVolume"
      ></mpg-training-volume-table>
    }
  </ion-card-content>
  <mpg-loading-spinner
    [fullScreen]="false"
    [isLoading]="isLoading"
  ></mpg-loading-spinner>
</mpg-modal-layout>
<ion-modal [keepContentsMounted]="true">
  <ng-template>
    <ion-datetime
      (ionChange)="handleDateChange($event)"
      [value]="dateSubject$ | async"
      id="datetime"
      presentation="date"
    ></ion-datetime>
  </ng-template>
</ion-modal>
