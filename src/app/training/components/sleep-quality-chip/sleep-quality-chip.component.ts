import { Component, Input, OnInit } from '@angular/core';
import { SleepQualityService } from '../../services';
import { DateService } from '../../../shared/services';

@Component({
  selector: 'mpg-sleep-quality-chip',
  templateUrl: './sleep-quality-chip.component.html',
  styleUrls: ['./sleep-quality-chip.component.scss'],
})
export class SleepQualityChipComponent implements OnInit {
  @Input() rating: number;
  @Input() date: string;
  @Input() color: 'light' | 'dark' = 'light';
  @Input() traineeId: string;

  constructor(
    private sleepQualityService: SleepQualityService,
    private dateService: DateService,
  ) {}

  ngOnInit(): void {}

  handleClick() {
    this.sleepQualityService.showHistoryModal({
      traineeId: this.traineeId,
      highlightedDate: this.dateService.getSimpleDate(this.date),
    });
  }
}
