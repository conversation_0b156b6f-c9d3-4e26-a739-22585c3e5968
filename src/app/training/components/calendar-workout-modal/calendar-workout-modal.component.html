<mpg-modal-layout
  [button]="modalButton"
  [title]="
    ('training.workout' | translate) +
    ' ' +
    scheduledWorkout.workout.orderNumber
  "
  >
  <ion-card-content class="light-background">
    @if (scheduledWorkout.date || scheduledWorkout.time) {
      <h2
        class="border-bottom ion-padding-bottom"
        >
        <ion-chip class="dark">{{ date }}</ion-chip>
      </h2>
    }
    <mpg-workout-table
      [isEditable]="false"
      [workout]="scheduledWorkout.workout"
    ></mpg-workout-table>
  </ion-card-content>
</mpg-modal-layout>
