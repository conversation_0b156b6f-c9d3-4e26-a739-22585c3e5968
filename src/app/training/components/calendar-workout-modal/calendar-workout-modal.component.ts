import { Component, Input, OnInit } from '@angular/core';
import { ScheduledWorkout } from '../../models';
import { Button } from '../../../shared/models';
import { DateService } from '../../../shared/services';
import { ScheduledRestDayService } from '../../services';

@Component({
  selector: 'mpg-calendar-workout-modal',
  templateUrl: './calendar-workout-modal.component.html',
  styleUrls: ['./calendar-workout-modal.component.scss'],
})
export class CalendarWorkoutModalComponent implements OnInit {
  @Input() scheduledWorkout: ScheduledWorkout;
  @Input() onRestDayCreated: () => void;
  modalButton: Button;

  constructor(
    private dateService: DateService,
    private scheduledRestDayService: ScheduledRestDayService,
  ) {}

  get date(): string {
    const date = this.dateService.getLocalizedMediumDate(
      this.scheduledWorkout.date,
    );

    if (!this.scheduledWorkout.time) {
      return date;
    }

    return `${date} - ${this.dateService.getLocalizedShortTime(
      this.scheduledWorkout.time,
    )}`;
  }

  ngOnInit(): void {
    this.modalButton = this.scheduledRestDayService.getMakeRestDayButton(
      this.scheduledWorkout,
      this.onRestDayCreated,
    );
  }
}
