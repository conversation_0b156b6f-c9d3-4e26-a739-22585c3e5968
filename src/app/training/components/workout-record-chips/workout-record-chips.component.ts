import { Component, Input, OnInit } from '@angular/core';
import { WorkoutRecord } from '../../models';
import { MealDiaryInfo } from '../../../nutrition/models';
import { DateService, RedirectionService } from '../../../shared/services';
import { SubscriptionPlanType } from 'src/app/payments/enumerations';

@Component({
  selector: 'mpg-workout-record-chips',
  templateUrl: './workout-record-chips.component.html',
  styleUrls: ['./workout-record-chips.component.scss'],
})
export class WorkoutRecordChipsComponent implements OnInit {
  @Input() workoutRecord: WorkoutRecord;
  @Input() color: 'light' | 'dark' = 'light';
  @Input() traineeId: string;
  @Input() mealDiaryInfo: MealDiaryInfo;

  totalPersonalRecordsCount = 0;

  SubscriptionPlanType = SubscriptionPlanType;

  showTimePassed = false;

  constructor(
    private redirectionService: RedirectionService,
    private dateService: DateService,
  ) {}

  ngOnInit() {
    this.totalPersonalRecordsCount = this.workoutRecord.exerciseRecords.reduce(
      (acc, exerciseRecord) => acc + exerciseRecord.personalRecordsCount,
      0,
    );
  }

  handleMealDiaryInfoClick() {
    this.redirectionService.fetchAndNavigate(
      '/trainee/tabs/nutrition',
      this.traineeId,
      {
        queryParams: {
          date: this.mealDiaryInfo.date,
        },
      },
    );
  }

  handleOnCreatineClick() {
    this.redirectionService.fetchAndNavigate(
      '/trainee/creatine-tracker',
      this.traineeId,
    );
  }

  handleBodyWeightClick() {
    this.redirectionService.fetchAndNavigate(
      '/trainee/tabs/weight',
      this.traineeId,
      {
        queryParams: {
          date: this.dateService.getSimpleDate(this.workoutRecord.startedOn),
        },
      },
    );
  }

  getMilliSecondsPassed() {
    return (
      (Math.round(new Date(this.workoutRecord.endedOn).getTime() / 1000) -
        Math.round(new Date(this.workoutRecord.startedOn).getTime() / 1000)) *
      1000
    );
  }
}
