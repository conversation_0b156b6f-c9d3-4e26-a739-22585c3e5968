<mpg-modal-layout-v2
  [title]="title || 'training.change-exercise' | translate"
  iconSrc="/assets/icon/menu-3.svg"
>
  <div
    class="flex-space-between margin-bottom-m p-sticky header z-100 filter-searchbar"
  >
    <ion-searchbar
      (ionInput)="handleSearch($event)"
      searchIcon="search-outline"
    ></ion-searchbar>
    <div (click)="handleFilter()" class="filter flex-centered">
      <ion-icon name="filter"></ion-icon>
    </div>
  </div>
  @for (exercise of filteredExercises; track exercise) {
  <div
    class="exercise flex-space-between ion-activatable overflow-hidden"
    (click)="handleSelect(exercise)"
  >
    <ion-ripple-effect></ion-ripple-effect>
    <ion-label
      >{{ "exercises." + exercise.id | translate: { fallback: exercise.name } }}
    </ion-label>
    <ion-icon name="swap-horizontal"></ion-icon>
  </div>
  }
</mpg-modal-layout-v2>
