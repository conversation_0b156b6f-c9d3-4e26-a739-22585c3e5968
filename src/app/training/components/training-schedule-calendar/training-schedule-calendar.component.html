<div class="legend flex-space-between">
  <div class="past flex-centered flex-grow margin-right-m">
    <div class="line"></div>
    <ion-label>{{ "past" | translate }}</ion-label>
  </div>
  <div class="planned flex-centered flex-grow margin-right-m">
    <div class="line"></div>
    <ion-label>{{ "planned" | translate }}</ion-label>
  </div>
  <div class="rest flex-centered flex-grow">
    <div class="line"></div>
    <ion-label>{{ "rest" | translate }}</ion-label>
  </div>
</div>
<div class="flex-centered p-relative">
  <ion-datetime
    #datetime
    (ionChange)="handleDateChange($event)"
    (touchend)="handleTouchEnd($event)"
    (touchstart)="handleTouchStart($event)"
    [firstDayOfWeek]="1"
    [value]="selectedDate"
    presentation="date"
  ></ion-datetime>
  @if (isLoading) {
  <div class="loading">
    <mpg-loading-spinner
      [fullScreen]="false"
      [isLoading]="true"
    ></mpg-loading-spinner>
  </div>
  }
</div>
