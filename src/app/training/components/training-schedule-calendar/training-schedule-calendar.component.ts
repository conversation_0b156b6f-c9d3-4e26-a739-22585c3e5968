import {
  AfterViewInit,
  Component,
  EventEmitter,
  OnDestroy,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { ScheduledRestDayService, WorkoutRecordService } from '../../services';
import { DateService } from '../../../shared/services';
import { parseInt } from 'lodash';
import { tap } from 'rxjs/operators';
import { combineLatestWith } from 'rxjs';

@Component({
  selector: 'mpg-training-schedule-calendar',
  templateUrl: './training-schedule-calendar.component.html',
  styleUrls: ['./training-schedule-calendar.component.scss'],
})
export class TrainingScheduleCalendarComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  @Output() onDateSelect = new EventEmitter<string>();
  @ViewChild('datetime', { static: true }) datetime!: any;

  observer!: MutationObserver;
  currentMonthYear: string | null = null;
  prevButtons: any;

  isLoading = true;
  restDates: string[];
  pastDates: string[];
  plannedDates: string[];

  selectedDate: string;
  private touchStartX: number;
  private touchStartMonthYear: string;
  private touchEndX: number;
  private loadingClearTimeout: any;

  constructor(
    private workoutRecordService: WorkoutRecordService,
    private scheduledRestDaysService: ScheduledRestDayService,
    private dateService: DateService,
  ) {}

  ngOnInit(): void {
    this.selectedDate = this.dateService.getCurrentDate();
    this.fetchHighlightedDates();
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.observeMonthChange();
    }, 100);
  }

  observeMonthChange() {
    const datetimeEl = this.datetime.el.shadowRoot;
    const header = datetimeEl?.querySelector('#toggle-wrapper');

    if (!header) {
      console.warn('Month header not found!');
      return;
    }

    const icon = header.querySelector('ion-icon');
    if (icon) {
      icon.style.display = 'none';
    }

    this.currentMonthYear = header.textContent?.trim() || '';
    this.observer = new MutationObserver(() => {
      const newMonthYear = header.textContent?.trim();
      if (newMonthYear && newMonthYear !== this.currentMonthYear) {
        this.currentMonthYear = newMonthYear;
        this.onMonthChange();
      }
    });

    this.observer.observe(header, {
      characterData: true,
      subtree: true,
      childList: true,
    });

    datetimeEl
      ?.querySelector('.calendar-next-prev')
      .addEventListener('click', () => {
        this.isLoading = true;
      });
  }

  onMonthChange() {
    setTimeout(() => this.applyAllStyles(), 100);
  }

  applyAllStyles() {
    if (this.prevButtons) {
      this.prevButtons.forEach((button: HTMLElement) => {
        button.style.borderBottom = 'none';
      });
    }

    const pastButtons = this.applyStyles(
      this.pastDates,
      'var(--color-warning-alert-yellow-900)',
    );
    const plannedButtons = this.applyStyles(
      this.plannedDates,
      'var(--color-accent-primary-red-800)',
    );
    const restButtons = this.applyStyles(
      this.restDates,
      'var(--color-accent-secondary-blue-600)',
    );

    this.prevButtons = [...pastButtons, ...plannedButtons, ...restButtons];

    this.isLoading = false;
  }

  applyStyles(dates: string[], color: string): any {
    if (!dates?.length) {
      return [];
    }

    const dateSelectors = dates
      .map((date) => {
        const [year, month, day] = date.split('-');
        const formattedDay = parseInt(day, 10);
        const formattedMonth = parseInt(month, 10);

        return `button.calendar-day[data-day="${formattedDay}"][data-month="${formattedMonth}"][data-year="${year}"]`;
      })
      .join(', ');

    if (!dateSelectors) {
      return [];
    }

    const buttons = this.datetime.el.shadowRoot.querySelectorAll(dateSelectors);

    buttons.forEach((button: HTMLElement) => {
      button.style.borderBottom = `1px solid ${color}`;
    });

    return buttons;
  }

  ngOnDestroy() {
    if (this.observer) {
      this.observer.disconnect();
    }
  }

  fetchHighlightedDates() {
    this.workoutRecordService
      .getSchedule()
      .pipe(
        tap(() => (this.isLoading = true)),
        combineLatestWith(
          this.scheduledRestDaysService.getSchedule(),
          this.workoutRecordService.getHistory({
            page: 1,
            size: 90,
            basicInfo: true,
          }),
        ),
        tap(() => (this.isLoading = false)),
      )
      .subscribe(([workoutsSchedule, restDaysSchedule, workoutsHistory]) => {
        this.restDates = restDaysSchedule.restDays.map((r) => r.restOn);
        this.plannedDates = workoutsSchedule.workouts.map((w) => w.date);
        this.pastDates = workoutsHistory.content.map((w) =>
          this.dateService.getSimpleDate(w.endedOn),
        );

        this.applyAllStyles();
      });
  }

  handleTouchStart(event: TouchEvent) {
    this.touchStartX = event.touches[0].clientX;
    this.touchStartMonthYear = this.currentMonthYear;
  }

  handleTouchEnd(event: TouchEvent) {
    this.touchEndX = event.changedTouches[0].clientX;
    this.detectSwipe();
  }

  handleDateChange(event: any) {
    this.onDateSelect.emit(event.detail.value);
  }

  private detectSwipe() {
    const swipeThreshold = 30;

    if (
      this.touchEndX < this.touchStartX - swipeThreshold ||
      this.touchEndX > this.touchStartX + swipeThreshold
    ) {
      this.isLoading = true;

      if (this.loadingClearTimeout) {
        clearTimeout(this.loadingClearTimeout);
      }

      this.loadingClearTimeout = setTimeout(() => {
        if (this.touchStartMonthYear === this.currentMonthYear) {
          this.isLoading = false;
        }
      }, 800);
    }
  }
}
