import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { TrainingScheduleCalendarComponent } from './training-schedule-calendar.component';
import { LoadingSpinnerComponentModule } from '../../../shared/components/loading-spinner/loading-spinner.module';
import { TranslateModule } from '@ngx-translate/core';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    LoadingSpinnerComponentModule,
    TranslateModule,
  ],
  declarations: [TrainingScheduleCalendarComponent],
  exports: [TrainingScheduleCalendarComponent],
})
export class TrainingScheduleCalendarComponentModule {}
