.legend {
  padding: 8px 16px;

  .past .line {
    background-color: var(--color-warning-alert-yellow-900);
  }

  .planned .line {
    background-color: var(--color-accent-primary-red-800);
  }

  .rest .line {
    background-color: var(--color-accent-secondary-blue-600);
  }

  .line {
    height: 1px;
    flex-grow: 1;
    width: 100%;
    margin-right: 8px;
  }

  ion-label {
    font-size: var(--typefaces-size-xl);
    color: var(--color-background-secondary-white-600);
  }
}

ion-datetime {
  --background: var(--color-background-primary-black-700);
  --background-rgb: rgb(10, 10, 10);
  --wheel-highlight-background: var(--color-background-primary-black-400);
  max-width: 100vw;
  width: 100%;

  &::part(calendar-day) {
    font-size: 20px;
    font-weight: 350;
    color: var(--color-background-secondary-white-600);
    border-radius: var(--radius-medium, 16px);
    background: var(--color-background-primary-black-500, #0F0F0F);
    margin-bottom: 8px;
  }

  &::part(today) {
    border: none;
    box-shadow: none;
  }

  &::part(active) {
    box-shadow: 0 0 0 1px rgba(var(--ion-color-light-rgb), 1);
    border: none;
  }

  &::part(wheel-item) {
    color: var(--color-background-secondary-white-600);
    box-shadow: none !important;
  }

  &::part(month-year-button) {
    color: var(--color-background-secondary-white-600);
  }

}

.loading {
  position: absolute;
  top: 80px;
  z-index: 100;
  height: calc(100% - 80px);
  width: 100%;
  background-color: #0A0A0A;
}
