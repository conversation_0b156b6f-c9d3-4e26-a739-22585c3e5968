import { Component, Input, OnInit } from '@angular/core';
import { <PERSON><PERSON> } from '../../../shared/models';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import {
  DateService,
  Locale,
  LocalizationService,
  ModalService,
  ToastService,
} from '../../../shared/services';
import { MPGValidators } from '../../../shared/validators/validators';
import { Observable } from 'rxjs';
import { CreatineRecord } from '../../models';
import { CreatineRecordService } from '../../services';
import { catchError } from 'rxjs/operators';

@Component({
  selector: 'mpg-creatine-record-modal',
  templateUrl: './creatine-record-modal.component.html',
  styleUrls: ['./creatine-record-modal.component.scss'],
})
export class CreatineRecordModalComponent implements OnInit {
  @Input() creatineRecord: CreatineRecord;

  creatineRecordFormGroup: FormGroup;
  locale$: Observable<Locale>;
  modalButton: Button = {
    label: 'buttons.submit',
    handler: () => this.handleSubmit(),
    disabled: () => this.creatineRecordFormGroup.invalid,
  };

  constructor(
    private fb: FormBuilder,
    private creatineRecordService: CreatineRecordService,
    private dateService: DateService,
    private localizationService: LocalizationService,
    private modalService: ModalService,
    private toastService: ToastService,
  ) {}

  ngOnInit() {
    this.locale$ = this.localizationService.locale$;

    this.creatineRecordFormGroup = this.fb.group(
      {
        startedOn: [
          this.creatineRecord?.startedOn || this.dateService.getCurrentDate(),
          Validators.required,
        ],
        endedOn: [
          this.creatineRecord?.endedOn ||
            this.dateService.getDatePlusMonths(
              this.dateService.getCurrentDate(),
              2,
            ),
          Validators.required,
        ],
      },
      {
        validators: MPGValidators.endDateAfterStartDate('endedOn', 'startedOn'),
      },
    );

    this.creatineRecordFormGroup.controls.startedOn.valueChanges.subscribe(
      (startedOn) => {
        this.creatineRecordFormGroup.controls.endedOn.setValue(
          this.dateService.getDatePlusMonths(startedOn, 2),
        );
      },
    );
  }

  handleSubmit() {
    const request = this.creatineRecordFormGroup.value;

    if (this.creatineRecord) {
      this.creatineRecordService
        .edit(this.creatineRecord.id, request)
        .pipe(catchError((err) => this.toastService.handleError(err)))
        .subscribe(() => {
          this.modalService.closeTopModal({
            ...this.creatineRecord,
            ...request,
          });
        });

      return;
    }

    this.creatineRecordService
      .create(request)
      .pipe(catchError((err) => this.toastService.handleError(err)))
      .subscribe((creatineRecord) => {
        this.modalService.closeTopModal(creatineRecord);
      });
  }
}
