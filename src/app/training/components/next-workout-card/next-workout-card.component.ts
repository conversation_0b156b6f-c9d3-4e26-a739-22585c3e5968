import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import {
  ScheduledWorkout,
  TrainingSplit,
  Workout,
  WorkoutExercise,
} from '../../models';
import { ModalService, ToastService } from '../../../shared/services';
import { Button } from '../../../shared/models';
import {
  ScheduledRestDayService,
  TrainingSplitService,
  WorkoutRecordService,
  WorkoutService,
} from '../../services';
import { switchMap } from 'rxjs';

@Component({
  selector: 'mpg-next-workout-card',
  templateUrl: './next-workout-card.component.html',
  styleUrls: ['./next-workout-card.component.scss'],
})
export class NextWorkoutCardComponent implements OnInit {
  @Input() workout: ScheduledWorkout;
  @Output() onLoadingChange = new EventEmitter<boolean>();

  showExercises = false;
  trainingSplit: TrainingSplit;

  constructor(
    private modalService: ModalService,
    private workoutService: WorkoutService,
    private scheduledRestDayService: ScheduledRestDayService,
    private workoutRecordService: WorkoutRecordService,
    private trainingSplitService: TrainingSplitService,
    private toastService: ToastService,
  ) {}

  get button(): Button {
    if (this.showExercises) {
      return {
        label: 'hide exercises',
        handler: () => {
          this.showExercises = false;
        },
        iconSrc: 'assets/icon/show.svg',
      };
    }

    return {
      label: 'show exercises',
      handler: () => {
        this.showExercises = true;
      },
      iconSrc: 'assets/icon/hide.svg',
    };
  }

  getOrderNumber(exercise: WorkoutExercise) {
    return this.workoutService.getOrderNumber(exercise);
  }

  ngOnInit() {}

  handleRestDay() {
    this.scheduledRestDayService.createRestDayModal(this.workout).subscribe();
  }

  handleStartWorkout() {
    this.onLoadingChange.emit(true);
    this.workoutRecordService
      .startWorkout(this.workout.workout)
      .subscribe(() => {
        this.onLoadingChange.emit(false);
      });
  }

  handleChangeWorkout() {
    this.trainingSplitService
      .getActive()
      .pipe(
        switchMap((trainingSplit) => {
          return this.modalService.createSimpleInput<Workout>({
            title: 'training.change-workout',
            icon: 'swap-horizontal',
            type: 'options',
            options: trainingSplit.workouts,
            optionFormatter: (option) => {
              return option.orderNumber + '. ' + option.name;
            },
          });
        }),
      )
      .subscribe((workout: Workout) => {
        this.workout.workout = workout;
        this.toastService.showInfoToast('training.change-workout-success');
      });
  }
}
