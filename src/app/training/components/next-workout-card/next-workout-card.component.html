<mpg-card-v2
  [button]="button"
  [subtitle]="
    (workout?.date | dayOfWeek | translate) +
    (workout?.time ? ' - ' + (workout?.time | timeFormat) : '')
  "
  [title]="
    ('training.workout' | translate) + ' ' + workout?.workout?.orderNumber
  "
  iconSrc="/assets/icon/treadmill.svg"
>
  <div class="controls flex-space-between">
    <ion-icon
      (click)="handleChangeWorkout()"
      class="change"
      name="swap-horizontal"
    ></ion-icon>
    <mpg-button (click)="handleRestDay()" [iconOnly]="true" color="dark">
      <ion-icon src="/assets/icon/rest.svg" style="font-size: 30px"></ion-icon>
    </mpg-button>
    <mpg-button (click)="handleStartWorkout()">
      <ion-icon name="play-sharp"></ion-icon>
    </mpg-button>
  </div>
  @if (showExercises) {
  <div class="exercises-container">
    @for (exercise of workout?.workout?.exercises; track exercise) {
    <div class="exercise-container">
      <ion-label class="order-number"
        >{{ getOrderNumber(exercise) }}.
      </ion-label>
      <ion-label
        >{{
          "exercises." + exercise.exercise.id
            | translate: { fallback: exercise.exercise.name }
        }}
      </ion-label>
    </div>
    }
  </div>
  }
</mpg-card-v2>
