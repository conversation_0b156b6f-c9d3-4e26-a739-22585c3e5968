<div class="w-full flex-centered">
  <div (click)="bedtimeModal.present()" class="time-container flex-start">
    <ion-icon src="/assets/icon/bedtime.svg"></ion-icon>
    <ion-datetime-button
      [ngClass]="{
        empty: !sleepQualityRecord?.bedtime
      }"
      color="dark"
      datetime="bedtime"
    ></ion-datetime-button>
  </div>
  <div (click)="wakeUpTimeModal.present()" class="time-container flex-start">
    <ion-icon src="/assets/icon/wake-up-time.svg"></ion-icon>
    <ion-datetime-button
      [ngClass]="{
        empty: !sleepQualityRecord?.wakeUpTime
      }"
      color="dark"
      datetime="wake-up-time"
    ></ion-datetime-button>
  </div>
</div>

<form [formGroup]="sleepQualityFormGroup">
  <ion-modal #bedtimeModal [keepContentsMounted]="true">
    <ng-template>
      <ion-datetime
        [minuteValues]="[0, 15, 30, 45]"
        [showClearButton]="false"
        [showDefaultButtons]="false"
        formControlName="bedtime"
        id="bedtime"
        presentation="time"
      ></ion-datetime>
    </ng-template>
  </ion-modal>
  <ion-modal #wakeUpTimeModal [keepContentsMounted]="true">
    <ng-template>
      <ion-datetime
        [minuteValues]="[0, 15, 30, 45]"
        [showClearButton]="false"
        [showDefaultButtons]="false"
        formControlName="wakeUpTime"
        id="wake-up-time"
        presentation="time"
      ></ion-datetime>
    </ng-template>
  </ion-modal>
</form>
