import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { SleepQualityRecord, SleepQualityRecordPutRequest } from '../../models';
import { DateService, LocalizationService } from '../../../shared/services';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Observable } from 'rxjs';

@Component({
  selector: 'mpg-sleep-quality-time-form',
  templateUrl: './sleep-quality-time-form.component.html',
  styleUrls: ['./sleep-quality-time-form.component.scss'],
})
export class SleepQualityTimeFormComponent implements OnInit {
  @Input() sleepQualityRecord: SleepQualityRecord;
  @Output() onUpdate = new EventEmitter<SleepQualityRecordPutRequest>();
  sleepQualityFormGroup: FormGroup;
  @Input() isLoading = false;

  locale$: Observable<string>;

  constructor(
    private localizationService: LocalizationService,
    private fb: FormBuilder,
    private dateService: DateService,
  ) {}

  ngOnInit(): void {
    this.locale$ = this.localizationService.localeHourCycle23$;

    this.sleepQualityFormGroup = this.fb.group({
      bedtime: [
        this.dateService.trimTime(this.sleepQualityRecord?.bedtime) ||
          '00:00:00',
      ],
      wakeUpTime: [
        this.dateService.trimTime(this.sleepQualityRecord?.wakeUpTime) ||
          '00:00:00',
      ],
    });

    this.sleepQualityFormGroup.controls.bedtime.valueChanges.subscribe(
      (bedtime) => {
        if (!bedtime) {
          this.sleepQualityFormGroup.controls.bedtime.setValue('00:00:00', {
            emitEvent: false,
            onlySelf: true,
          });
        }
        this.onUpdate.emit({
          ...this.sleepQualityRecord,
          bedtime,
        });
      },
    );
    this.sleepQualityFormGroup.controls.wakeUpTime.valueChanges.subscribe(
      (wakeUpTime) => {
        if (!wakeUpTime) {
          this.sleepQualityFormGroup.controls.wakeUpTime.setValue('00:00:00', {
            emitEvent: false,
            onlySelf: true,
          });
        }
        this.onUpdate.emit({
          ...this.sleepQualityRecord,
          wakeUpTime,
        });
      },
    );
  }
}
