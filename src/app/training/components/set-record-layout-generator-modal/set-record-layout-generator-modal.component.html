@if (exerciseRecord) {
  <mpg-modal-layout
    [popoverMenuIconButton]="popoverMenuIconButton"
  [title]="
    'exercises.' + exerciseRecord.exercise.exercise.id
      | translate: { fallback: exerciseRecord.exercise.exercise.name }
  "
    >
    <ion-item>
      <ion-select
        [(ngModel)]="selectedSetText"
        [mode]="mode"
        class="ion-text-center"
        interface="popover"
        labelPlacement="floating"
        >
        @for (
          setText of setTextOptions; track
          setText; let index = $index; let last = $last) {
          <ion-select-option
            [value]="setText"
            >{{ last ? "" : index + 1 + "." }} {{ setText.text }}
          </ion-select-option>
        }
      </ion-select>
      <ion-select
        [(ngModel)]="selectedDetails"
        [mode]="mode"
        [multiple]="true"
        [selectedText]="'details' | translate"
        class="ion-text-center"
        interface="popover"
        labelPlacement="floating"
        >
        <ion-select-option value="bw">
          {{ "weight.body-weight" | translate }}
        </ion-select-option>
        <ion-select-option value="sleep">
          {{ "sleep-quality.label" | translate }}
        </ion-select-option>
        <ion-select-option value="pr">PR</ion-select-option>
      </ion-select>
    </ion-item>
    <div class="flex-centered container">
      <div #layout class="layout">
        <img alt="logo" class="logo" src="/assets/icon/favicon.png" />
        @if (selectedDetails.includes('pr')) {
          <ion-chip class="pr gold">
            <ion-icon color="light" name="trophy"></ion-icon>
            <ion-label class="bold" color="light">PR</ion-label>
          </ion-chip>
        }
        <div class="exercise bold">
          {{
          "exercises." + exerciseRecord.exercise.exercise.id
          | translate: { fallback: exerciseRecord.exercise.exercise.name }
          }}
        </div>
        <div class="set flex-centered flex-wrap">
          <ion-chip
          [ngClass]="{
            large: onlySetIsShowed
          }"
            class="light"
            >
            <ion-icon color="dark" name="barbell-sharp"></ion-icon>
            <ion-label>
              {{ selectedSetText.text }}
            </ion-label>
          </ion-chip>
          @if (
            exerciseRecord.bodyWeightInKg && selectedDetails.includes('bw')
            ) {
            <ion-chip
              class="light"
              >
              <ion-icon color="dark" name="scale"></ion-icon>
              <ion-label>{{ exerciseRecord.bodyWeightInKg }}</ion-label>
            </ion-chip>
          }
          @if (
            exerciseRecord.sleepQuality && selectedDetails.includes('sleep')
            ) {
            <ion-chip
              class="light"
              >
              <ion-icon color="dark" src="/assets/icon/sleep.svg"></ion-icon>
              <ion-label>{{ exerciseRecord.sleepQuality }}%</ion-label>
            </ion-chip>
          }
        </div>
      </div>
    </div>
  </mpg-modal-layout>
}
