.container {
  margin-top: 40px;

  .layout {
    position: relative;
    border-radius: 20px;
    background-color: var(--ion-color-dark);
    width: 300px;

    .logo {
      position: absolute;
      top: -25px;
      left: -20px;
      height: 45px;
      width: 45px;
      z-index: 10;
    }

    .pr {
      position: absolute;
      top: -15px;
      right: -30px;
      z-index: 10;
    }

    .exercise {
      background-color: white;
      border-radius: 0 19px 0 20px;
      padding: 5px 0;
    }

    .set {
      padding: 1rem 0;
      font-weight: bold;
      row-gap: 10px;

      ion-text {
        font-size: 1rem;
      }
    }
  }
}

.large {
  font-size: 1.3rem;

  ion-icon {
    font-size: 1.5rem;
  }
}

