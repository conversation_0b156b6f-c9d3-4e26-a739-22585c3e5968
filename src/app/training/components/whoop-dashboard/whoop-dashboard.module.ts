import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { WhoopDashboardComponent } from './whoop-dashboard.component';
import { TranslateModule } from '@ngx-translate/core';
import { WhoopChartModule } from '../whoop-chart/whoop-chart.module';
import { ScreenshotButtonModule } from '../../../shared/components/screenshot-button/screenshot-button.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ReactiveFormsModule,
    TranslateModule,
    WhoopChartModule,
    ScreenshotButtonModule,
  ],
  declarations: [WhoopDashboardComponent],
  exports: [WhoopDashboardComponent],
})
export class WhoopDashboardModule {}
