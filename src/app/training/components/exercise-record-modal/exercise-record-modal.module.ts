import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { ExerciseRecordModalComponent } from './exercise-record-modal.component';
import { ModalLayoutModule } from '../../../shared/components/modal-layout/modal-layout.module';
import { MaxLengthDirectiveModule } from '../../../shared/directives/max-length/max-length-directive.module';
import { TranslateModule } from '@ngx-translate/core';
import { ExerciseRecordFormModule } from '../exercise-record-form/exercise-record-form.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ReactiveFormsModule,
    ModalLayoutModule,
    MaxLengthDirectiveModule,
    TranslateModule,
    ExerciseRecordFormModule,
  ],
  declarations: [ExerciseRecordModalComponent],
  exports: [ExerciseRecordModalComponent],
})
export class ExerciseRecordModalModule {}
