@for (entity of bodyMeasurementEntities; track entity) {
  @if (bodyMeasurementRecord[entity.prop]) {
    <ion-item
      class="ion-no-padding ion-text-center"
      >
      <ion-col size="8">
        <ion-text color="dark"
          ><h2>{{ entity.label | translate }}</h2>
        </ion-text>
      </ion-col>
      <ion-col size="4">
        <ion-chip class="dark">
          <ion-label
            >{{ bodyMeasurementRecord[entity.prop] }} {{ "cm" | translate }}
          </ion-label>
        </ion-chip>
      </ion-col>
    </ion-item>
  }
}
