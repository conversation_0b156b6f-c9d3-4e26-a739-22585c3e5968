@if (!workout.exercises?.length) {
  <div>
    <ion-text class="ion-margin-bottom ion-padding" color="dark"
      ><h2>{{ "training.no-exercises-added" | translate }}</h2></ion-text
      >
    </div>
  }
  @if (workout.exercises?.length) {
    <div>
      <ion-row class="border-bottom">
        <ion-col class="ion-padding" size="2">
          <ion-text color="dark"><h2>№</h2></ion-text>
        </ion-col>
        <ion-col class="ion-padding" size="4">
          <ion-text color="dark"
            ><h2>{{ "training.exercise" | translate }}</h2></ion-text
            >
          </ion-col>
          <ion-col class="ion-padding" size="3">
            <ion-text color="dark"
              ><h2>{{ "training.reps" | translate }}</h2></ion-text
              >
            </ion-col>
            <ion-col class="ion-padding" size="3">
              <ion-text color="dark"
                ><h2>{{ "training.sets" | translate }}</h2></ion-text
                >
              </ion-col>
            </ion-row>
            @for (
              exercise of workout.exercises; track
              exercise; let last = $last; let index = $index) {
              <ion-row
    [ngClass]="{
      'border-bottom':
        !last &&
        !(
          workout.exercises[index + 1] &&
          workout.exercises[index + 1].mainOrderNumber ===
            exercise.mainOrderNumber
        )
    }"
                >
                <ion-item-sliding #itemSliding>
                  <ion-item class="ion-no-padding ion-text-center">
                    <ion-col size="2">
                      <ion-text color="dark"
                        ><h2>
                        {{ getOrderNumber(exercise) }}
                      </h2>
                    </ion-text>
                  </ion-col>
                  <ion-col size="4">
                    <ion-text color="dark"
                      ><h2>
                      {{
                      "exercises." + exercise.exercise.id
                      | translate: { fallback: exercise.exercise.name }
                      }}
                    </h2>
                  </ion-text>
                </ion-col>
                <ion-col size="3">
                  <ion-text color="dark"
                    ><h2>
                    {{ getRepsTarget(exercise) }}
                  </h2>
                </ion-text>
              </ion-col>
              <ion-col size="3">
                <ion-text color="dark"
                  ><h2>{{ exercise.sets }}</h2></ion-text
                  >
                </ion-col>
              </ion-item>
              <ion-item-options>
                <ion-item-option
          (click)="
            handleExercisePopoverMenu($event, itemSliding, exercise, index)
          "
                  [class.transparent]="transparent"
                  >
                  <ion-icon
                    color="dark"
                    name="ellipsis-vertical"
                    slot="icon-only"
                  ></ion-icon>
                </ion-item-option>
              </ion-item-options>
            </ion-item-sliding>
          </ion-row>
        }
      </div>
    }
