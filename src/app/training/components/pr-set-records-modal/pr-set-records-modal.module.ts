import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { PRSetRecordsModalComponent } from './pr-set-records-modal.component';
import { ModalLayoutModule } from '../../../shared/components/modal-layout/modal-layout.module';
import { WorkoutTableModule } from '../workout-table/workout-table.module';
import { CardComponentModule } from '../../../shared/components/card/card.module';
import { TranslateModule } from '@ngx-translate/core';
import { TrainingVolumeTableModule } from '../training-volume-table/training-volume-table.module';
import { DataHistoryLimitItemModule } from '../../../payments/components/data-history-limit-item/data-history-limit-item.module';
import { ModalLayoutV2Module } from '../../../shared/components/modal-layout-v2/modal-layout-v2.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ModalLayoutModule,
    WorkoutTableModule,
    CardComponentModule,
    TranslateModule,
    TrainingVolumeTableModule,
    DataHistoryLimitItemModule,
    ModalLayoutV2Module,
  ],
  declarations: [PRSetRecordsModalComponent],
  exports: [PRSetRecordsModalComponent],
})
export class PRSetRecordsModalModule {}
