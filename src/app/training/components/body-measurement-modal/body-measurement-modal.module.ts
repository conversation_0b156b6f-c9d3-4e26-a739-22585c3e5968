import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { BodyMeasurementModalComponent } from './body-measurement-modal.component';
import { ModalLayoutModule } from '../../../shared/components/modal-layout/modal-layout.module';
import { TranslateModule } from '@ngx-translate/core';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ReactiveFormsModule,
    ModalLayoutModule,
    TranslateModule,
  ],
  declarations: [BodyMeasurementModalComponent],
  exports: [BodyMeasurementModalComponent],
})
export class BodyMeasurementModalModule {}
