import { Component, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import {
  BodyMeasurementRecord,
  BodyMeasurementRecordPutRequest,
} from '../../models';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import {
  DateService,
  Locale,
  LocalizationService,
  ModalService,
} from '../../../shared/services';
import { BodyMeasurementService } from '../../services';
import { Button } from '../../../shared/models';
import { Observable, switchMap } from 'rxjs';
import { MPGValidators } from '../../../shared/validators/validators';

@Component({
  selector: 'mpg-body-measurement-modal',
  templateUrl: './body-measurement-modal.component.html',
  styleUrls: ['./body-measurement-modal.component.scss'],
})
export class BodyMeasurementModalComponent implements OnInit {
  @Input() date: string;
  @Input() traineeId: string;

  bodyMeasurementFormGroup: FormGroup;
  locale$: Observable<Locale>;
  modalButton: Button = {
    label: 'buttons.submit',
    handler: () => this.handleSubmit(),
    disabled: () => this.bodyMeasurementFormGroup?.invalid,
  };

  constructor(
    private router: Router,
    private fb: FormBuilder,
    private dateService: DateService,
    private bodyMeasurementService: BodyMeasurementService,
    private modalService: ModalService,
    private localizationService: LocalizationService,
  ) {}

  ngOnInit() {
    this.bodyMeasurementFormGroup = this.fb.group(
      {
        date: [null, [Validators.required]],
        chestInCm: [null, [Validators.min(1)]],
        bicepsInCm: [null, [Validators.min(1)]],
        legInCm: [null, [Validators.min(1)]],
        waistInCm: [null, [Validators.min(1)]],
        hipsInCm: [null, [Validators.min(1)]],
      },
      { validators: MPGValidators.atLeastTruthyValues(2) },
    );

    this.locale$ = this.localizationService.locale$;

    this.bodyMeasurementFormGroup.controls.date.valueChanges
      .pipe(
        switchMap((date) => {
          return this.bodyMeasurementService.getRecord(date);
        }),
      )
      .subscribe((bodyMeasurementRecord: BodyMeasurementRecord) => {
        this.bodyMeasurementFormGroup.controls.chestInCm.setValue(
          bodyMeasurementRecord?.chestInCm,
        );
        this.bodyMeasurementFormGroup.controls.bicepsInCm.setValue(
          bodyMeasurementRecord?.bicepsInCm,
        );
        this.bodyMeasurementFormGroup.controls.legInCm.setValue(
          bodyMeasurementRecord?.legInCm,
        );
        this.bodyMeasurementFormGroup.controls.waistInCm.setValue(
          bodyMeasurementRecord?.waistInCm,
        );
        this.bodyMeasurementFormGroup.controls.hipsInCm.setValue(
          bodyMeasurementRecord?.hipsInCm,
        );
      });

    this.bodyMeasurementFormGroup.controls.date.setValue(
      this.date || this.dateService.getCurrentDate(),
    );
  }

  handleSubmit() {
    if (this.bodyMeasurementFormGroup.invalid) {
      return;
    }

    const formValue: BodyMeasurementRecordPutRequest =
      this.bodyMeasurementFormGroup.value;
    this.bodyMeasurementService
      .putRecord(formValue)
      .subscribe((bodyMeasurementRecord) => {
        this.modalService.closeTopModal(bodyMeasurementRecord);
      });
  }
}
