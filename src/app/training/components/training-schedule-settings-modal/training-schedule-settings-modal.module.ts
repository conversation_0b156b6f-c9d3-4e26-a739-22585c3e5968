import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { TrainingScheduleSettingsModalComponent } from './training-schedule-settings-modal.component';
import { ModalLayoutV2Module } from '../../../shared/components/modal-layout-v2/modal-layout-v2.module';
import { TranslateModule } from '@ngx-translate/core';
import { LoadingSpinnerComponentModule } from '../../../shared/components/loading-spinner/loading-spinner.module';
import { ButtonModule } from '../../../shared/components/button/button.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ModalLayoutV2Module,
    TranslateModule,
    ReactiveFormsModule,
    LoadingSpinnerComponentModule,
    ButtonModule,
  ],
  declarations: [TrainingScheduleSettingsModalComponent],
  exports: [TrainingScheduleSettingsModalComponent],
})
export class TrainingScheduleSettingsModalModule {}
