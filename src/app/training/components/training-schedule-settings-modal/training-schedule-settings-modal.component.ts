import { Component, OnInit } from '@angular/core';
import {
  TrainingSchedule,
  TrainingScheduleRequest,
  TrainingTimeSchedule,
  TrainingTimeScheduleRequest,
} from '../../models';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Button } from '../../../shared/models';
import {
  TrainingScheduleService,
  TrainingTimeScheduleService,
} from '../../services';
import { ModalService } from '../../../shared/services';
import { MPGValidators } from '../../../shared/validators/validators';
import { TrainingScheduleType } from '../../enumerations';
import { combineLatestWith, Observable, of } from 'rxjs';

@Component({
  selector: 'mpg-training-schedule-settings-modal',
  templateUrl: './training-schedule-settings-modal.component.html',
  styleUrls: ['./training-schedule-settings-modal.component.scss'],
})
export class TrainingScheduleSettingsModalComponent implements OnInit {
  trainingSchedule: TrainingSchedule;
  trainingTimeSchedule: TrainingTimeSchedule;

  TrainingScheduleType = TrainingScheduleType;
  patternScheduleFormGroup: FormGroup;
  daysOfTheWeekScheduleFormGroup: FormGroup;
  createTrainingScheduleFormGroup: FormGroup;
  isPattern = false;
  createTrainingTimeScheduleFormGroup: FormGroup;
  daysOfTheWeek = [
    'monday',
    'tuesday',
    'wednesday',
    'thursday',
    'friday',
    'saturday',
    'sunday',
  ];
  isLoading = false;
  modalButton: Button = {
    label: 'buttons.done',
    handler: () => this.handleSubmit(),
    disabled: () => {
      return (
        this.createTrainingScheduleFormGroup.invalid ||
        (this.createTrainingScheduleFormGroup.pristine &&
          this.createTrainingTimeScheduleFormGroup.pristine) ||
        this.isLoading
      );
    },
  };

  constructor(
    private fb: FormBuilder,
    private trainingScheduleService: TrainingScheduleService,
    private trainingTimeScheduleService: TrainingTimeScheduleService,
    private modalService: ModalService,
  ) {}

  ngOnInit() {
    this.isLoading = true;
    this.trainingScheduleService
      .getActive()
      .pipe(combineLatestWith(this.trainingTimeScheduleService.getActive()))
      .subscribe(([trainingSchedule, trainingTimeSchedule]) => {
        this.trainingSchedule = trainingSchedule;
        this.trainingTimeSchedule = trainingTimeSchedule;

        this.buildForms();
        this.isLoading = false;
      });
  }

  handleSubmit() {
    if (this.createTrainingScheduleFormGroup.invalid) {
      return;
    }

    const formValue: TrainingScheduleRequest =
      this.createTrainingScheduleFormGroup.value;

    this.isLoading = true;
    this.trainingScheduleService
      .create(formValue)
      .pipe(combineLatestWith(this.getTrainingTimeScheduleObservable()))
      .subscribe((res) => {
        this.modalService.closeTopModal(res);
        this.isLoading = false;
      });
  }

  handleTrainingDay() {
    this.patternScheduleFormGroup.controls.pattern.setValue(
      this.patternScheduleFormGroup.value.pattern + '1',
    );
  }

  handleRestDay() {
    this.patternScheduleFormGroup.controls.pattern.setValue(
      this.patternScheduleFormGroup.value.pattern + '0',
    );
  }

  getPatternChars(): string[] {
    return this.patternScheduleFormGroup.value.pattern.split('');
  }

  handleDeletePatternChar(index: number) {
    const currentPattern = this.patternScheduleFormGroup.value.pattern;
    this.patternScheduleFormGroup.controls.pattern.setValue(
      currentPattern.substring(0, index) + currentPattern.substring(index + 1),
    );
  }

  handlePatternToggle(event: any) {
    this.enableTrainingScheduleTypeInForm(
      event.detail.checked
        ? TrainingScheduleType.PATTERN
        : TrainingScheduleType.DAYS_OF_THE_WEEK,
    );
  }

  private enableTrainingScheduleTypeInForm(
    type: TrainingScheduleType,
    markAsDirty = true,
  ) {
    if (type === TrainingScheduleType.PATTERN) {
      this.createTrainingScheduleFormGroup.controls.patternSchedule.enable();
      this.createTrainingScheduleFormGroup.controls.daysOfTheWeekSchedule.disable();
      this.isPattern = true;
    } else {
      this.createTrainingScheduleFormGroup.controls.daysOfTheWeekSchedule.enable();
      this.createTrainingScheduleFormGroup.controls.patternSchedule.disable();
      this.isPattern = false;
    }

    if (markAsDirty) {
      this.createTrainingScheduleFormGroup.markAsDirty();
    }

    this.createTrainingScheduleFormGroup.controls.type.setValue(type);
  }

  private buildForms() {
    this.patternScheduleFormGroup = this.fb.group({
      pattern: [
        this.trainingSchedule?.patternSchedule?.pattern || '1',
        [Validators.required, Validators.pattern(/^(1|([01]*0))$/)],
      ],
    });

    this.daysOfTheWeekScheduleFormGroup = this.fb.group(
      {
        monday: [
          this.trainingSchedule?.daysOfTheWeekSchedule?.monday || false,
          Validators.required,
        ],
        tuesday: [
          this.trainingSchedule?.daysOfTheWeekSchedule?.tuesday || false,
          Validators.required,
        ],
        wednesday: [
          this.trainingSchedule?.daysOfTheWeekSchedule?.wednesday || false,
          Validators.required,
        ],
        thursday: [
          this.trainingSchedule?.daysOfTheWeekSchedule?.thursday || false,
          Validators.required,
        ],
        friday: [
          this.trainingSchedule?.daysOfTheWeekSchedule?.friday || false,
          Validators.required,
        ],
        saturday: [
          this.trainingSchedule?.daysOfTheWeekSchedule?.saturday || false,
          Validators.required,
        ],
        sunday: [
          this.trainingSchedule?.daysOfTheWeekSchedule?.sunday || false,
          Validators.required,
        ],
      },
      { validators: MPGValidators.atLeastOneTrue },
    );

    this.createTrainingScheduleFormGroup = this.fb.group({
      type: [
        this.trainingSchedule?.type || TrainingScheduleType.DAYS_OF_THE_WEEK,
        Validators.required,
      ],
      patternSchedule: this.patternScheduleFormGroup,
      daysOfTheWeekSchedule: this.daysOfTheWeekScheduleFormGroup,
    });

    this.enableTrainingScheduleTypeInForm(
      this.createTrainingScheduleFormGroup.value.type,
      false,
    );

    this.createTrainingTimeScheduleFormGroup = this.fb.group(
      {
        monday: [this.trainingTimeSchedule?.monday],
        tuesday: [this.trainingTimeSchedule?.tuesday],
        wednesday: [this.trainingTimeSchedule?.wednesday],
        thursday: [this.trainingTimeSchedule?.thursday],
        friday: [this.trainingTimeSchedule?.friday],
        saturday: [this.trainingTimeSchedule?.saturday],
        sunday: [this.trainingTimeSchedule?.sunday],
      },
      { validators: MPGValidators.atLeastTruthyValues(1) },
    );
  }

  private getTrainingTimeScheduleObservable(): Observable<TrainingTimeSchedule | null> {
    if (this.createTrainingTimeScheduleFormGroup.invalid) {
      return of(null);
    }

    const trainingTimeRequest: TrainingTimeScheduleRequest =
      this.createTrainingTimeScheduleFormGroup.value;

    return this.trainingTimeScheduleService.create(trainingTimeRequest);
  }
}
