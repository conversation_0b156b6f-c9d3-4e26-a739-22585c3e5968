import { Component, OnInit } from '@angular/core';
import { TraineeService, TrainingSplitService } from '../../services';
import { switchMap } from 'rxjs';
import { TrainingSplit } from '../../models';
import { Button } from '../../../shared/models';
import { take } from 'rxjs/operators';

@Component({
  selector: 'mpg-training-splits-modal',
  templateUrl: './training-splits-modal.component.html',
  styleUrls: ['./training-splits-modal.component.scss'],
})
export class TrainingSplitsModalComponent implements OnInit {
  trainingSplits: TrainingSplit[];
  selectedSplit: TrainingSplit;

  constructor(
    private trainingSplitService: TrainingSplitService,
    private traineeService: TraineeService,
  ) {}

  get modalButton(): Button {
    if (!this.selectedSplit) {
      return null;
    }

    return {
      label: 'buttons.modify',
      iconSrc: '/assets/icon/modify.svg',
      handler: () => {
        this.trainingSplitService
          .createModifyModal(this.selectedSplit)
          .subscribe(() => {
            this.handleUpdate();
          });
      },
    };
  }

  ngOnInit() {
    this.traineeService.traineeId$
      .pipe(
        take(1),
        switchMap((traineeId) => {
          return this.trainingSplitService.getAll({ traineeId });
        }),
      )
      .subscribe((trainingSplits) => {
        this.trainingSplits = trainingSplits;
      });
  }

  handleSelect(event: MouseEvent, trainingSplit: TrainingSplit) {
    event.stopPropagation();
    this.selectedSplit = trainingSplit;
  }

  handleUpdate() {
    this.ngOnInit();
    this.selectedSplit = null;
  }

  handleCreate() {
    this.trainingSplitService
      .createCreationModal()
      .subscribe((trainingSplit) => {
        this.trainingSplits = this.trainingSplits.concat(trainingSplit);
        this.selectedSplit = trainingSplit;
      });
  }
}
