import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { TrainingSplitsModalComponent } from './training-splits-modal.component';
import { ModalLayoutV2Module } from '../../../shared/components/modal-layout-v2/modal-layout-v2.module';
import { TrainingSplitCardComponentModule } from '../training-split-card/training-split-card.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ModalLayoutV2Module,
    TrainingSplitCardComponentModule,
  ],
  declarations: [TrainingSplitsModalComponent],
  exports: [TrainingSplitsModalComponent],
})
export class TrainingSplitsModalModule {}
