import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { GenerateFeedbackModalComponent } from './generate-feedback-modal.component';
import { ModalLayoutModule } from '../../../shared/components/modal-layout/modal-layout.module';
import { WorkoutTableModule } from '../workout-table/workout-table.module';
import { CardComponentModule } from '../../../shared/components/card/card.module';
import { TranslateModule } from '@ngx-translate/core';
import { LoadingSpinnerComponentModule } from '../../../shared/components/loading-spinner/loading-spinner.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ModalLayoutModule,
    WorkoutTableModule,
    CardComponentModule,
    TranslateModule,
    LoadingSpinnerComponentModule,
  ],
  declarations: [GenerateFeedbackModalComponent],
  exports: [GenerateFeedbackModalComponent],
})
export class GenerateFeedbackModalModule {}
