import { Component, OnInit } from '@angular/core';
import { BehaviorSubject, switchMap } from 'rxjs';
import {
  AlertService,
  ModalService,
  ToastService,
} from '../../../shared/services';
import { CourseService, TraineeService } from '../../services';
import { CreateCourseModalComponent } from '../../components/create-course-modal/create-course-modal.component';
import { map, take, tap } from 'rxjs/operators';
import { Course } from '../../models';
import { ActivatedRoute } from '@angular/router';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';

interface FetchedCourse {
  course: Course;
  videos: Video[];
}

interface Video {
  id: string;
  name: string;
  embedUrl: SafeResourceUrl;
}

@Component({
  selector: 'mpg-courses',
  templateUrl: './courses.page.html',
  styleUrls: ['./courses.page.scss'],
})
export class CoursesPage implements OnInit {
  courses: Course[] = [];
  isLoading = false;
  fetchedCourses: FetchedCourse[];
  isTraineePanel = false;
  model: { traineeId?: string; trainerId?: string };
  private readonly searchSubject$ = new BehaviorSubject<string>(null);
  private watchedVideoIds: string[];

  constructor(
    private modalService: ModalService,
    private courseService: CourseService,
    private traineeService: TraineeService,
    private activatedRoute: ActivatedRoute,
    private sanitizer: DomSanitizer,
    private toastService: ToastService,
    private alertService: AlertService,
  ) {}

  handleRefresh(event: any) {
    this.ionViewDidEnter();
    event.target.complete();
  }

  ngOnInit() {}

  ionViewDidEnter() {
    this.activatedRoute.data
      .pipe(
        take(1),
        map((data) => data.isTraineePanel),
        tap((isTraineePanel) => {
          this.isTraineePanel = isTraineePanel;
        }),
        switchMap(() => {
          if (this.isTraineePanel) {
            return this.traineeService.traineeId$.pipe(
              map((traineeId) => {
                return { traineeId };
              }),
            );
          }

          return this.traineeService.trainerId$.pipe(
            map((trainerId) => {
              return { trainerId };
            }),
          );
        }),
        take(1),
        tap((model) => {
          this.model = model;
        }),
        switchMap((model) => {
          return this.courseService.getAll(model);
        }),
      )
      .subscribe((courses) => {
        this.courses = courses;
        this.buildFetchedCourses(courses);
      });

    this.courseService.getAllWatchedVideoIds().subscribe((watchedVideoIds) => {
      this.watchedVideoIds = watchedVideoIds;
    });
  }

  handleAddCourse() {
    this.modalService
      .create<Course>({
        component: CreateCourseModalComponent,
      })
      .subscribe((course) => {
        // this.handleSearchClear();
        this.courses = this.courses.concat(course);
      });
  }

  handleDeleteCourse(course: Course, itemSliding: any) {
    this.alertService.createDeleteAlert(() => {
      this.courseService.delete(course.id).subscribe(() => {
        this.courses = this.courses.filter((c) => c.id !== course.id);
        this.toastService.showInfoToast('courses.delete-success');
        itemSliding.close();
      });
    });
  }

  handleRefreshCourse(course: Course, itemSliding: any) {
    this.alertService.createConfirmAlert(
      'courses.refresh-course-confirm',
      () => {
        this.courseService.refreshCourse(course.id).subscribe(() => {
          this.toastService.showInfoToast('courses.refresh-course-success');
          itemSliding.close();
        });
      },
    );
  }

  handleSearch(event: any) {
    this.searchSubject$.next(event.target.value || null);
  }

  handleSearchClear() {
    this.searchSubject$.next(null);
  }

  getTellaEmbedUrl(videoId: string): SafeResourceUrl {
    return this.sanitizer.bypassSecurityTrustResourceUrl(
      `https://www.tella.tv/video/${videoId}/embed?b=0&title=0&a=0&loop=0&t=0&muted=0&wt=0`,
    );
  }

  isVideoWatched(id: string) {
    return this.watchedVideoIds.includes(id);
  }

  markAsWatched(id: string, courseName: string, videoName: string) {
    this.courseService
      .markAsWatched(id, {
        courseName,
        videoName,
      })
      .subscribe(() => {
        this.watchedVideoIds = this.watchedVideoIds.concat(id);
        this.toastService.showInfoToast('courses.mark-as-watched-success');
      });
  }

  markAsUnwatched(id: string) {
    this.courseService.markAsUnwatched(id).subscribe(() => {
      this.watchedVideoIds = this.watchedVideoIds.filter((v) => v !== id);
      this.toastService.showInfoToast('courses.mark-as-unwatched-success');
    });
  }

  private buildFetchedCourses(courses: Course[]) {
    this.isLoading = true;
    const tellaCourses = courses.filter((course) =>
      course.url.includes('tella'),
    );

    Promise.all(
      tellaCourses.map(async (course) => {
        const apiUrl = this.transformTellaUrlToApi(course.url);
        if (!apiUrl) {
          return null;
        }

        try {
          const response = await fetch(apiUrl);
          const data = await response.json();

          const videos: Video[] = data.stories
            .map((story: any) => ({
              id: story.id,
              name: story.name,
              embedUrl: this.getTellaEmbedUrl(story.id),
            }))
            .reverse();

          return { course, videos } as FetchedCourse;
        } catch (error) {
          console.error('Error fetching Tella stories:', error);
          return null;
        }
      }),
    ).then((fetchedCourses) => {
      this.fetchedCourses = fetchedCourses.filter(Boolean);
      this.isLoading = false;
    });
  }

  private transformTellaUrlToApi(url: string): string | null {
    const match = url.match(/tella\.tv\/playlist\/([^/]+)/);
    if (match && match[1]) {
      const courseId = match[1];
      return `https://www.tella.tv/api/channels/${courseId}/stories`;
    }

    return null;
  }
}
