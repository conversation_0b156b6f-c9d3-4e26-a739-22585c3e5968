import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { TrainingSplitDetailsPageRoutingModule } from './training-split-details-routing.module';

import { TrainingSplitDetailsPage } from './training-split-details.page';
import { WorkoutCardModule } from '../../components/workout-card/workout-card.module';
import { TranslateModule } from '@ngx-translate/core';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    TrainingSplitDetailsPageRoutingModule,
    WorkoutCardModule,
    TranslateModule,
  ],
  declarations: [TrainingSplitDetailsPage],
})
export class TrainingSplitDetailsPageModule {}
