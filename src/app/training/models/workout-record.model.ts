import { Workout, WorkoutExercise, WorkoutExerciseInfo, WorkoutInfo } from './workout.model';
import { IdentifiableDate, Note, SmartNote } from '../../shared/models';
import { TrainingVolume } from './exercise.model';
import { Gym } from './gym.model';

export interface WorkoutRecordCreateRequest {
  workoutId: string;
}

export interface WorkoutRecordEditRequest {
  startedOn?: string;
  endedOn?: string;
  gymId?: string;
}

export interface WorkoutRecord {
  id: string;
  exerciseRecords: WorkoutExerciseRecord[];
  workout: Workout;
  startedOn: string;
  endedOn: string;
  createdOn: string;
  trainingVolume: TrainingVolume;
  bodyWeightInKg?: number;
  notes: Note[];
  sleepQuality?: number;
  gym?: Gym;
  onCreatine?: boolean;
}

export interface WorkoutRecordProgress {
  totalSets: number;
  completedSets: number;
  progress: number;
}

export interface WorkoutExerciseRecord {
  id: string;
  history?: WorkoutExerciseHistory;
  setRecords: WorkoutExerciseSetRecord[];
  exercise: WorkoutExercise;
  setRecordsAsText: string;
  score: number;
  workoutRecord: IdentifiableDate;
  bodyWeightInKg?: number;
  sleepQuality?: number;
  isSkipped: boolean;
  notes: Note[];
  personalRecordsCount: number;
}

export interface WorkoutExerciseRecordInfo {
  id: string;
  exercise: WorkoutExerciseInfo;
  workoutRecord: IdentifiableDate;
}

export interface WorkoutExerciseRecordEditRequest {
  exerciseId: string;
}

export interface GetAllWorkoutExerciseRecordsRequest {
  exerciseId?: string;
  workoutExerciseId?: string;
  page: number;
  gymId: string;
}

export interface WorkoutExerciseSetRecord {
  id: string;
  orderNumber: number;
  repsTarget: number;
  weightInKgTarget: number;
  reps: number;
  weightInKg: number;
  exerciseRecord: WorkoutExerciseRecordInfo;
  bodyWeightInKg?: number;
  sleepQuality?: number;
  video?: WorkoutExerciseSetRecordVideo;
  isPersonalRecord: boolean;
  smartNotes: SmartNote[];
}

export interface WorkoutExerciseSetRecordVideo {
  id: string;
  url: string;
  notes: Note[];
}

export interface WorkoutExerciseSetRecordVideoRequest {
  videoId: string;
}

export interface WorkoutExerciseSetRecordEditRequest {
  reps: number;
  weightInKg: number;
}

export interface ScheduledWorkout {
  date?: string;
  time?: string;
  workout: Workout;
}

export interface ScheduledWorkoutInfo {
  date?: string;
  time?: string;
  workout: WorkoutInfo;
}

export interface WorkoutsSchedule {
  workouts: ScheduledWorkout[];
}

export interface WorkoutExerciseHistory {
  latestRecordForExercise: WorkoutRecordHistory;
  latestRecordForWorkoutExercise: WorkoutRecordHistory;
  prRecordForExercise: WorkoutRecordHistory;
}

export interface WorkoutRecordHistory {
  setRecords: WorkoutExerciseSetRecord[];
  setRecordsAsText: string;
  workoutRecord: WorkoutRecordInfo;
  notes: Note[];
  hasVideo: boolean;
  isSkipped: boolean;
}

export interface WorkoutRecordInfo {
  id: string;
  workout: WorkoutInfo;
  startedOn: string;
  endedOn: string;
}

export interface WorkoutExerciseSetRecordEditRequest {
  id: string;
  reps: number;
  weightInKg: number;
}
