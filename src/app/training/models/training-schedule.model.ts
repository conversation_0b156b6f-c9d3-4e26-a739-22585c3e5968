import { TrainingScheduleType } from '../enumerations/training-schedule-type.enum';

export interface TrainingScheduleRequest {
  type: TrainingScheduleType;
  patternSchedule?: PatternTrainingScheduleRequest;
  daysOfTheWeekSchedule?: DaysOfTheWeekTrainingScheduleRequest;
  traineeId: string;
}

export interface PatternTrainingScheduleRequest {
  pattern: string;
}

export interface DaysOfTheWeekTrainingScheduleRequest {
  monday: boolean;
  tuesday: boolean;
  wednesday: boolean;
  thursday: boolean;
  friday: boolean;
  saturday: boolean;
  sunday: boolean;
}

export interface TrainingSchedule {
  type: TrainingScheduleType;
  patternSchedule?: PatternTrainingSchedule;
  daysOfTheWeekSchedule?: DaysOfTheWeekTrainingSchedule;
}

export interface PatternTrainingSchedule {
  pattern: string;
}

export interface DaysOfTheWeekTrainingSchedule {
  monday: boolean;
  tuesday: boolean;
  wednesday: boolean;
  thursday: boolean;
  friday: boolean;
  saturday: boolean;
  sunday: boolean;
}

export interface TrainingTimeScheduleRequest {
  monday: string;
  tuesday: string;
  wednesday: string;
  thursday: string;
  friday: string;
  saturday: string;
  sunday: string;
  traineeId: string;
}

export interface TrainingTimeSchedule {
  id: string;
  monday: string;
  tuesday: string;
  wednesday: string;
  thursday: string;
  friday: string;
  saturday: string;
  sunday: string;
}
