export interface Exercise {
  id: string;
  name: string;
  nameBg?: string;
  trainingVolume: TrainingVolume;
  videoUrl?: string;
}

export interface ExerciseInfo {
  id: string;
  name: string;
}

export interface ExerciseCreateRequest {
  name: string;
  trainingVolume: TrainingVolume;
}

export interface TrainingVolume {
  chest: number;
  frontDelts: number;
  lateralDelts: number;
  biceps: number;
  abs: number;
  upperTraps: number;
  rearDelts: number;
  triceps: number;
  midTraps: number;
  lowerTraps: number;
  lats: number;
  erectors: number;
  quads: number;
  calves: number;
  glutes: number;
  hamstrings: number;
  forearms: number;
}

export interface TrainingVolumeHistory {
  startDate: string;
  endDate: string;
  trainingVolume: TrainingVolume;
}

export type ExerciseMuscleGroupFilter = Partial<{
  [key in keyof TrainingVolume]: boolean;
}>;
