import { NutritionInfo } from '../../nutrition/models';

export interface DaySummary {
  date: string;
  bodyWeightInKg: number;
  sleepQuality: number;
  nutritionInfo: NutritionInfo;
  onCreatine: boolean;
  hasBodyMeasurements: boolean;
  hasBodyPhotos: boolean;
  events: DaySummaryEvent[];
}

export interface DaySummaryEvent {
  type: DaySummaryEventType;
  time: string;
  payload: any;
}

export enum DaySummaryEventType {
  WAKE_UP = 'WAKE_UP',
  WORKOUT = 'WORKOUT',
  MEAL = 'MEAL',
  SLEEP = 'SLEEP',
}
