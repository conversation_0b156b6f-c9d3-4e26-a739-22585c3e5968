import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, switchMap } from 'rxjs';
import { environment } from '../../../environments/environment';
import { TrainingSchedule, TrainingScheduleRequest } from '../models';
import { TraineeService } from './trainee.service';
import { take } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class TrainingScheduleService {
  static readonly BASE_URL = `${environment.TRAINING_SERVICE_API_URL}/v1/training-schedules`;

  constructor(
    private http: HttpClient,
    private traineeService: TraineeService,
  ) {}

  create(model: TrainingScheduleRequest): Observable<TrainingSchedule> {
    return this.traineeService.traineeId$.pipe(
      take(1),
      switchMap((traineeId) =>
        this.http.post<TrainingSchedule>(TrainingScheduleService.BASE_URL, {
          ...model,
          traineeId,
        }),
      ),
    );
  }

  getActive(): Observable<TrainingSchedule> {
    return this.traineeService.traineeId$.pipe(
      take(1),
      switchMap((traineeId) =>
        this.http.get<TrainingSchedule>(
          `${TrainingScheduleService.BASE_URL}/active?traineeId=${traineeId}`,
        ),
      ),
    );
  }

  // edit(id: string, model: TrainingSplitEditRequest): Observable<void> {
  //   return this.http.patch<void>(
  //     `${TrainingSplitService.BASE_URL}/${id}`,
  //     model
  //   );
  // }
}
