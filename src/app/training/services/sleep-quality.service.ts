import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, switchMap } from 'rxjs';
import { environment } from '../../../environments/environment';
import { SleepQualityRecord, SleepQualityRecordPutRequest } from '../models';
import { Page, Stats, StatsRequest } from '../../shared/models';
import { ModalService } from '../../shared/services';
import { SleepQualityHistoryModalComponent } from '../components/sleep-quality-history-modal/sleep-quality-history-modal.component';
import { TraineeService } from './trainee.service';
import { take } from 'rxjs/operators';
import { WhoopDashboardComponent } from '../components/whoop-dashboard/whoop-dashboard.component';

@Injectable({
  providedIn: 'root',
})
export class SleepQualityService {
  static readonly BASE_URL = `${environment.TRAINING_SERVICE_API_URL}/v1/sleep-quality-records`;

  constructor(
    private http: HttpClient,
    private modalService: ModalService,
    private traineeService: TraineeService,
  ) {}

  getRecord(): Observable<SleepQualityRecord> {
    return this.traineeService.traineeId$.pipe(
      take(1),
      switchMap((traineeId) => {
        return this.http.get<SleepQualityRecord>(SleepQualityService.BASE_URL, {
          params: new HttpParams().append('traineeId', traineeId),
        });
      }),
    );
  }

  getHistory({
    page = 1,
    traineeId,
    maxDate,
  }: {
    page: number;
    traineeId?: string;
    maxDate?: string;
  }): Observable<Page<SleepQualityRecord>> {
    let params = new HttpParams().append('page', page);
    if (maxDate) {
      params = params.append('maxDate', maxDate);
    }

    return this.traineeService.traineeId$.pipe(
      take(1),
      switchMap((currentTraineeId) => {
        return this.http.get<Page<SleepQualityRecord>>(
          `${SleepQualityService.BASE_URL}/history?traineeId=${
            traineeId || currentTraineeId
          }`,
          {
            params,
          },
        );
      }),
    );
  }

  getStats(model: StatsRequest): Observable<Stats<SleepQualityRecord>> {
    return this.traineeService.traineeId$.pipe(
      take(1),
      switchMap((traineeId) => {
        return this.http.get<Stats<SleepQualityRecord>>(
          `${SleepQualityService.BASE_URL}/stats`,
          {
            params: new HttpParams()
              .append('traineeId', traineeId)
              .append('endDate', model.endDate)
              .append('period', model.period),
          },
        );
      }),
    );
  }

  putRecord(
    request: SleepQualityRecordPutRequest,
  ): Observable<SleepQualityRecord> {
    return this.traineeService.traineeId$.pipe(
      take(1),
      switchMap((traineeId) => {
        return this.http.put<SleepQualityRecord>(
          SleepQualityService.BASE_URL,
          request,
          { params: new HttpParams().append('traineeId', traineeId) },
        );
      }),
    );
  }

  showHistoryModal({
    traineeId,
    highlightedDate,
  }: {
    traineeId?: string;
    highlightedDate?: string;
  } = {}) {
    this.modalService
      .create({
        component: SleepQualityHistoryModalComponent,
        componentProps: { traineeId, highlightedDate },
      })
      .subscribe();
  }

  showWhoopModal(sleepQualityRecord: SleepQualityRecord) {
    this.modalService
      .create({
        component: WhoopDashboardComponent,
        componentProps: { sleepQualityRecord, showHistoryButton: false },
      })
      .subscribe();
  }
}
