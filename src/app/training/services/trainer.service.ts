import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { Trainer } from '../models';

@Injectable({
  providedIn: 'root',
})
export class TrainerService {
  static readonly BASE_URL = `${environment.TRAINING_SERVICE_API_URL}/v1/trainers`;

  constructor(private http: HttpClient) {}

  getAll(): Observable<Trainer[]> {
    return this.http.get<Trainer[]>(TrainerService.BASE_URL);
  }
}
