import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, switchMap } from 'rxjs';
import { environment } from '../../../environments/environment';
import { WorkoutExercise, WorkoutExerciseSetRecord } from '../models';
import { TraineeService } from './trainee.service';
import { DateService, ModalService } from '../../shared/services';
import { PRSetRecordsModalComponent } from '../components/pr-set-records-modal/pr-set-records-modal.component';
import { take } from 'rxjs/operators';
import { SetRecordVideoModalComponent } from '../components/set-record-video-modal/set-record-video-modal.component';

@Injectable({
  providedIn: 'root',
})
export class WorkoutExerciseSetRecordService {
  static readonly BASE_URL = `${environment.TRAINING_SERVICE_API_URL}/v1/workout-exercise-set-records`;

  constructor(
    private http: HttpClient,
    private traineeService: TraineeService,
    private modalService: ModalService,
    private dateService: DateService,
  ) {}

  get(id: string, traineeId: string) {
    return this.traineeService.traineeId$.pipe(
      take(1),
      switchMap((currentTraineeId) => {
        return this.http.get<WorkoutExerciseSetRecord>(
          `${WorkoutExerciseSetRecordService.BASE_URL}/${id}?traineeId=${
            traineeId || currentTraineeId
          }`,
        );
      }),
    );
  }

  getPRSetRecords({
    exerciseId,
    traineeId,
    endDate,
  }: {
    exerciseId: string;
    traineeId?: string;
    endDate?: string;
  }): Observable<WorkoutExerciseSetRecord[]> {
    let params = new HttpParams().append('exerciseId', exerciseId);
    if (endDate) {
      params = params.append(
        'endDate',
        this.dateService.getSimpleDate(endDate),
      );
    }

    return this.traineeService.traineeId$.pipe(
      take(1),
      switchMap((currentTraineeId) =>
        this.http.get<WorkoutExerciseSetRecord[]>(
          `${WorkoutExerciseSetRecordService.BASE_URL}/pr?traineeId=${
            traineeId || currentTraineeId
          }`,
          { params },
        ),
      ),
    );
  }

  showPRSetRecordsModal({
    workoutExercise,
    traineeId,
    workoutExerciseRecordId,
    endDate,
  }: {
    workoutExercise: WorkoutExercise;
    traineeId?: string;
    workoutExerciseRecordId?: string;
    endDate?: string;
  }) {
    this.modalService
      .create({
        component: PRSetRecordsModalComponent,
        componentProps: {
          workoutExercise,
          traineeId,
          workoutExerciseRecordId,
          endDate,
        },
      })
      .subscribe();
  }

  fetchAndShowSetRecordVideoModal({
    traineeId,
    setRecordId,
    onDelete,
  }: {
    traineeId?: string;
    setRecordId: string;
    onDelete?: () => void;
  }) {
    this.get(setRecordId, traineeId).subscribe((setRecord) => {
      this.showSetRecordVideoModal(setRecord, onDelete);
    });
  }

  showSetRecordVideoModal(
    setRecord: WorkoutExerciseSetRecord,
    onDelete?: () => void,
  ) {
    this.modalService
      .create({
        component: SetRecordVideoModalComponent,
        componentProps: {
          setRecord,
          onDelete,
        },
      })
      .subscribe();
  }
}
