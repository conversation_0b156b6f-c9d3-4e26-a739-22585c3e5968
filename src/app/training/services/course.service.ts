import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { environment } from '../../../environments/environment';
import { TraineeService } from './trainee.service';
import { Observable, switchMap } from 'rxjs';
import {
  Course,
  CourseCreateRequest,
  CourseVideoWatchRequest,
} from '../models';
import { take } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class CourseService {
  static readonly BASE_URL = `${environment.TRAINING_SERVICE_API_URL}/v1/courses`;

  constructor(
    private http: HttpClient,
    private traineeService: TraineeService,
  ) {}

  getAll({
    traineeId,
    trainerId,
  }: {
    traineeId?: string;
    trainerId?: string;
  }): Observable<Course[]> {
    let params = new HttpParams();

    if (traineeId) {
      params = params.set('traineeId', traineeId);
    }
    if (trainerId) {
      params = params.set('trainerId', trainerId);
    }

    return this.http.get<Course[]>(CourseService.BASE_URL, { params });
  }

  create(course: CourseCreateRequest): Observable<Course> {
    return this.traineeService.trainerId$.pipe(
      take(1),
      switchMap((trainerId) => {
        return this.http.post<Course>(CourseService.BASE_URL, {
          ...course,
          trainerId,
        });
      }),
    );
  }

  refreshCourse(id: string): Observable<void> {
    return this.http.post<void>(
      `${CourseService.BASE_URL}/${id}/videos/refresh`,
      null,
    );
  }

  delete(id: string): Observable<void> {
    return this.http.delete<void>(`${CourseService.BASE_URL}/${id}`);
  }

  markAsWatched(
    videoId: string,
    model: CourseVideoWatchRequest,
  ): Observable<void> {
    return this.traineeService.traineeId$.pipe(
      take(1),
      switchMap((traineeId) => {
        const params = new HttpParams().set('traineeId', traineeId);

        return this.http.post<void>(
          `${CourseService.BASE_URL}/videos/${videoId}/watch`,
          model,
          { params },
        );
      }),
    );
  }

  markAsUnwatched(videoId: string): Observable<void> {
    return this.traineeService.traineeId$.pipe(
      take(1),
      switchMap((traineeId) => {
        const params = new HttpParams().set('traineeId', traineeId);

        return this.http.delete<void>(
          `${CourseService.BASE_URL}/videos/${videoId}/watch`,
          { params },
        );
      }),
    );
  }

  getAllWatchedVideoIds(): Observable<string[]> {
    return this.traineeService.traineeId$.pipe(
      take(1),
      switchMap((traineeId) => {
        const params = new HttpParams().set('traineeId', traineeId);

        return this.http.post<string[]>(
          `${CourseService.BASE_URL}/videos/watched/ids`,
          null,
          { params },
        );
      }),
    );
  }
}
