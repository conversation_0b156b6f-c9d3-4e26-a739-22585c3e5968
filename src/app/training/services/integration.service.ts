import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../../environments/environment';
import { Observable, switchMap } from 'rxjs';
import { Integration, WhoopIntegrationRequest } from '../models';
import { UserService } from '../../auth/services';
import { TraineeService } from './trainee.service';
import { take } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class IntegrationService {
  static readonly BASE_URL = `${environment.TRAINING_SERVICE_API_URL}/v1/integrations`;

  constructor(
    private http: HttpClient,
    private userService: UserService,
    private traineeService: TraineeService,
  ) {}

  createWhoopIntegration(model: WhoopIntegrationRequest): Observable<void> {
    return this.userService.loggedUserId$.pipe(
      take(1),
      switchMap((userId) => {
        return this.http.post<void>(
          `${IntegrationService.BASE_URL}/whoop?traineeId=${userId}`,
          model,
        );
      }),
    );
  }

  getAll(): Observable<Integration[]> {
    return this.traineeService.traineeId$.pipe(
      take(1),
      switchMap((traineeId) => {
        return this.http.get<Integration[]>(
          `${IntegrationService.BASE_URL}?traineeId=${traineeId}`,
        );
      }),
    );
  }
}
