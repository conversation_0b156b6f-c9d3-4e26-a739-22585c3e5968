import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CreateExerciseModalModule } from '../components/create-exercise-modal/create-exercise-modal.module';
import { CreateFitnessStateModalModule } from '../components/create-fitness-state-modal/create-fitness-state-modal.module';
import { CreateTraineeModalModule } from '../components/create-trainee-modal/create-trainee-modal.module';
import { WeightModalModule } from '../components/weight-modal/weight-modal.module';
import { CreateWorkoutModalModule } from '../components/create-workout-modal/create-workout-modal.module';
import { CreateWorkoutExerciseModalModule } from '../components/create-workout-exercise-modal/create-workout-exercise-modal.module';
import { CreateTrainingSplitModalModule } from '../components/create-training-split-modal/create-training-split-modal.module';
import { CreateTrainingScheduleModalModule } from '../components/create-training-schedule-modal/create-training-schedule-modal.module';
import { CalendarWorkoutModalModule } from '../components/calendar-workout-modal/calendar-workout-modal.module';
import { CalendarRestDayModalModule } from '../components/calendar-rest-day-modal/calendar-rest-day-modal.module';
import { CreateTrainingTimeScheduleModalModule } from '../components/create-training-time-schedule-modal/create-training-time-schedule-modal.module';
import { ExerciseRecordModalModule } from '../components/exercise-record-modal/exercise-record-modal.module';
import { ChangeWorkoutRecordDatetimeModalModule } from '../components/change-workout-record-datetime-modal/change-workout-record-datetime-modal.module';
import { TrainingVolumeModalModule } from '../components/training-volume-modal/training-volume-modal.module';
import { ExerciseRecordsHistoryModalModule } from '../components/exercise-records-history-modal/exercise-records-history-modal.module';
import { PRSetRecordsModalModule } from '../components/pr-set-records-modal/pr-set-records-modal.module';
import { WorkoutRecordModalModule } from '../components/workout-record-modal/workout-record-modal.module';
import { BodyPhotosModalModule } from '../components/body-photos-modal/body-photos-modal.module';
import { SleepQualityHistoryModalModule } from '../components/sleep-quality-history-modal/sleep-quality-history-modal.module';
import { SetRecordVideoModalModule } from '../components/set-record-video-modal/set-record-video-modal.module';
import { SetRecordLayoutGeneratorModalModule } from '../components/set-record-layout-generator-modal/set-record-layout-generator-modal.module';
import { BodyMeasurementModalModule } from '../components/body-measurement-modal/body-measurement-modal.module';
import { ExerciseModalModule } from '../components/exercise-modal/exercise-modal.module';
import { CreatineRecordModalModule } from '../components/creatine-record-modal/creatine-record-modal.module';
import { TraineeInvitationModalModule } from '../components/trainee-invitation-modal/trainee-invitation-modal.module';
import { TrainingVolumeHistoryModalModule } from '../components/training-volume-history-modal/training-volume-history-modal.module';
import { WeightProgressModalModule } from '../components/weight-progress-modal/weight-progress-modal.module';
import { GenerateFeedbackModalModule } from '../components/generate-feedback-modal/generate-feedback-modal.module';
import { CreateCourseModalModule } from '../components/create-course-modal/create-course-modal.module';
import { GymSelectorModalModule } from '../components/gym-selector-modal/gym-selector-modal.module';
import { ExerciseSelectorModalModule } from '../components/exercise-selector-modal/exercise-selector-modal.module';
import { ExerciseSelectorFiltersModalModule } from '../components/exercise-selector-filters-modal/exercise-selector-filters-modal.module';
import { TrainingScheduleModalModule } from '../components/training-schedule-modal/training-schedule-modal.module';
import { TrainingScheduleSettingsModalModule } from '../components/training-schedule-settings-modal/training-schedule-settings-modal.module';
import { TrainingSplitsModalModule } from '../components/training-splits-modal/training-splits-modal.module';
import { TrainingSplitModifyModalModule } from '../components/training-split-modify-modal/training-split-modify-modal.module';
import { ExerciseRecordSmartNotesModalModule } from '../components/exercise-record-smart-notes-modal/exercise-record-smart-notes-modal.module';

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    CreateExerciseModalModule,
    CreateFitnessStateModalModule,
    CreateTraineeModalModule,
    CreateFitnessStateModalModule,
    WeightModalModule,
    CreateWorkoutModalModule,
    CreateWorkoutExerciseModalModule,
    CreateTrainingSplitModalModule,
    CreateTrainingScheduleModalModule,
    CreateTrainingTimeScheduleModalModule,
    CalendarWorkoutModalModule,
    CalendarRestDayModalModule,
    ExerciseRecordModalModule,
    ChangeWorkoutRecordDatetimeModalModule,
    TrainingVolumeModalModule,
    ExerciseRecordsHistoryModalModule,
    PRSetRecordsModalModule,
    WorkoutRecordModalModule,
    BodyPhotosModalModule,
    SleepQualityHistoryModalModule,
    SetRecordVideoModalModule,
    SetRecordLayoutGeneratorModalModule,
    BodyMeasurementModalModule,
    ExerciseModalModule,
    CreatineRecordModalModule,
    TraineeInvitationModalModule,
    TrainingVolumeHistoryModalModule,
    WeightProgressModalModule,
    GenerateFeedbackModalModule,
    GymSelectorModalModule,
    ExerciseSelectorModalModule,
    ExerciseSelectorFiltersModalModule,
    TrainingScheduleModalModule,
    TrainingScheduleSettingsModalModule,
    TrainingSplitsModalModule,
    TrainingSplitModifyModalModule,
    CreateCourseModalModule,
    ExerciseRecordSmartNotesModalModule,
  ],
})
export class TrainingModalsModule {}
