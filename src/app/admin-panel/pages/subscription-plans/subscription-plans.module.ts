import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { SubscriptionPlansPageRoutingModule } from './subscription-plans-routing.module';
import { TranslateModule } from '@ngx-translate/core';
import { SubscriptionPlansPage } from './subscription-plans.page';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    SubscriptionPlansPageRoutingModule,
    ReactiveFormsModule,
    TranslateModule,
  ],
  declarations: [SubscriptionPlansPage],
})
export class SubscriptionPlansModule {}
