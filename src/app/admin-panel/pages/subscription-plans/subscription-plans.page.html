<ion-header>
  <ion-toolbar color="dark">
    <ion-buttons slot="start">
      <ion-menu-button></ion-menu-button>
    </ion-buttons>
    <ion-title>Subscription plans</ion-title>
  </ion-toolbar>
</ion-header>

<ion-fab #fab class="mb" horizontal="end" slot="fixed" vertical="bottom">
  <ion-fab-button>
    <ion-icon name="add"></ion-icon>
  </ion-fab-button>
  <ion-fab-list side="top">
    <ion-fab-button
      (click)="handleCreateSubscriptionPlan()"
      data-desc="Create subscription plan"
      >
      <ion-icon name="person-add"></ion-icon>
    </ion-fab-button>
  </ion-fab-list>
</ion-fab>
<ion-content (click)="fab.close()">
  <ion-row>
    <ion-col class="ion-no-padding" offset-lg="3" size="12" size-lg="6">
      <ion-list class="ion-no-padding">
        @for (subscriptionPlan of subscriptionPlans; track subscriptionPlan) {
          <ion-item>
            <ion-label class="ion-text-center">
              <h2>{{ subscriptionPlan.price }}</h2>
              <p>{{ subscriptionPlan.currency }}</p>
            </ion-label>
          </ion-item>
        }
      </ion-list>
    </ion-col>
  </ion-row>
</ion-content>
