import { Component, OnInit } from '@angular/core';
import { UserService } from '../../../auth/services';
import { IonItemSliding, ViewDidEnter } from '@ionic/angular';
import { BehaviorSubject, combineLatestWith, switchMap } from 'rxjs';
import { DetailedTrainee, Trainer } from '../../../training/models';
import { Page } from '../../../shared/models';
import {
  ModalService,
  PopoverService,
  RedirectionService,
} from '../../../shared/services';
import { TrainerService } from '../../../training/services/trainer.service';
import { TraineeService } from '../../../training/services';

@Component({
  selector: 'mpg-users',
  templateUrl: './users.page.html',
  styleUrls: ['./users.page.scss'],
})
export class UsersPage implements OnInit, ViewDidEnter {
  usersPage: Page<DetailedTrainee>;
  private searchSubject$ = new BehaviorSubject<string>('');
  private pageSubject$ = new BehaviorSubject<number>(1);

  constructor(
    private userService: UserService,
    private popoverService: PopoverService,
    private redirectionService: RedirectionService,
    private modalService: ModalService,
    private trainerService: TrainerService,
    private traineeService: TraineeService,
  ) {}

  ngOnInit() {}

  handleRefresh(event: any) {
    this.searchSubject$.next(this.searchSubject$.value);
    event.target.complete();
  }

  ionViewDidEnter() {
    this.searchSubject$
      .pipe(
        combineLatestWith(this.pageSubject$),
        switchMap(([search, page]) => this.userService.getAll(page, search)),
      )
      .subscribe((usersPage) => {
        if (!this.usersPage || usersPage.first) {
          this.usersPage = usersPage;
          return;
        }

        this.usersPage = {
          ...usersPage,
          content: this.usersPage.content.concat(usersPage.content),
        };
      });
  }

  handleSearch(event: any) {
    const search = event.detail.value;
    this.searchSubject$.next(search);
    this.pageSubject$.next(1);
  }

  handleInfiniteScroll(event: any) {
    this.pageSubject$.next(this.pageSubject$.value + 1);
    event.target.complete();
  }

  handlePopoverMenu(
    event: MouseEvent,
    itemSliding: IonItemSliding,
    user: DetailedTrainee,
  ) {
    return this.popoverService.handlePopoverMenu(
      event,
      [
        {
          label: 'auth.promote-to-trainer',
          handler: () => {
            this.userService
              .promote(user.accountData.id, ['trainer'])
              .subscribe(() => {
                user.accountData.roles =
                  user.accountData.roles.concat('trainer');
              });
          },
          disabled: () => user.accountData.roles.includes('trainer'),
        },
        {
          label: 'auth.demote-from-trainer',
          handler: () => {
            this.userService
              .demote(user.accountData.id, ['trainer'])
              .subscribe(() => {
                user.accountData.roles = user.accountData.roles.filter(
                  (role) => role !== 'trainer',
                );
              });
          },
          disabled: () => !user.accountData.roles.includes('trainer'),
        },
        {
          label: 'auth.trainee-panel',
          handler: () => {
            this.redirectionService.switchAndNavigate('/trainee', user.trainee);
          },
        },
        {
          label: 'auth.trainer-panel',
          handler: () => {
            this.redirectionService.switchTrainerAndNavigate(
              '/trainer',
              user.trainee,
            );
          },
        },
        {
          label: 'auth.change-trainer',
          handler: () => {
            this.trainerService
              .getAll()
              .pipe(
                switchMap((trainers) => {
                  return this.modalService.createEntitySelector<Trainer>({
                    title: 'auth.change-trainer',
                    entities: trainers,
                    size: 'large',
                    formatter: (entity) => {
                      return `${entity.firstName} ${entity.lastName}`;
                    },
                    entitySearchProps: ['firstName', 'lastName'],
                    entityDisplayProp: 'firstName',
                  });
                }),
                switchMap((trainer) => {
                  return this.traineeService.changeTrainer(
                    user.trainee.id,
                    trainer.id,
                  );
                }),
              )
              .subscribe();
          },
        },
      ],
      () => itemSliding.close(),
    );
  }
}
