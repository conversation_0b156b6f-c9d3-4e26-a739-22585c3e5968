import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  {
    path: 'exercises',
    loadChildren: () =>
      import('./pages/exercises/exercises.module').then(
        (m) => m.ExercisesPageModule,
      ),
  },
  {
    path: 'notifications',
    loadChildren: () =>
      import('./pages/notifications/notifications.module').then(
        (m) => m.NotificationsPageModule,
      ),
  },
  {
    path: 'subscription-plans',
    loadChildren: () =>
      import('./pages/subscription-plans/subscription-plans.module').then(
        (m) => m.SubscriptionPlansModule,
      ),
  },
  {
    path: 'invoices',
    loadChildren: () =>
      import('./pages/invoices/invoices.module').then(
        (m) => m.InvoicesPageModule,
      ),
  },
  {
    path: 'users',
    loadChildren: () =>
      import('./pages/users/users.module').then((m) => m.UsersPageModule),
  },
  {
    path: '',
    redirectTo: '/admin/exercises',
    pathMatch: 'full',
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AdminPanelRoutingModule {
  static readonly DEFAULT_URL = '/admin/exercises';
}
