import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../../environments/environment';
import { Observable } from 'rxjs';
import { Recipe, RecipeSearch } from '../models';
import { switchMap } from 'rxjs/operators';
import { ModalService } from '../../shared/services';
import { RecipeModalComponent } from '../components/recipe-modal/recipe-modal.component';
import { Page } from '../../shared/models';

@Injectable({
  providedIn: 'root',
})
export class RecipeService {
  static readonly BASE_URL = `${environment.NUTRITION_SERVICE_API_URL}/v1/recipes`;

  constructor(
    private http: HttpClient,
    private modalService: ModalService,
  ) {}

  search(search: string, page: number = 1): Observable<Page<RecipeSearch>> {
    return this.http.get<Page<RecipeSearch>>(
      `${RecipeService.BASE_URL}?search=${search || 'a'}&page=${page}`,
    );
  }

  get(id: number): Observable<Recipe> {
    return this.http.get<Recipe>(`${RecipeService.BASE_URL}/${id}`);
  }

  showRecipeModal(id: number) {
    this.get(id)
      .pipe(
        switchMap((recipe) => {
          return this.modalService.create({
            component: RecipeModalComponent,
            componentProps: { recipe },
          });
        }),
      )
      .subscribe();
  }
}
