import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import {
  StorageObject,
  StorageObjectUploadUrlRequest,
} from '../../shared/models';
import {
  ProductPromotion,
  ProductPromotionAICreateRequest,
  ProductPromotionRequest,
  ProductPromotionStore,
} from '../models';

@Injectable({
  providedIn: 'root',
})
export class ProductPromotionService {
  private static readonly BASE_URL = `${environment.NUTRITION_SERVICE_API_URL}/v1/product-promotions`;

  constructor(private http: HttpClient) {}

  getAll(search?: string): Observable<ProductPromotionStore[]> {
    let params = new HttpParams();

    if (search) {
      params = params.append('search', search);
    }

    return this.http.get<ProductPromotionStore[]>(
      `${ProductPromotionService.BASE_URL}`,
      { params },
    );
  }

  create(model: ProductPromotionRequest): Observable<ProductPromotion> {
    return this.http.post<ProductPromotion>(
      ProductPromotionService.BASE_URL,
      model,
    );
  }

  bulkCreate(
    models: ProductPromotionRequest[],
  ): Observable<ProductPromotion[]> {
    return this.http.post<ProductPromotion[]>(
      `${ProductPromotionService.BASE_URL}/bulk`,
      { promotions: models },
    );
  }

  bulkAiCreate(models: ProductPromotionAICreateRequest[]): Observable<void> {
    return this.http.post<void>(`${ProductPromotionService.BASE_URL}/ai/bulk`, {
      promotions: models,
    });
  }

  edit(id: string, model: ProductPromotionRequest): Observable<void> {
    return this.http.put<void>(
      `${ProductPromotionService.BASE_URL}/${id}`,
      model,
    );
  }

  delete(id: string): Observable<void> {
    return this.http.delete<void>(`${ProductPromotionService.BASE_URL}/${id}`);
  }

  getPhotoUploadUrl: (
    model: StorageObjectUploadUrlRequest,
  ) => Observable<StorageObject> = (model) => {
    return this.http.post<StorageObject>(
      `${ProductPromotionService.BASE_URL}/photos/upload-url`,
      model,
    );
  };
}
