import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, switchMap } from 'rxjs';
import { environment } from '../../../environments/environment';
import { FoodRecord, MealDiary, MealRecord } from '../models';
import { take } from 'rxjs/operators';
import { TraineeService } from '../../training/services';
import { FoodLoggerModalComponent } from '../components/food-logger-modal/food-logger-modal.component';
import { ModalService } from '../../shared/services';

@Injectable({
  providedIn: 'root',
})
export class FoodRecordService {
  static readonly BASE_URL = `${environment.NUTRITION_SERVICE_API_URL}/v1/food-records`;

  constructor(
    private http: HttpClient,
    private traineeService: TraineeService,
    private modalService: ModalService,
  ) {}

  getHistory(search?: string): Observable<FoodRecord[]> {
    return this.traineeService.traineeId$.pipe(
      take(1),
      switchMap((traineeId) => {
        let params = new HttpParams().append('traineeId', traineeId);

        if (search) {
          params = params.append('search', search);
        }

        return this.http.get<FoodRecord[]>(FoodRecordService.BASE_URL, {
          params,
        });
      }),
    );
  }

  createFoodLoggerModal({
    date,
    mealRecords,
    mealOrderNumber,
    onMealRecordCreate,
    opts,
  }: {
    date: string;
    mealRecords: MealRecord[];
    mealOrderNumber: number;
    onMealRecordCreate: (mealRecord: MealRecord) => void;
    opts: any;
  }): Observable<MealDiary> {
    return this.modalService.create<MealDiary>({
      component: FoodLoggerModalComponent,
      componentProps: {
        date,
        mealRecords,
        mealOrderNumber,
        onMealRecordCreate,
        ...opts,
      },
    });
  }
}
