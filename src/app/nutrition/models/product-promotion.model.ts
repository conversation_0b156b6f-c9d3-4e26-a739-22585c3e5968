export interface ProductPromotionRequest {
  productName: string;
  storeName: string;
  startDate: string;
  endDate: string;
  discountPrice: number;
  regularPrice: number;
  photoId: string;
}

export interface ProductPromotion {
  id: string;
  productName: string;
  storeName: string;
  startDate: string;
  endDate: string;
  discountPrice: number;
  regularPrice: number;
  discountPercentage: number;
  photoUrl: string;
  creatorId: string;
}

export interface ProductPromotionStore {
  storeName: string;
  activePromotions: ProductPromotion[];
  upcomingPromotions: ProductPromotion[];
}

export interface ProductPromotionAICreateRequest {
  index: number;
  photoUrl: string;
}

export interface ProductPromotionAIResult {
  index: number;
  productName: string;
  discountPrice: number;
  regularPrice: number;
  discountPercentage: number;
}
