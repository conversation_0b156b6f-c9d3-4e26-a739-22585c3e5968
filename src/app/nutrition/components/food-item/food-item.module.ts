import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { FoodItemComponent } from './food-item.component';
import { NutritionInfoChipsModule } from '../nutrition-info-chips/nutrition-info-chips.module';
import { LoadingCheckmarkModule } from '../../../shared/components/loading-checkmark/loading-checkmark.module';
import { CaloriesComponentModule } from '../calories/calories.module';
import { MacronutrientTextComponentModule } from '../macronutrient-text/macronutrient-text.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ReactiveFormsModule,
    NutritionInfoChipsModule,
    LoadingCheckmarkModule,
    CaloriesComponentModule,
    MacronutrientTextComponentModule,
  ],
  declarations: [FoodItemComponent],
  exports: [FoodItemComponent],
})
export class FoodItemModule {}
