import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { FoodCreateModalComponent } from './food-create-modal.component';
import { TranslateModule } from '@ngx-translate/core';
import { EntitySelectModule } from '../../../shared/components/entity-select/entity-select.module';
import { LoadingSpinnerComponentModule } from '../../../shared/components/loading-spinner/loading-spinner.module';
import { ModalLayoutV2Module } from '../../../shared/components/modal-layout-v2/modal-layout-v2.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    TranslateModule,
    EntitySelectModule,
    ReactiveFormsModule,
    LoadingSpinnerComponentModule,
    ModalLayoutV2Module,
  ],
  declarations: [FoodCreateModalComponent],
  exports: [FoodCreateModalComponent],
})
export class FoodCreateModalModule {}
