<mpg-modal-layout-v2
  [button]="modalButton"
  [isBreakpoint]="true"
  iconSrc="/assets/icon/create-food.svg"
  title="nutrition.create-food"
>
  <mpg-loading-spinner
    [fullScreen]="false"
    [isLoading]="isLoading"
  ></mpg-loading-spinner>
  @if (!isLoading) {
  <form [formGroup]="foodFormGroup">
    @if (!food) {
    <ion-input
      [label]="'nutrition.brand-name' | translate"
      formControlName="brandName"
      labelPlacement="stacked"
      type="text"
    ></ion-input>
    }
    <ion-input
      [label]="'nutrition.food-name' | translate"
      formControlName="name"
      labelPlacement="stacked"
      type="text"
    ></ion-input>
    <ng-container formGroupName="nutritionInfo">
      <ion-input
        [label]="'nutrition.fat-per-100g' | translate"
        formControlName="fat"
        labelPlacement="stacked"
        type="number"
      ></ion-input>
      <ion-input
        [label]="'nutrition.carbs-per-100g' | translate"
        formControlName="carbs"
        labelPlacement="stacked"
        type="number"
      ></ion-input>
      <ion-input
        [label]="'nutrition.protein-per-100g' | translate"
        formControlName="protein"
        labelPlacement="stacked"
        type="number"
      ></ion-input>
    </ng-container>
  </form>
  }
</mpg-modal-layout-v2>
