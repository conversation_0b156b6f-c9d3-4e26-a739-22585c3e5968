import { Component, Input, OnInit } from '@angular/core';
import { Recipe } from '../../models';
import { But<PERSON> } from '../../../shared/models';
import { ModalService } from '../../../shared/services';

@Component({
  selector: 'mpg-recipe-modal',
  templateUrl: './recipe-modal.component.html',
  styleUrls: ['./recipe-modal.component.scss'],
})
export class RecipeModalComponent implements OnInit {
  @Input() recipe: Recipe;
  modalButton: Button;

  constructor(private modalService: ModalService) {}

  ngOnInit(): void {
    this.modalButton = this.modalService.closeButton;
  }

  handleImageClick() {
    this.modalService.showPhoto(this.recipe.recipeImages.recipeImage[0]);
  }
}
