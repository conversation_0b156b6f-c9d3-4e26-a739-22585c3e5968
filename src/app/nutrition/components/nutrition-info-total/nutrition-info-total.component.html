<div [class.padding]="padding" class="container">
  <div class="flex-space-between">
    @if (showCalories) {
    <div class="total flex-centered margin-right-l">
      <mpg-calories [calories]="nutritionInfo.calories"></mpg-calories>
    </div>
    }
    <div class="macros flex-space-between flex-grow">
      <mpg-macronutrient-text
        [value]="nutritionInfo.protein"
        type="protein"
      ></mpg-macronutrient-text>
      <mpg-macronutrient-text
        [value]="nutritionInfo.carbs"
        type="carbs"
      ></mpg-macronutrient-text>
      <mpg-macronutrient-text
        [value]="nutritionInfo.fat"
        type="fat"
      ></mpg-macronutrient-text>
    </div>
  </div>
</div>
