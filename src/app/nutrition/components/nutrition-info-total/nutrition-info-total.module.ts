import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { NutritionInfoTotalComponent } from './nutrition-info-total.component';
import { MacronutrientTextComponentModule } from '../macronutrient-text/macronutrient-text.module';
import { CaloriesComponentModule } from '../calories/calories.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    MacronutrientTextComponentModule,
    CaloriesComponentModule,
  ],
  declarations: [NutritionInfoTotalComponent],
  exports: [NutritionInfoTotalComponent],
})
export class NutritionInfoTotalComponentModule {}
