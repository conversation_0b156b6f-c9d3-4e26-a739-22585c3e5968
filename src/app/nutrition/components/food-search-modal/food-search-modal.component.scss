ion-thumbnail {
  --size: 85px;
  border-radius: 10px;
  overflow: hidden;
  margin-left: 16px;
}

ion-chip:first-of-type {
  margin-left: 0;
}

.margin-bottom {
  margin-bottom: 5rem;
}

ion-select::part(icon) {
  display: none;
}

ion-fab-button {
  position: absolute;
  right: 0;
  z-index: 10;
}

.barcode-button::part(native) {
  height: 42px;
  font-size: 1.1em;
}

ngx-barcode-scanner {
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 1000;
}

ion-searchbar {
  --border-radius: var(--radius-medium) 0 0 var(--radius-medium) !important;
}

.filter {
  position: relative;
  cursor: pointer;
  background: #101010;
  border-radius: 0 var(--radius-medium) var(--radius-medium) 0;
  padding: 11px 12px 11px;

  ion-icon {
    font-size: 20px;
    color: var(--color-background-secondary-white-600);
  }

  &.active {
    background: var(--color-background-secondary-white-600);

    ion-icon {
      color: #101010;;
    }
  }
}

.filter:not(.no-border-radius)::before {
  content: "";
  position: absolute;
  left: 0;
  top: 25%;
  height: 50%;
  width: 1px;
  background-color: var(--color-background-primary-black-400);
}

.segments {
  margin: 16px 0;
  padding-bottom: 16px;
  box-shadow: 0 0.25px 0 var(--color-background-primary-black-300);

  ion-icon {
    font-size: 20px;
    margin-right: 16px;
    color: var(--color-background-secondary-white-600);
    cursor: pointer;
  }
}

.segment {
  color: var(--color-background-primary-black-200);
  font-size: var(--typefaces-size-2xl);
  font-weight: 300;
  margin-left: 16px;
  margin-bottom: 16px;
}

div.placeholder {
  height: 16px;
}

.create-food {
  height: 50px;
  width: 100%;
  border-radius: 12px;
  border: 0.5px dashed var(--color-background-primary-black-300);
  background: var(--color-background-primary-black-450);
  background-blend-mode: screen;
  margin-bottom: 16px;

  ion-icon {
    font-size: 24px;
    color: var(--color-background-secondary-white-800);
  }
}
