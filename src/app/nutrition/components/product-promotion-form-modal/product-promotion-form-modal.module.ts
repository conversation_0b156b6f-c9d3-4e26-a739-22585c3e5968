import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { ProductPromotionFormModalComponent } from './product-promotion-form-modal.component';
import { ModalLayoutModule } from '../../../shared/components/modal-layout/modal-layout.module';
import { TranslateModule } from '@ngx-translate/core';
import { EntitySelectModule } from '../../../shared/components/entity-select/entity-select.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ModalLayoutModule,
    TranslateModule,
    EntitySelectModule,
    ReactiveFormsModule,
  ],
  declarations: [ProductPromotionFormModalComponent],
  exports: [ProductPromotionFormModalComponent],
})
export class ProductPromotionFormModalModule {}
