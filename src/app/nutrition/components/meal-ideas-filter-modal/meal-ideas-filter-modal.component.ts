import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { BreakpointModal, Button, Segment } from '../../../shared/models';
import { ModalService } from '../../../shared/services';

@Component({
  selector: 'mpg-meal-ideas-filter-modal',
  templateUrl: './meal-ideas-filter-modal.component.html',
  styleUrls: ['./meal-ideas-filter-modal.component.scss'],
})
export class MealIdeasFilterModalComponent
  extends BreakpointModal
  implements OnInit
{
  static contentHeight = 570;
  filterFormGroup: FormGroup;
  segments: Segment[] = [
    { label: 'nutrition.menus', value: 'meal-diaries' },
    { label: 'nutrition.meals', value: 'meal-records' },
  ];
  button: Button = {
    label: 'buttons.apply',
    handler: () => {
      this.modalService.closeTopModal(this.filterFormGroup.value);
    },
  };

  secondaryButton: Button = {
    label: 'buttons.clear',
    handler: () => {
      this.modalService.closeTopModal({});
    },
  };

  constructor(
    private fb: FormBuilder,
    private modalService: ModalService,
  ) {
    super();
  }

  ngOnInit() {
    this.filterFormGroup = this.fb.group({
      type: ['meal-diaries'],
      minCalories: [null, Validators.min(0)],
      maxCalories: [null, Validators.min(0)],
      minProtein: [null, Validators.min(0)],
      maxProtein: [null, Validators.min(0)],
      minCarbs: [null, Validators.min(0)],
      maxCarbs: [null, Validators.min(0)],
      minFat: [null, Validators.min(0)],
      maxFat: [null, Validators.min(0)],
      minMealsCount: [null, Validators.min(0)],
      maxMealsCount: [null, Validators.min(0)],
    });

    this.filterFormGroup.controls.type.valueChanges.subscribe((segment) => {
      if (segment === 'meal-records') {
        this.filterFormGroup.controls.minMealsCount.disable();
        this.filterFormGroup.controls.maxMealsCount.disable();
        return;
      }

      this.filterFormGroup.controls.minMealsCount.enable();
      this.filterFormGroup.controls.maxMealsCount.enable();
    });
  }
}
