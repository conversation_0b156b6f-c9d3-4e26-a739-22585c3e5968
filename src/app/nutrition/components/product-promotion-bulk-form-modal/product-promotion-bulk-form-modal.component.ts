import { Component, OnInit } from '@angular/core';
import {
  AlertService,
  FileService,
  ModalService,
  ToastService,
} from '../../../shared/services';
import { ProductPromotionService } from '../../services';
import {
  AbstractControl,
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { ProductPromotionStoreName } from '../../enumerations';
import { environment } from '../../../../environments/environment';
import { Button } from '../../../shared/models';
import { catchError, map } from 'rxjs/operators';
import { filter, forkJoin } from 'rxjs';
import { WebSocketService } from '../../../notifications/services';
import { ProductPromotionAIResult } from '../../models';
import { WebSocketAction } from '../../../notifications/enumerations';
import { MPGValidators } from '../../../shared/validators/validators';

@Component({
  selector: 'mpg-product-promotion-bulk-form-modal',
  templateUrl: './product-promotion-bulk-form-modal.component.html',
  styleUrls: ['./product-promotion-bulk-form-modal.component.scss'],
})
export class ProductPromotionBulkFormModalComponent implements OnInit {
  formArray: FormArray;
  formGroupPhotoMap: Record<number, string> = {};
  isLoading = false;
  initialFormGroup: FormGroup;

  modalButton: Button = {
    label: 'buttons.done',
    handler: () => {
      if (this.formArray.invalid) {
        return;
      }

      this.isLoading = true;
      this.productPromotionService
        .bulkCreate(this.formArray.value)
        .pipe(
          catchError((error) => {
            this.isLoading = false;
            return this.toastService.handleError(error);
          }),
        )
        .subscribe(() => {
          this.isLoading = false;
          this.toastService.showInfoToast(
            'nutrition.create-product-promotions-success',
          );
          this.modalService.closeTopModal(true);
        });
    },
    disabled: () => {
      return !this.formArray || this.formArray.invalid || this.isLoading;
    },
  };
  stores = Object.values(ProductPromotionStoreName);
  aiLoading = false;
  fullFormGroupsCount = 0;

  constructor(
    private modalService: ModalService,
    private fb: FormBuilder,
    private productPromotionService: ProductPromotionService,
    private toastService: ToastService,
    private alertService: AlertService,
    private fileService: FileService,
    private webSocketService: WebSocketService,
  ) {}

  get photosUploaded(): boolean {
    return (
      Object.values(this.formGroupPhotoMap)?.length === this.formArray?.length
    );
  }

  ngOnInit(): void {
    this.webSocketService
      .on<ProductPromotionAIResult>(
        WebSocketAction.PRODUCT_PROMOTION_RECOGNITION,
      )
      .pipe(
        map((result) => {
          const discountPrice =
            result.discountPrice === 0 ? null : result.discountPrice;
          const regularPrice =
            result.regularPrice === 0 ? null : result.regularPrice;
          const discountPercentage =
            result.discountPercentage === 0 ? null : result.discountPercentage;

          if (!discountPrice && regularPrice) {
            return {
              ...result,
              discountPrice: regularPrice,
              regularPrice: null,
              discountPercentage,
            };
          }

          return {
            ...result,
            discountPrice,
            regularPrice,
            discountPercentage,
          };
        }),
      )
      .subscribe((result) => {
        const formGroup = this.formArray.controls[result.index] as FormGroup;
        formGroup.patchValue({
          productName: result.productName,
          discountPrice: result.discountPrice,
          regularPrice: result.regularPrice,
          discountPercentage: result.discountPercentage,
        });
        this.fullFormGroupsCount = this.formArray.controls.filter(
          (formGroup) => {
            return formGroup.get('productName').value;
          },
        ).length;

        if (this.fullFormGroupsCount === this.formArray.length) {
          this.aiLoading = false;
        }
      });

    this.initialFormGroup = this.fb.group(
      {
        storeName: [ProductPromotionStoreName.KAUFLAND, Validators.required],
        startDate: [null, Validators.required],
        endDate: [null, Validators.required],
      },
      {
        validators: MPGValidators.endDateAfterStartDate('endDate', 'startDate'),
      },
    );
  }

  getFormGroup(formGroup: AbstractControl) {
    return formGroup as FormGroup;
  }

  handleImageSelect(
    event: Event,
    formGroupControl: AbstractControl,
    index: number,
  ) {
    const formGroup = this.getFormGroup(formGroupControl);

    this.uploadImage(event, formGroup).subscribe((storageObject) => {
      if (storageObject.progress === 100) {
        this.formGroupPhotoMap[index] = storageObject.url;
      }
    });
  }

  handleImagesSelect(event: any) {
    this.aiLoading = false;
    const imageFiles = Array.from(event.target.files);
    this.formArray = this.createEmptyFormArray(imageFiles.length);
    const formGroups = this.formArray.controls as FormGroup[];

    if (imageFiles.length !== formGroups.length) {
      this.alertService.createErrorAlert(
        'Number of images should match the number of promotions',
      );
      return;
    }

    const uploadObservables = imageFiles.map((file, index) => {
      return this.uploadImage(
        { target: { files: [file] } },
        formGroups[index],
      ).pipe(
        filter((s) => s.progress === 100),
        map((s) => [s.url, index]),
      );
    });

    forkJoin(uploadObservables).subscribe((results) => {
      results.forEach(([url, index]) => {
        this.formGroupPhotoMap[index] = url;
      });
    });
  }

  handleAIRecognition() {
    if (this.aiLoading) {
      return;
    }

    this.aiLoading = true;
    this.productPromotionService
      .bulkAiCreate(
        Object.keys(this.formGroupPhotoMap).map((index) => {
          return {
            index: +index,
            photoUrl: this.formGroupPhotoMap[index],
          };
        }),
      )
      .subscribe();
  }

  showPhoto(src: string) {
    this.modalService.showPhoto(src);
  }

  private uploadImage(event: any, formGroup: FormGroup<any>) {
    return this.fileService.uploadFile(
      event,
      this.productPromotionService.getPhotoUploadUrl,
      environment.NUTRITION_SERVICE_API_URL,
      formGroup.controls.photoId as FormControl,
    );
  }

  private createEmptyFormArray(count: number): FormArray {
    const formGroups = Array.from({ length: count }).map(() => {
      return this.fb.group(
        {
          productName: ['', Validators.required],
          storeName: [
            this.initialFormGroup.controls.storeName.value,
            Validators.required,
          ],
          startDate: [
            this.initialFormGroup.controls.startDate.value,
            Validators.required,
          ],
          endDate: [
            this.initialFormGroup.controls.endDate.value,
            Validators.required,
          ],
          discountPrice: [null],
          regularPrice: [null],
          discountPercentage: [null],
          photoId: [null, Validators.required],
        },
        {
          validators: MPGValidators.atLeastOneTruthyInProvidedControls([
            'discountPrice',
            'discountPercentage',
          ]),
        },
      );
    });

    return this.fb.array(formGroups);
  }
}
