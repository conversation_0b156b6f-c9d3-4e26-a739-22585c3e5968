<mpg-modal-layout-v2 [button]="modalButton" title="nutrition.add-promotions">
  <mpg-loading-spinner
    [fullScreen]="false"
    [isLoading]="isLoading"
  ></mpg-loading-spinner>
  @if (!isLoading) {
  <form [formGroup]="initialFormGroup" class="margin-top-l">
    <mpg-segments
      [segments]="stores"
      (onChange)="initialFormGroup.controls.storeName.setValue($event)"
    ></mpg-segments>
    <div class="flex-space-around margin-top-l">
      <ion-label>{{ "from" | translate }}</ion-label>
      <ion-datetime-button
        datetime="startDate"
        [class.empty]="initialFormGroup.controls.startDate.invalid"
      ></ion-datetime-button>
      <ion-label>{{ "to" | translate }}</ion-label>
      <ion-datetime-button
        datetime="endDate"
        [class.empty]="initialFormGroup.controls.endDate.invalid"
      ></ion-datetime-button>
    </div>

    <ion-modal [keepContentsMounted]="true">
      <ng-template>
        <ion-datetime
          formControlName="startDate"
          id="startDate"
          presentation="date"
        ></ion-datetime>
      </ng-template>
    </ion-modal>
    <ion-modal [keepContentsMounted]="true">
      <ng-template>
        <ion-datetime
          [minuteValues]="[0, 15, 30, 45]"
          formControlName="endDate"
          id="endDate"
          presentation="date"
        ></ion-datetime>
      </ng-template>
    </ion-modal>
  </form>
  @if (initialFormGroup.valid) {
  <div class="flex-centered margin-top-l">
    <mpg-button
      color="dark"
      [iconOnly]="true"
      (click)="imagesInput.click()"
      [disabled]="initialFormGroup?.invalid"
      class="margin-right-m"
    >
      <ion-icon name="duplicate"></ion-icon>
    </mpg-button>
    <mpg-button
      color="light"
      class="flex-grow"
      (click)="handleAIRecognition()"
      [disabled]="!photosUploaded"
    >
      @if (!aiLoading) {
      <ion-text>AI Recognition</ion-text>
      } @else {
      {{ fullFormGroupsCount }}/{{ formArray?.length }}
      <ion-spinner name="lines-sharp-small"></ion-spinner>
      }
    </mpg-button>
    <input
      #imagesInput
      (change)="handleImagesSelect($event)"
      [multiple]="true"
      accept="image/*"
      class="invisible"
      type="file"
    />
  </div>
  } @for (formGroup of formArray?.controls; track formGroup; let index = $index)
  {
  <div class="image">
    <ion-img
      (click)="
        showPhoto(
          formGroupPhotoMap[index] ||
            'https://ionicframework.com/docs/img/demos/thumbnail.svg'
        )
      "
      [src]="
        formGroupPhotoMap[index] ||
        'https://ionicframework.com/docs/img/demos/thumbnail.svg'
      "
    />
  </div>
  <form [formGroup]="getFormGroup(formGroup)">
    <div class="flex-centered flex-wrap w-full ion-padding-bottom mt">
      <ion-input
        formControlName="productName"
        label="Product name"
        labelPlacement="floating"
        type="text"
        class="margin-bottom-m"
      ></ion-input>
      <ion-input
        formControlName="discountPrice"
        label="Discount price"
        labelPlacement="floating"
        type="number"
        class="margin-bottom-m"
      ></ion-input>
      <ion-input
        formControlName="regularPrice"
        label="Regular price"
        labelPlacement="floating"
        type="number"
        class="margin-bottom-m"
      ></ion-input>
      <ion-input
        formControlName="discountPercentage"
        label="Discount percentage"
        labelPlacement="floating"
        type="number"
        class="margin-bottom-m"
      ></ion-input>
    </div>
    <input
      #imageInput
      (change)="handleImageSelect($event, formGroup, index)"
      accept="image/*"
      class="invisible"
      type="file"
    />
  </form>
  }
  <div class="bottom-placeholder"></div>
  }
</mpg-modal-layout-v2>
