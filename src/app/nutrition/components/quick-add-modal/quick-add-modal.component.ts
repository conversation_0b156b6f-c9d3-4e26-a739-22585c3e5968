import { Component, Input, OnInit } from '@angular/core';
import {
  BreakpointModal,
  Button,
  Segment,
  StorageObjectInProgress,
} from '../../../shared/models';
import {
  AlertService,
  FileService,
  ModalService,
} from '../../../shared/services';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { FoodRecord, MealRecordRecognitionRequest } from '../../models';
import { combineLatestWith, startWith } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { MealDiaryService, MealRecordService } from '../../services';

@Component({
  selector: 'mpg-quick-add-modal',
  templateUrl: './quick-add-modal.component.html',
  styleUrls: ['./quick-add-modal.component.scss'],
})
export class QuickAddModalComponent extends BreakpointModal implements OnInit {
  static contentHeight = 550;

  @Input() date: string;
  @Input() time: string;
  @Input() foodRecord: FoodRecord;
  @Input() onDelete: () => void;

  quickAddFormGroup: FormGroup;
  aiMealRecognitionFormGroup: FormGroup;
  image: StorageObjectInProgress;
  segments: Segment[] = [
    {
      label: 'nutrition.quick-add-manual',
      value: 'manual',
    },
    {
      label: 'nutrition.quick-add-scan-photo',
      value: 'ai-recognition',
    },
  ];
  selectedSegment = 'manual';
  isLoading = false;

  constructor(
    private modalService: ModalService,
    private fb: FormBuilder,
    private translateService: TranslateService,
    private alertService: AlertService,
    private mealRecordService: MealRecordService,
    private fileService: FileService,
    private mealDiaryService: MealDiaryService,
  ) {
    super();
  }

  get modalButton(): Button {
    if (this.selectedSegment === 'manual') {
      return {
        label: 'buttons.done',
        handler: () => {
          if (this.quickAddFormGroup.invalid) {
            return;
          }

          this.modalService.closeTopModal(this.quickAddFormGroup.value);
        },
        disabled: () => this.quickAddFormGroup.invalid,
      };
    }

    return {
      label: 'buttons.scan',
      handler: () => {
        if (this.aiMealRecognitionFormGroup.invalid) {
          return;
        }

        this.isLoading = true;

        const model: MealRecordRecognitionRequest =
          this.aiMealRecognitionFormGroup.value;

        this.mealDiaryService
          .createMealRecordFromRecognition(this.date, model)
          .subscribe((recognition) => {
            this.modalService.closeTopModal(recognition);
          });
      },
      disabled: () =>
        this.aiMealRecognitionFormGroup.invalid ||
        this.image?.progress !== 100 ||
        this.isLoading,
    };
  }

  get secondaryButton(): Button {
    if (!this.onDelete) {
      return null;
    }

    return {
      label: 'buttons.delete',
      handler: () => {
        this.alertService.createDeleteAlert(async () => {
          this.onDelete();

          setTimeout(async () => {
            await this.modalService.closeTopModal();
          }, 500);
        });
      },
    };
  }

  ngOnInit(): void {
    const nutritionInfoFormGroup = this.fb.group({
      calories: [
        this.foodRecord?.nutritionInfo?.calories || 0,
        [Validators.required, Validators.min(1)],
      ],
      protein: [
        this.foodRecord?.nutritionInfo?.protein || 0,
        [Validators.required, Validators.min(0)],
      ],
      carbs: [
        this.foodRecord?.nutritionInfo?.carbs || 0,
        [Validators.required, Validators.min(0)],
      ],
      fat: [
        this.foodRecord?.nutritionInfo?.fat || 0,
        [Validators.required, Validators.min(0)],
      ],
      saturatedFat: [this.foodRecord?.nutritionInfo?.saturatedFat || 0],
      monounsaturatedFat: [
        this.foodRecord?.nutritionInfo?.monounsaturatedFat || 0,
      ],
      polyunsaturatedFat: [
        this.foodRecord?.nutritionInfo?.polyunsaturatedFat || 0,
      ],
      transFat: [this.foodRecord?.nutritionInfo?.transFat || 0],
      sugar: [this.foodRecord?.nutritionInfo?.sugar || 0],
      fiber: [this.foodRecord?.nutritionInfo?.fiber || 0],
      glycemicIndex: [this.foodRecord?.nutritionInfo?.glycemicIndex || 0],
      cholesterol: [this.foodRecord?.nutritionInfo?.cholesterol || 0],
      sodium: [this.foodRecord?.nutritionInfo?.sodium || 0],
      potassium: [this.foodRecord?.nutritionInfo?.potassium || 0],
      iron: [this.foodRecord?.nutritionInfo?.iron || 0],
      calcium: [this.foodRecord?.nutritionInfo?.calcium || 0],
      vitaminA: [this.foodRecord?.nutritionInfo?.vitaminA || 0],
      vitaminB: [this.foodRecord?.nutritionInfo?.vitaminB || 0],
      vitaminC: [this.foodRecord?.nutritionInfo?.vitaminC || 0],
    });

    this.quickAddFormGroup = this.fb.group({
      isQuickAdded: [true],
      name: [
        this.foodRecord?.name ||
          this.translateService.instant('nutrition.quick-add') ||
          null,
        Validators.required,
      ],
      metricServingAmount: [1, [Validators.required, Validators.min(1)]],
      nutritionInfo: nutritionInfoFormGroup,
    });

    const protein = nutritionInfoFormGroup.controls.protein.valueChanges;
    const carbs = nutritionInfoFormGroup.controls.carbs.valueChanges;
    const fat = nutritionInfoFormGroup.controls.fat.valueChanges;

    protein
      .pipe(
        startWith(0),
        combineLatestWith(carbs.pipe(startWith(0)), fat.pipe(startWith(0))),
      )
      .subscribe(([proteinValue, carbsValue, fatValue]) => {
        const calories = Math.round(
          (proteinValue || 0) * 4 + (carbsValue || 0) * 4 + (fatValue || 0) * 9,
        );

        nutritionInfoFormGroup.controls.calories.setValue(calories);
      });

    if (this.foodRecord?.nutritionInfo) {
      nutritionInfoFormGroup.controls.protein.setValue(
        this.foodRecord?.nutritionInfo.protein,
      );
      nutritionInfoFormGroup.controls.carbs.setValue(
        this.foodRecord?.nutritionInfo.carbs,
      );
      nutritionInfoFormGroup.controls.fat.setValue(
        this.foodRecord?.nutritionInfo.fat,
      );
      nutritionInfoFormGroup.controls.calories.setValue(
        this.foodRecord?.nutritionInfo.calories,
      );
    }

    this.aiMealRecognitionFormGroup = this.fb.group({
      time: [this.time],
      photoUrl: [null, Validators.required],
      language: ['Bulgarian'],
    });
  }

  handleImageSelect(event: any) {
    this.fileService
      .uploadFile(
        event,
        this.mealRecordService.getRecognitionPhotoUploadUrl,
        environment.NUTRITION_SERVICE_API_URL,
      )
      .subscribe((storageObject) => {
        if (storageObject?.progress === 100) {
          this.aiMealRecognitionFormGroup.controls.photoUrl.setValue(
            storageObject.url,
          );

          setTimeout(() => {
            this.image = storageObject;
          }, 1000);
          return;
        }

        this.image = storageObject;
      });
  }
}
