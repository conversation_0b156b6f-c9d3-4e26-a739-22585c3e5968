.container {
  height: 100%;
  width: 100%;

  display: flex;
  flex-direction: column;
  justify-content: flex-end;

  .record {
    margin-bottom: 16px;
  }
}

.new-meal-block {
  cursor: pointer;
  border-radius: var(--radius-medium);
  border: 0.5px solid var(--color-background-primary-black-400);
  background: var(--color-background-primary-black-500);
  height: 88px;
  position: relative;
  margin-bottom: 30px;

  ion-icon {
    font-size: 20px;
    color: var(--color-background-secondary-white-600);
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
}

.empty {
  height: 32px;
}
