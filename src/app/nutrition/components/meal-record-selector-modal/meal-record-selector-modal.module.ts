import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { MealRecordSelectorModalComponent } from './meal-record-selector-modal.component';
import { ModalLayoutV2Module } from '../../../shared/components/modal-layout-v2/modal-layout-v2.module';
import { MealRecordItemV2ComponentModule } from '../meal-record-item-v2/meal-record-item-v2.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ModalLayoutV2Module,
    MealRecordItemV2ComponentModule,
  ],
  declarations: [MealRecordSelectorModalComponent],
  exports: [MealRecordSelectorModalComponent],
})
export class MealRecordSelectorModalModule {}
