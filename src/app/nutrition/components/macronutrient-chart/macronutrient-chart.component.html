<div class="container">
  <div class="title">{{ type | uppercase }}</div>
  <div class="chart">
    @if (chartOptions) {
    <mpg-chart [chartOptions]="chartOptions"></mpg-chart>
    }
  </div>
  <div class="value">
    <ion-label [style.color]="getColor()" class="grams">
      {{ value | number: "1.0-0" }}
    </ion-label>
    <ion-label class="goal">/{{ goal | number: "1.0-0" }} g</ion-label>
  </div>
</div>
