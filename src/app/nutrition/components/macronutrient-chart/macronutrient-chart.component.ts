import {
  Component,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { ApexOptions } from 'ng-apexcharts';

@Component({
  selector: 'mpg-macronutrient-chart',
  templateUrl: './macronutrient-chart.component.html',
  styleUrls: ['./macronutrient-chart.component.scss'],
})
export class MacronutrientChartComponent implements OnInit, OnChanges {
  @Input() value: number;
  @Input() goal = 100;
  @Input() type: 'protein' | 'carbs' | 'fat';

  chartOptions: ApexOptions;

  constructor() {}

  ngOnInit() {}

  getColor() {
    switch (this.type) {
      case 'protein':
        return '#FF0F1E';
      case 'carbs':
        return '#FFB333';
      case 'fat':
        return '#A882DD';
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    const color = this.getColor();

    this.chartOptions = {
      series: [
        {
          data: [Math.min(this.value, this.goal)],
        },
      ],
      chart: {
        type: 'bar',
        height: 15, // Keep it thin
        sparkline: {
          enabled: true, // Removes unnecessary labels
        },
        dropShadow: {
          enabled: true,
          top: 0,
          left: 5,
          blur: 10, // Controls glow intensity
          opacity: 0.3,
          color,
        },
      },
      plotOptions: {
        bar: {
          horizontal: true,
          barHeight: '100%', // Makes it look like a thin line
          borderRadius: 8, // Rounds the ends
          colors: {
            backgroundBarColors: ['#1f1f1f'],
            backgroundBarRadius: 8, // Rounds the ends
          },
        },
      },
      fill: {
        colors: [color],
        type: 'solid',
      },
      xaxis: {
        max: this.goal, // Max value
        labels: {
          show: false, // Hide axis labels
        },
      },
      grid: {
        show: false, // Hide background grid
      },
      stroke: {
        show: false, // Hide line stroke
      },
      tooltip: {
        enabled: false, // Hide tooltip
      },
    };
  }
}
