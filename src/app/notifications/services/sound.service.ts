import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Sound, SoundCreateRequest, SoundEditRequest } from '../models';
import {
  StorageObject,
  StorageObjectUploadUrlRequest,
} from '../../shared/models';

@Injectable({
  providedIn: 'root',
})
export class SoundService {
  static readonly BASE_URL = `${environment.NOTIFICATION_SERVICE_API_URL}/v1/sounds`;

  constructor(private http: HttpClient) {}

  getAll(): Observable<Sound[]> {
    return this.http.get<Sound[]>(SoundService.BASE_URL);
  }

  create(model: SoundCreateRequest): Observable<Sound> {
    return this.http.post<Sound>(SoundService.BASE_URL, model);
  }

  edit(id: string, model: SoundEditRequest): Observable<void> {
    return this.http.patch<void>(`${SoundService.BASE_URL}/${id}`, model);
  }

  delete(id: string): Observable<void> {
    return this.http.delete<void>(`${SoundService.BASE_URL}/${id}`);
  }

  getSoundUploadUrl: (
    model: StorageObjectUploadUrlRequest,
  ) => Observable<StorageObject> = (model) => {
    return this.http.post<StorageObject>(
      `${SoundService.BASE_URL}/upload-url`,
      model,
    );
  };
}
