import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment';
import { AngularFireMessaging } from '@angular/fire/compat/messaging';
import { combineLatestWith, filter, switchMap } from 'rxjs';
import { TokenService } from './token.service';
import { AlertService, ToastService } from '../../shared/services';
import { NotificationsService } from './notifications.service';
import { take } from 'rxjs/operators';
import { UserService } from '../../auth/services';

@Injectable({
  providedIn: 'root',
})
export class PushNotificationsService {
  constructor(
    private messaging: AngularFireMessaging,
    private tokenService: TokenService,
    private alertService: AlertService,
    private toastService: ToastService,
    private notificationsService: NotificationsService,
    private userServive: UserService,
  ) {
    if (!environment.production) {
      return;
    }

    this.notificationsService.hasUserClicked$
      .pipe(
        filter((x) => !x),
        take(1),
        switchMap(() => this.messaging.getToken),
        filter((token) => !!token),
        switchMap((token) => this.tokenService.save(token)),
      )
      .subscribe();

    setTimeout(() => {
      this.messaging.getToken
        .pipe(combineLatestWith(this.userServive.loggedUserId$))
        .subscribe(([token, userId]) => {
          if (!token && userId) {
            this.alertService.createConfirmAlert(
              'notifications.alert-header',
              () => {
                this.requestPerms();
              },
            );
          }
        });
    }, 10000);
  }

  async requestPerms() {
    if (!environment.production) {
      return;
    }

    this.messaging.requestToken
      .pipe(
        switchMap((token) => {
          return this.tokenService.save(token);
        }),
      )
      .subscribe(() => {
        this.toastService.showInfoToast(
          'notifications.notifications-setup-success',
        );
      });
  }
}
