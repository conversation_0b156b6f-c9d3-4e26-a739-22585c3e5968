import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment';
import { HttpClient } from '@angular/common/http';
import { switchMap } from 'rxjs';
import { UserService } from '../../auth/services';
import { take } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class TokenService {
  static readonly BASE_URL = `${environment.NOTIFICATION_SERVICE_API_URL}/v1/tokens`;

  constructor(
    private userService: UserService,
    private http: HttpClient,
  ) {}

  save(token: string) {
    return this.userService.loggedUserId$.pipe(
      take(1),
      switchMap((userId) => {
        return this.http.put<void>(`${TokenService.BASE_URL}`, {
          userId,
          token,
        });
      }),
    );
  }
}
