<mpg-modal-layout
  [button]="modalButton"
  [title]="'notifications.send' | translate"
  >
  <form [formGroup]="sendNotificationFormGroup">
    <ion-item>
      <ion-input
        (keyup.enter)="modalButton.handler()"
        formControlName="title"
        label="Title"
        labelPlacement="floating"
        type="text"
      ></ion-input>
    </ion-item>
    <ion-item>
      <ion-input
        (keyup.enter)="modalButton.handler()"
        formControlName="body"
        label="Body"
        labelPlacement="floating"
        type="text"
      ></ion-input>
    </ion-item>
    <ion-item>
      <ion-toggle formControlName="showConfetti"> Show confetti</ion-toggle>
    </ion-item>
    <ion-item>
      <ion-select formControlName="soundId" label="Sound">
        @for (sound of sounds; track sound) {
          <ion-select-option [value]="sound.id"
            >{{ sound.name }}
          </ion-select-option>
        }
      </ion-select>
    </ion-item>
    <ion-item>
      <ion-input
        (keyup.enter)="modalButton.handler()"
        formControlName="videoUrl"
        label="Video url"
        labelPlacement="floating"
        type="text"
      ></ion-input>
    </ion-item>
    <input
      #imageInput
      (change)="handleImageSelect($event)"
      accept="image/*"
      class="invisible"
      type="file"
      />
    @if (image) {
      <ion-item>
        @if (image.progress < 100) {
          <ion-progress-bar
            [value]="image.progress / 100"
          ></ion-progress-bar>
        }
        @if (image.progress === 100) {
          <ion-img
            [src]="image.url"
            class="margin-auto"
          ></ion-img>
        }
      </ion-item>
    }
    <div
      class="modal-buttons-container ion-justify-content-center ion-margin-bottom"
      >
      <ion-fab-button (click)="imageInput.click()" class="ion-margin-top">
        <ion-icon name="image"></ion-icon>
      </ion-fab-button>
    </div>
  </form>
</mpg-modal-layout>
