@if (
  (userRoles$ | async).trainer &&
  latestNotifications.length > 0 &&
  !(backUrl$ | async)
  ) {
  <ion-fab
    [routerLink]="['/trainer/tabs/notifications']"
    slot="fixed"
    >
    <ion-fab-button color="secondary">
      <ion-icon name="notifications"></ion-icon>
    </ion-fab-button>
    <div class="counter flex-centered">
      <ion-text color="dark">{{ latestNotifications.length }}</ion-text>
      <ion-ripple-effect></ion-ripple-effect>
    </div>
  </ion-fab>
}
