import { Component, Input, OnInit } from '@angular/core';
import {
  AudioService,
  ConfettiService,
  ModalService,
} from '../../../shared/services';
import { Button } from '../../../shared/models';
import { Notification, NotificationHandler } from '../../models';
import { NotificationsService } from '../../services';
import { take } from 'rxjs/operators';
import { Router } from '@angular/router';

@Component({
  selector: 'mpg-notification-modal',
  templateUrl: './notification-modal.component.html',
  styleUrls: ['./notification-modal.component.scss'],
})
export class NotificationModalComponent implements OnInit {
  @Input() notification: Notification;
  @Input() notificationHandler?: NotificationHandler;

  soundUrl?: string;
  photoUrl?: string;
  showConfetti: boolean;
  videoUrl?: string;

  hasUserClicked = false;
  isAudioPlaying = false;
  isCampaign = false;

  iconButtons: Button[] = [
    {
      label: 'details',
      icon: 'eye',
      color: 'primary',
      handler: () => {
        this.notificationsService
          .read(this.notification.id)
          .subscribe(async () => {
            await this.modalService.closeTopModal(true);
            this.notificationHandler.handle(this.notification.data);
          });
      },
      disabled: () => !this.notificationHandler,
    },
    {
      label: 'confetti',
      icon: 'sparkles-sharp',
      color: 'secondary',
      handler: () => {
        this.confettiService.showConfetti(1);
      },
      disabled: () => !this.showConfetti,
    },
    {
      label: 'ok',
      icon: 'thumbs-up-sharp',
      handler: () => {
        this.notificationsService.read(this.notification.id).subscribe(() => {
          this.modalService.closeTopModal(true);
        });
      },
      disabled: () => !!this.notificationHandler,
    },
    {
      label: 'music',
      icon: 'musical-notes',
      color: 'dark',
      handler: () => {
        this.playSound();
      },
      disabled: () => !this.soundUrl || this.isAudioPlaying,
    },
  ];

  constructor(
    private modalService: ModalService,
    private notificationsService: NotificationsService,
    private audioService: AudioService,
    private confettiService: ConfettiService,
    private router: Router,
  ) {}

  ngOnInit() {
    this.soundUrl = this.notification.data?.soundUrl;
    this.photoUrl = this.notification.data?.photoUrl;
    this.showConfetti = !!this.notification.data?.showConfetti;
    this.videoUrl = this.notification.data?.videoUrl;
    this.isCampaign = !!this.notification.data?.campaign;

    this.notificationsService.hasUserClicked$
      .pipe(take(1))
      .subscribe((hasUserClicked) => {
        this.hasUserClicked = hasUserClicked;
      });

    this.showSpecialEffects();
  }

  handleUserClick() {
    this.hasUserClicked = true;
    this.showSpecialEffects();
  }

  handleBlackFriday() {
    this.notificationsService.read(this.notification.id).subscribe(() => {
      this.modalService.closeTopModal(true);
      this.router.navigate(['/trainee/subscription']);
    });
  }

  private showSpecialEffects() {
    if (!this.hasUserClicked && this.soundUrl) {
      return;
    }

    if (this.soundUrl) {
      this.playSound();
    }

    if (this.showConfetti) {
      this.confettiService.showConfetti();
    }
  }

  private playSound() {
    this.audioService.play(this.soundUrl, () => {
      this.isAudioPlaying = false;
    });
    this.isAudioPlaying = true;
  }
}
