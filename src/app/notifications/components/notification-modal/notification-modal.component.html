@if (soundUrl ? !hasUserClicked : false) {
  <div
    class="h-full flex-column-centered"
    >
    <ion-text color="dark"
      ><h2 class="bolder ion-padding">
      {{ "notifications.special" | translate }}
    </h2>
  </ion-text>
  <ion-button (click)="handleUserClick()"
    >{{ "buttons.of-course" | translate }}
  </ion-button>
</div>
}

@if (soundUrl ? hasUserClicked : true) {
  <mpg-modal-layout
    [iconButtons]="!isCampaign ? iconButtons : undefined"
    [title]="notification.title"
    >
    <div [ngClass]="{ 'h-full': !photoUrl }">
      @if (notification.body) {
        <ion-text
          ><h5 class="ion-padding-horizontal ion-margin-bottom">
          {{ notification.body }}
        </h5></ion-text
        >
      }
      @if (videoUrl) {
        <video [src]="videoUrl" mpgVideoPlayer></video>
      }
      @if (isCampaign) {
        <div class="modal-buttons-container">
          <ion-button
            (click)="handleBlackFriday()"
            class="ion-margin"
            color="primary"
            >
            Виж промоциите
          </ion-button>
        </div>
      }
    </div>
    @if (photoUrl) {
      <ion-img [src]="photoUrl"></ion-img>
    }
  </mpg-modal-layout>
}
