import { Injectable } from '@angular/core';
import { NotificationHandler } from '../models';
import { RedirectionService } from '../../shared/services';

@Injectable({
  providedIn: 'root',
})
export class MealRecordNotificationHandler implements NotificationHandler {
  constructor(private redirectionService: RedirectionService) {}

  handle(data: Record<string, string>): void {
    const { traineeId, date } = data;

    this.redirectionService.fetchAndNavigate(
      '/trainee/tabs/nutrition',
      traineeId,
      {
        queryParams: { date },
      },
    );
  }
}
