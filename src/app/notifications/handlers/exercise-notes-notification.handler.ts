import { Injectable } from '@angular/core';
import { Notification<PERSON>and<PERSON> } from '../models';
import { ExerciseService } from '../../training/services';

@Injectable({
  providedIn: 'root',
})
export class ExerciseNotesNotificationHandler implements NotificationHandler {
  constructor(private exerciseService: ExerciseService) {}

  handle(data: Record<string, string>): void {
    const { traineeId, exerciseId } = data;
    if (!exerciseId) {
      return;
    }

    this.exerciseService.get(exerciseId).subscribe((exercise) => {
      this.exerciseService.showExerciseNotesModal(exercise, traineeId);
    });
  }
}
