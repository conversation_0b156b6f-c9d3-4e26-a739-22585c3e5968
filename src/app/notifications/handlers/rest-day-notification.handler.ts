import { Injectable } from '@angular/core';
import { NotificationHandler } from '../models';
import { RedirectionService } from '../../shared/services';

@Injectable({
  providedIn: 'root',
})
export class RestDayNotificationHandler implements NotificationHandler {
  constructor(private redirectionService: RedirectionService) {}

  handle(data: Record<string, string>): void {
    const { traineeId } = data;

    this.redirectionService.fetchAndNavigate(
      '/trainee/training-schedule',
      traineeId,
    );
  }
}
