import { Injectable } from '@angular/core';
import { NotificationHandler } from '../models';
import { FoodService } from '../../nutrition/services';
import { switchMap } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class CreatedFoodNotificationHandler implements NotificationHandler {
  constructor(private foodService: FoodService) {}

  handle(data: Record<string, string>): void {
    const { foodId } = data;

    this.foodService
      .get(foodId)
      .pipe(
        switchMap((food) => {
          return this.foodService.createFoodCreationModal(food);
        }),
      )
      .subscribe();
  }
}
