import { Injectable } from '@angular/core';
import { NotificationHandler } from '../models';
import { WorkoutExerciseSetRecordService } from '../../training/services';

@Injectable({
  providedIn: 'root',
})
export class SetRecordVideoNotificationHandler implements NotificationHandler {
  constructor(
    private workoutExerciseSetRecordService: WorkoutExerciseSetRecordService,
  ) {}

  handle(data: Record<string, string>): void {
    const { traineeId, setRecordId } = data;

    this.workoutExerciseSetRecordService.fetchAndShowSetRecordVideoModal({
      traineeId,
      setRecordId,
    });
  }
}
