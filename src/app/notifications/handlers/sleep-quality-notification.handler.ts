import { Injectable } from '@angular/core';
import { NotificationHandler } from '../models';
import { SleepQualityService } from '../../training/services';

@Injectable({
  providedIn: 'root',
})
export class SleepQualityNotificationHandler implements NotificationHandler {
  constructor(private sleepQualityService: SleepQualityService) {}

  handle(data: Record<string, string>): void {
    const { traineeId, date } = data;

    this.sleepQualityService.showHistoryModal({
      traineeId,
      highlightedDate: date,
    });
  }
}
