import { NotificationType } from '../enumerations';
import { WorkoutRecordNotificationHandler } from './workout-record-notification.handler';
import { Type } from '@angular/core';
import { NotificationHandler } from '../models';
import { BodyWeightNotificationHandler } from './body-weight-notification.handler';
import { ExerciseNotesNotificationHandler } from './exercise-notes-notification.handler';
import { BodyPhotoNotificationHandler } from './body-photo-notification.handler';
import { RestDayNotificationHandler } from './rest-day-notification.handler';
import { SetRecordVideoNotificationHandler } from './set-record-video-notification.handler';
import { SleepQualityNotificationHandler } from './sleep-quality-notification.handler';
import { BodyMeasurementsNotificationHandler } from './body-measurements-notification.handler';
import { MealRecordNotificationHandler } from './meal-record-notification.handler';
import { CreatedFoodNotificationHandler } from './created-food-notification.handler';
import { TraineeInvitationNotificationHandler } from './trainee-invitation-notification.handler';
import { PhotoNotificationHandler } from './photo-notification.handler';
import { CreatedProductPromotionNotificationHandler } from './created-product-promotion-notification.handler';
import { TraineeDetailsNotificationHandler } from './trainee-details-notification.handler';

export const NOTIFICATIONS_HANDLERS_MAP: Partial<
  Record<NotificationType, Type<NotificationHandler>>
> = {
  ENTERED_BODY_WEIGHT: BodyWeightNotificationHandler,
  ENTERED_BODY_MEASUREMENTS: BodyMeasurementsNotificationHandler,
  ENTERED_SLEEP_QUALITY: SleepQualityNotificationHandler,
  STARTED_WORKOUT: WorkoutRecordNotificationHandler,
  FINISHED_WORKOUT: WorkoutRecordNotificationHandler,
  CREATED_EXERCISE_NOTE: ExerciseNotesNotificationHandler,
  CREATED_WORKOUT_RECORD_NOTE: WorkoutRecordNotificationHandler,
  CREATED_WORKOUT_EXERCISE_RECORD_NOTE: WorkoutRecordNotificationHandler,
  CREATED_SET_RECORD_VIDEO_NOTE: SetRecordVideoNotificationHandler,
  UPLOADED_BODY_PHOTO: BodyPhotoNotificationHandler,
  UPLOADED_NOTE_PHOTO: PhotoNotificationHandler,
  UPLOADED_SET_RECORD_VIDEO: SetRecordVideoNotificationHandler,
  SCHEDULED_REST_DAY: RestDayNotificationHandler,
  WHOOP_SLEEP_SCORE: SleepQualityNotificationHandler,
  WHOOP_RECOVERY_SCORE: SleepQualityNotificationHandler,
  FILLED_TRAINEE_INVITATION: TraineeInvitationNotificationHandler,
  PRE_APPROVED_TRAINEE_INVITATION: TraineeDetailsNotificationHandler,
  SKIPPED_EXERCISE: WorkoutRecordNotificationHandler,
  CHANGED_EXERCISE: WorkoutRecordNotificationHandler,

  CREATED_FOOD: CreatedFoodNotificationHandler,
  CREATED_MEAL_RECORD: MealRecordNotificationHandler,
  CREATED_MEAL_RECORD_RECOGNITION: MealRecordNotificationHandler,
  CREATED_MEAL_RECORD_COPY: MealRecordNotificationHandler,
  CREATED_MEAL_RECORDS_COPY: MealRecordNotificationHandler,
  CREATED_PRODUCT_PROMOTION: CreatedProductPromotionNotificationHandler,
  QUICK_ADDED_FOOD_RECORD: MealRecordNotificationHandler,
};
