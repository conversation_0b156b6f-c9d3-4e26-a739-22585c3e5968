$screen-breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1400px,
) !default;

@mixin min-width-sm {
  @media (max-width: map-get($screen-breakpoints, sm)) {
    @content;
  }
}
@mixin min-width-md {
  @media (min-width: map-get($screen-breakpoints, md)) {
    @content;
  }
}
@mixin min-width-lg {
  @media (min-width: map-get($screen-breakpoints, lg)) {
    @content;
  }
}
@mixin min-width-xl {
  @media (min-width: map-get($screen-breakpoints, xl)) {
    @content;
  }
}
@mixin max-width-sm {
  @media (max-width: map-get($screen-breakpoints, sm)) {
    @content;
  }
}
@mixin max-width-md {
  @media (max-width: map-get($screen-breakpoints, md)) {
    @content;
  }
}
@mixin max-width-lg {
  @media (max-width: map-get($screen-breakpoints, lg)) {
    @content;
  }
}
@mixin max-width-xl {
  @media (max-width: map-get($screen-breakpoints, xl)) {
    @content;
  }
}
