@import "breakpoints";

@each $keySize, $valueSize in $screen-breakpoints {
  .shadow-border-#{$keySize} {
    @media (min-width: $valueSize) {
      border-radius: 30px;
      box-shadow: 0 4px 16px rgba(var(black), 0.5);
      overflow: hidden;
    }
  }

  .margin-top-#{$keySize} {
    @media (min-width: $valueSize) {
      margin-top: 2rem;
    }
  }

  .margin-right-#{$keySize} {
    @media (min-width: $valueSize) {
      margin-right: 1rem;
    }
  }

  .flex-centered-#{$keySize} {
    @media (min-width: $valueSize) {
      display: flex !important;
      justify-content: center !important;
      align-items: center !important;
    }
  }

  .flex-space-around-#{$keySize} {
    @media (min-width: $valueSize) {
      display: flex !important;
      justify-content: space-around !important;
      align-items: center !important;
    }
  }
}

.margin-auto {
  display: block;
  margin: 0 auto;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.bold {
  font-weight: bold;
}

.bolder {
  font-weight: bolder;
}

.pointer {
  cursor: pointer;
}

.light-background {
  --background: var(--ion-color-light);
  background: var(--ion-color-light);
}

.white-background {
  --background: white;
  background: white;
}

.dark-background {
  --background: var(--ion-color-dark);
  background: var(--ion-color-dark);
}

.border-bottom {
  border-bottom: 2px solid var(--ion-color-dark);
}

.border-vertical {
  border-top: 2px solid var(--ion-color-dark);
  border-bottom: 2px solid var(--ion-color-dark);
}

.border-right {
  border-right: 2px solid var(--ion-color-dark);
}

.border-radius-top {
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
}

.border-radius-bottom {
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
}

.date-time-modal::part(content) {
  --background: none;
}

.date-time-modal .ion-page {
  justify-content: center;
}

ion-fab-button[data-desc] {
  position: relative;
}

ion-fab-button[data-desc]::after {
  position: absolute;
  content: attr(data-desc);
  z-index: 1;
  right: 55px;
  bottom: 4px;
  background-color: var(--ion-color-primary);
  padding: 9px;
  border-radius: 15px;
  color: white;
  box-shadow: 0 3px 5px -1px rgba(0, 0, 0, 0.2),
  0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12);
  cursor: pointer;
}

// Hide scrollbar for Chrome, Safari and Opera
ion-content::part(scroll)::-webkit-scrollbar {
  display: none;
}

// Hide scrollbar for IE, Edge and Firefox
ion-content::part(scroll) {
  -ms-overflow-style: none; // IE and Edge
  scrollbar-width: none; // Firefox
}

.p-relative {
  position: relative;
}

.p-sticky {
  position: sticky;
  top: 0;
}

.p-fixed {
  position: fixed;
}

.z-50 {
  z-index: 50;
}

.z-100 {
  z-index: 100;
}

.day-of-week {
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;

  h2 {
    border-radius: 50%;
    background: rgba(var(--ion-color-dark-rgb), 0.3);
    width: 2rem;
    height: 2rem;
    line-height: 2rem;
    font-weight: bold;
    color: var(--ion-color-dark);
  }
}

ion-card {
  border-radius: 30px;
}

ion-popover {
  --width: 220px;
}

ion-item-option.transparent {
  background: transparent;
}

ion-item.transparent {
  --ion-item-background: transparent;
}

.flex {
  display: flex !important;
}

.flex-start {
  display: flex !important;
  justify-content: flex-start !important;
  align-items: center !important;
}

.flex-centered {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}

.flex-end {
  display: flex !important;
  justify-content: flex-end !important;
  align-items: center !important;
}

.flex-space-around {
  display: flex !important;
  justify-content: space-around !important;
  align-items: center !important;
}

.flex-space-between {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
}

.flex-column-space-around {
  display: flex !important;
  flex-direction: column;
  justify-content: space-around !important;
  align-items: center !important;
}

.flex-column-space-between {
  display: flex !important;
  flex-direction: column;
  justify-content: space-between !important;
  align-items: center !important;
}

.flex-column-centered {
  display: flex !important;
  flex-direction: column;
  justify-content: center !important;
  align-items: center !important;
}

.flex-column-start {
  display: flex !important;
  flex-direction: column;
  justify-content: center !important;
  align-items: flex-start !important;
}

.flex-column-end {
  display: flex !important;
  flex-direction: column;
  justify-content: center !important;
  align-items: flex-end !important;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-shrink {
  flex-shrink: 1;
}

.flex-grow {
  flex-grow: 1;
}

.overflow-scroll {
  overflow-x: scroll;
}

.overflow-scroll::-webkit-scrollbar {
  display: none;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-auto {
  overflow: unset !important;
}

ion-chip.no-border-radius {
  border-radius: 0;
}

ion-chip.dark {
  --background: var(--ion-color-dark);
  --color: var(--ion-color-light);
}

ion-chip.primary {
  --background: var(--ion-color-primary);
  --color: var(--ion-color-light);
}

ion-chip.secondary {
  --background: var(--ion-color-secondary);
  --color: var(--ion-color-light);
}

ion-chip.success {
  --background: var(--ion-color-success);
  --color: var(--ion-color-light);
}

ion-chip.warning {
  --background: var(--ion-color-warning);
  --color: var(--ion-color-light);
}

ion-chip.gold {
  --background: linear-gradient(to left, #ffd700, #f8b500, #e5a00d, #c4881a, #a37020);
  --color: var(--ion-color-light);
}

ion-chip.light {
  --background: var(--ion-color-light);
  --color: var(--ion-color-dark);
}


.invisible {
  display: none;
}

pre {
  font-family: "Montserrat", serif;
  margin: 0;
}

.ion-activatable {
  position: relative;
  cursor: pointer;
}

.pan-zoom-frame {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.pan-element {
  position: relative !important;
}

.no-end-padding {
  --inner-padding-end: 0;
  padding-inline-end: 0 !important;
}

.no-bottom-padding {
  padding-bottom: 0;
}

.no-top-padding {
  padding-top: 0;
}

ion-icon.chip {
  font-size: 32px;
}

ion-icon.column {
  font-size: 22px;
}

ion-item.small {
  --min-height: 34px;
}

.plyr:fullscreen video {
  max-height: 100% !important
}

ion-fab.mb {
  margin-bottom: 40px;
}

.white-space-nowrap {
  white-space: nowrap;
}

mpg-screenshot-button {
  position: absolute;
  bottom: 0;
  right: 0;
  z-index: 100;
}

html, body {
  font-family: 'Bahnschrift', 'Bahnschrift-Regular';
  background: linear-gradient(
      0deg,
      rgba(5, 5, 5, 0.5) 0%,
      rgba(5, 5, 5, 0.5) 100%
  ),
  linear-gradient(
      168deg,
      #000 0%,
      rgba(0, 0, 0, 0) 88.51%,
      #181C61 109.65%
  ),
  radial-gradient(
      227.26% 102.31% at 30.23% 78.97%,
      #0A0A0A 33.5%,
      #0A0A0A 70.36%,
      #400405 74.86%,
      #FF0F1E 79.38%,
      #F4FF53 85.74%,
      #0A0A0A 97.75%
  );

  background-blend-mode: normal, exclusion, normal;

}

ion-content {
  --background: none;
  --padding-start: 30px;
  --padding-end: 30px;
}

ion-tab-bar {
  position: absolute;
  bottom: 40px;
  left: 50%;
  transform: translateX(-50%);
  height: 48px;
  width: 340px;

  border-radius: var(--radius-extra-large);
  --border: 0.25px solid var(--color-background-primary-black-400, #1F1F1F);
  --background: var(--color-background-primary-black-600, #0D0D0D);
  --color: linear-gradient(333.43deg, #1F1F1F 22.22%, #4D4D4D 161.9%);
  box-shadow: 0 0 2px 4px rgba(0, 0, 0, 0.10);
  --ion-safe-area-bottom: 0px;
  padding-bottom: 0 !important;
  transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;

  .tab-selected svg {
    --start-color: var(--ion-color-primary);
    --end-color: var(--ion-color-primary);
    transition: --start-color 0.5s ease, --end-color 0.5s ease;
  }

  &.hidden {
    transform: translate(-50%, 100%);
    opacity: 0;
  }

  //:not(.tab-selected):hover svg {
  //  --start-color: #FF0000;
  //  --end-color: #FFFF00;
  //}

}


.header-actions {
  ion-icon {
    font-size: 20px;
    cursor: pointer;

    color: var(--color-background-secondary-white-600);

    &:not(:last-of-type) {
      margin-right: 8px;
    }
  }
}

ion-content::part(scroll)::-webkit-scrollbar {
  -webkit-appearance: none;
}

ion-input {
  --background: var(--color-background-primary-black-400) !important;
  --border-radius: var(--radius-medium-small) !important;
  --color: var(--color-background-primary-black-200) !important;
  --padding-start: 16px !important;
  height: 33px;
  font-weight: 300;
}

ion-textarea {
  --background: var(--color-background-primary-black-400) !important;
  --border-radius: var(--radius-medium-small) !important;
  --color: var(--color-background-primary-black-200) !important;
  --padding-start: 16px !important;
}

.set {
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  /* Firefox */
  input[type=number] {
    -moz-appearance: textfield;
  }

  ion-input {
    display: flex;
    align-items: center;
    justify-content: center;
    --border-width: 80%;
  }

  .input-wrapper {
    min-height: 28px;
    height: 28px;
    border: 0.5px solid var(--color-background-primary-black-400);

    .native-input {
      padding-top: 2px;
    }
  }

  .input-highlight {
    width: calc(100% - 4px);
    margin-left: 2px;
  }
}

.workout {
  .input-wrapper {
    border: 0.5px solid var(--color-background-primary-black-400);
  }
}

.margin-right-sm {
  margin-right: 8px;
}

.margin-left-sm {
  margin-left: 8px;
}

.margin-left-m {
  margin-left: 16px;
}

.margin-left-l {
  margin-left: 32px;
}

.margin-right-m {
  margin-right: 16px;
}

.margin-right-l {
  margin-right: 32px;
}

.margin-bottom-sm {
  margin-bottom: 8px;
}

.margin-bottom-m {
  margin-bottom: 16px;
}

.margin-bottom-l {
  margin-bottom: 32px;
}

.margin-top-sm {
  margin-top: 8px;
}

.margin-top-m {
  margin-top: 16px;
}

.margin-top-l {
  margin-top: 32px;
}

.padding-sm {
  padding: 8px;
}

.padding-m {
  padding: 16px;
}

.padding-l {
  padding: 32px;
}

.padding-bottom-m {
  padding-bottom: 16px;
}

.large-icons {
  ion-icon {
    font-size: 24px;
  }
}

.toggle {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  color: var(--color-background-secondary-white-500);

  & > div {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
  }

  ion-label:first-of-type {
    font-size: 16px;
    font-weight: 350;
    margin-bottom: 8px;
  }

  ion-label:only-of-type {
    margin-bottom: 0;
  }

  ion-label:nth-of-type(2) {
    font-size: 12px;
    font-weight: 300;
    color: var(--color-background-primary-black-200);
    text-align: start;
    max-width: 60%;
  }

  ion-toggle {
    &::part(track) {
      width: 28px;
      height: 16px;
    }

    --track-background: var(--color-background-primary-black-400);
    --handle-height: 12px;
    --handle-width: 12px;
  }
}

ion-datetime-button::part(native) {
  background: none;
  color: var(--color-background-secondary-white-500);
  font-size: var(--typefaces-size-3xl);
  font-weight: 350;
  padding: 0;
  margin: 0;
}

ion-datetime-button.empty::part(native) {
  color: var(--color-background-primary-black-400);
}

ion-datetime {
  --background: var(--color-background-primary-black-400);
  --background-rgb: 30, 30, 30;
  --wheel-fade-background-rgb: 15, 15, 15;
  color: var(--color-background-primary-black-300);
}

.bottom-placeholder {
  height: calc(48px + var(--ion-safe-area-bottom));
  width: 100%;
}

ion-searchbar {
  --border-radius: var(--radius-medium) !important;
  --icon-color: var(--color-background-secondary-white-600) !important;
  --color: var(--color-background-secondary-white-600) !important;
  --background: #101010 !important;
  --box-shadow: none !important;
  --placeholder-font-weight: 300 !important;
  font-weight: 300 !important;
  text-align: start !important;
  line-height: normal !important;
  font-size: 12px !important;
  padding: 0 !important;
}

.no-border-radius {
  border-radius: 0 !important;
}

ion-datetime-button {

  &.empty {
    color: var(--color-background-primary-black-400);
  }
}

.simple-input {
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  input[type=number] {
    -moz-appearance: textfield;
  }
}

ion-radio::part(mark) {
  border-color: var(--color-accent-secondary-blue-600) !important;
}

.hero-block {
  z-index: 4;
  border-radius: 0 0 var(--radius-large) var(--radius-large);
  background: var(--color-background-primary-black-700);
}

.fake-background {
  background: linear-gradient(
      0deg,
      rgba(5, 5, 5, 0.5) 0%,
      rgba(5, 5, 5, 0.5) 100%
  ),
  linear-gradient(
      168deg,
      #000 0%,
      rgba(0, 0, 0, 0) 88.51%,
      #181C61 109.65%
  ),
  radial-gradient(
      227.26% 102.31% at 30.23% 78.97%,
      #0A0A0A 33.5%,
      #0A0A0A 70.36%,
      #400405 74.86%,
      #FF0F1E 79.38%,
      #F4FF53 85.74%,
      #0A0A0A 97.75%
  );

  background-blend-mode: normal, exclusion, normal;

  z-index: 2;
  position: absolute;
  left: 0;
  width: 100vw;
  height: 100vh;
}

.filter-searchbar {
  ion-searchbar {
    --border-radius: var(--radius-medium) 0 0 var(--radius-medium) !important;
  }

  .filter {
    position: relative;
    cursor: pointer;
    background: #101010;
    border-radius: 0 var(--radius-medium) var(--radius-medium) 0;
    padding: 11px 12px 11px;

    ion-icon {
      font-size: 20px;
      color: var(--color-background-secondary-white-600);
    }
  }

  .filter::before {
    content: "";
    position: absolute;
    left: 0;
    top: 25%;
    height: 50%;
    width: 1px;
    background-color: var(--color-background-primary-black-400);
  }
}

.mpg-toast {
  --background: var(--color-background-primary-black-700);
  --border-radius: var(--radius-large);
  --color: var(--color-background-secondary-white-600);
  --min-height: 70px;
  bottom: 100px;
  top: unset !important;
  padding: 16px;

  &::part(icon) {
    color: var(--green-500);
  }

  &::part(button cancel) {
    color: var(--color-background-primary-black-200);
  }

  &::part(header) {
    font-size: var(--typefaces-size-2xl);
    font-weight: 400;
  }

  &::part(message) {
    font-weight: 300;
    color: var(--color-background-primary-black-200);
  }
}

.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

