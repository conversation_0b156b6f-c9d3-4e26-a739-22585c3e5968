import { Directive, ElementRef, HostListener } from '@angular/core';

@Directive({
  selector: '[mpgMaxLength]',
})
export class MaxLengthDirective {
  constructor(private el: ElementRef) {}

  @HostListener('keydown', ['$event'])
  limitMaxLength(event: KeyboardEvent) {
    const nativeEl: HTMLInputElement =
      this.el.nativeElement.querySelector('input');

    const maxLength = this.getMaxLength(nativeEl);

    if (event.key !== 'Backspace' && maxLength < nativeEl.value.length) {
      event.preventDefault();
      return;
    }
  }

  private getMaxLength(nativeEl: HTMLInputElement) {
    if (nativeEl.type !== 'number') {
      return nativeEl.maxLength;
    }

    const value = nativeEl.value;
    const numberParts = value.split('.');

    if (numberParts.length === 1) {
      return nativeEl.maxLength;
    }

    return numberParts[0].length + 2;
  }
}
