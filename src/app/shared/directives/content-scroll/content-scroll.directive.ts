import { Directive, OnDestroy, OnInit } from '@angular/core';
import { IonContent } from '@ionic/angular';
import { NavbarService } from '../../services';
import { combineLatestWith, Subscription } from 'rxjs';

@Directive({
  selector: '[mpgContentScroll]',
})
export class ContentScrollDirective implements OnInit, OnDestroy {
  private lastScrollTop = 0;
  private subscription: Subscription;
  private threshold = 10;

  constructor(
    private navbarService: NavbarService,
    private ionContent: IonContent,
  ) {}

  ngOnInit() {
    this.ionContent.scrollEvents = true;
    this.subscription = this.ionContent.ionScroll
      .pipe(combineLatestWith(this.ionContent.getScrollElement()))
      .subscribe(([event, scrollElement]) => {
        const target = event.target as any;

        const scrollTop = event.detail.scrollTop;
        const clientHeight = target.clientHeight;
        const scrollHeight = scrollElement.scrollHeight;

        const isAtBottom = scrollTop + clientHeight >= scrollHeight - 5;

        if (isAtBottom) {
          this.navbarService.hideNavbar();
          return;
        }

        if (scrollTop > this.lastScrollTop + this.threshold) {
          this.navbarService.hideNavbar();
        }

        if (scrollTop < this.lastScrollTop - this.threshold / 4) {
          this.navbarService.showNavbar();
        }

        this.lastScrollTop = scrollTop;
      });
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
