import {
  AfterViewInit,
  Directive,
  ElementRef,
  EventEmitter,
  Input,
  Output,
} from '@angular/core';
import { Swiper, SwiperOptions } from 'swiper/types';

type SwiperHtmlElement = HTMLElement & { swiper?: Swiper } & {
  initialize: () => void;
};

@Directive({
  selector: '[mpgSwiper]',
})
export class SwiperDirective implements AfterViewInit {
  @Input() config?: SwiperOptions;
  @Output() onIndexChange = new EventEmitter<number>();

  private readonly swiperElement: SwiperHtmlElement;

  constructor(private el: ElementRef<SwiperHtmlElement>) {
    this.swiperElement = el.nativeElement;
  }

  ngAfterViewInit() {
    Object.assign(this.swiperElement, this.config);

    this.swiperElement.initialize();
    this.swiperElement.swiper.on('slideChange', (swiper) => {
      this.onIndexChange.next(swiper.activeIndex);
    });
  }
}
