.main-container {
  padding: 0 30px;
  border-radius: 24px;
  padding-bottom: 8px;
}

.week-container {
  box-shadow: 0 -0.25px 0 var(--color-background-primary-black-300);
  cursor: pointer;
  color: var(--color-background-primary-black-300, #4D4D4D);
  font-size: 12px;
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
  padding: 16px 10px 8px;

  .date {
    text-align: center;
  }

  .date-number {
    font-size: var(--typefaces-size-2xl);
    font-weight: 350;
  }

  .day {
    font-weight: 350;
  }

  .selected {
    color: var(--color-background-secondary-white-600, #F5F5FF);

    .date-number {
      font-size: var(--typefaces-size-3xl);
      font-weight: 400;
    }

    z-index: 333;
  }
}

.calendar {
  position: fixed;
  z-index: 100;
  background: var(--color-background-primary-black-700);
  width: calc(100%);
  border-radius: 0 0 var(--radius-large) var(--radius-large);
  overflow: hidden;
  max-width: 600px;
  left: 50%;
  transform: translateX(-50%);
  padding: 0 30px;

  .btn {
    margin: 0 auto;
    width: 90%;
    max-width: 400px;
  }
}

.grabber {
  width: 144px;
  height: 5px;
  border-radius: 100px;
  background: var(--color-background-primary-black-400);
  margin: 0 auto;
}

.grabber-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px 0;
}


.top-container {
  align-items: start !important;
  position: absolute;
  bottom: 0;
  width: 100vw;
  left: 0;
  z-index: 222;
  height: 25px;

  .square {
    width: 20px;
    height: 100%;
    background: var(--color-background-primary-black-700);
  }

  .line {
    height: 16px;
    flex-grow: 1;
    background: var(--color-background-primary-black-700);
  }
}

bottom-container {
  position: absolute;
  bottom: 32px;
  left: 50%;
  transform: translateX(-50%);
}

.backdrop {
  position: absolute;
  top: 135px;
  left: 0;
  z-index: 99;
  background: rgba(0, 0, 0, 0.5);
  height: 100vh;
  width: 100vw;
  touch-action: none;
}
