import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  OnInit,
  Output,
  Renderer2,
  ViewChild,
} from '@angular/core';
import { DateService, ModalService } from '../../services';
import { TrainingScheduleModalComponent } from '../../../training/components/training-schedule-modal/training-schedule-modal.component';
import { GestureController } from '@ionic/angular';

@Component({
  selector: 'mpg-date-header',
  templateUrl: './date-header.component.html',
  styleUrls: ['./date-header.component.scss'],
})
export class DateHeaderComponent implements OnInit, AfterViewInit {
  @ViewChild('grabberContainer', { read: ElementRef })
  grabberContainer!: ElementRef;
  @ViewChild('weekContainer', { read: ElementRef }) weekContainer!: ElementRef;
  @ViewChild('grabber', { read: ElementRef }) grabber!: ElementRef;
  @ViewChild('calendar', { read: ElementRef }) calendar!: ElementRef;
  currentWeek: string[] = [];
  currentDate: string;
  expanded = false;
  @Output() onExpand = new EventEmitter<void>();
  @Output() onCollapse = new EventEmitter<void>();
  private readonly MIN_HEIGHT = 0; // Collapsed height (visible grabber)
  private readonly MAX_HEIGHT = 490; // Fully expanded height
  private startY = 0;

  constructor(
    private dateService: DateService,
    private modalService: ModalService,
    private gestureController: GestureController,
    private renderer: Renderer2,
    private cdr: ChangeDetectorRef,
  ) {}

  ngOnInit() {
    this.currentWeek = this.dateService.getCurrentWeek();
    this.currentDate = this.dateService.getCurrentDate();
  }

  handleClick() {
    this.collapse();
    this.modalService
      .create({
        component: TrainingScheduleModalComponent,
      })
      .subscribe();
  }

  ngAfterViewInit() {
    this.renderer.setStyle(
      this.calendar.nativeElement,
      'height',
      `${this.MIN_HEIGHT}px`,
    );

    this.enableDraggingGesture(this.weekContainer);
    this.enableDraggingGesture(this.grabberContainer);
    this.enableDraggingGesture(this.grabber);
  }

  expand(animate = true) {
    if (this.expanded && !animate) {
      return;
    }

    this.expanded = true;
    this.onExpand.emit();

    this.cdr.detectChanges();

    if (animate) {
      this.animateSheet(this.MAX_HEIGHT);
    }
  }

  collapse() {
    if (!this.expanded) {
      return;
    }

    this.expanded = false;
    this.onCollapse.emit();

    this.cdr.detectChanges();

    this.animateSheet(this.MIN_HEIGHT);
  }

  private enableDraggingGesture(elementRef: ElementRef) {
    const threshold = 50; // Pixels needed to trigger expansion/collapse
    let isDraggingDown = false;
    let lastY = 0;
    let lastUpdateTime = 0;

    const gesture = this.gestureController.create({
      el: elementRef.nativeElement,
      gestureName: 'drag',
      threshold: 0,
      onStart: (ev) => {
        if (!this.expanded) {
          this.expand(false);
        }

        this.startY = ev.currentY;
        lastY = ev.currentY;
      },
      onMove: (ev) => {
        if (!this.expanded) {
          this.expand(false);
        }

        const currentTime = Date.now();
        if (currentTime - lastUpdateTime < 16) {
          return;
        }
        lastUpdateTime = currentTime;

        const deltaY = ev.currentY - lastY;
        lastY = ev.currentY;

        isDraggingDown = deltaY > 0;
      },
      onEnd: (ev) => {
        const velocity = ev.velocityY;

        if (
          isDraggingDown &&
          (ev.currentY - this.startY > threshold || velocity > 0.3)
        ) {
          this.expand();
        } else {
          this.collapse();
        }
      },
    });

    gesture.enable(true);
  }

  private animateSheet(targetHeight: number) {
    this.renderer.setStyle(
      this.calendar.nativeElement,
      'transition',
      'height 300ms ease-out',
    );
    this.renderer.setStyle(
      this.calendar.nativeElement,
      'height',
      `${targetHeight}px`,
    );

    setTimeout(() => {
      this.renderer.removeStyle(this.calendar.nativeElement, 'transition');
    }, 300);
  }
}
