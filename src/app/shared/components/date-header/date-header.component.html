<ion-row>
  <ion-col class="ion-no-padding" offset-lg="3" size="12" size-lg="6">
    <div class="p-relative main-container">
      <div #weekContainer class="week-container">
        @for (date of currentWeek; track date) {
        <div [ngClass]="{ selected: date === currentDate }" class="date">
          <span class="date-number">{{ date | date: "d" }}</span>
          <br />
          <span class="day">{{ date | date: "EEE" }}</span>
        </div>
        } @if (expanded) {
        <div class="top-container flex-space-between">
          <div class="square"></div>
          <div class="line"></div>
          <div class="square"></div>
        </div>
        }
      </div>
      <div #calendar class="calendar">
        <mpg-training-schedule-calendar></mpg-training-schedule-calendar>
        <div class="bottom-container">
          <div class="btn">
            <mpg-button (click)="handleClick()" [border]="true" color="dark">
              <ion-icon
                src="/assets/icon/expand.svg"
                style="font-size: 20px"
              ></ion-icon>
            </mpg-button>
          </div>
          <div #grabberContainer class="grabber-container">
            <div class="grabber"></div>
          </div>
        </div>
      </div>
      <div #grabber class="grabber margin-top-m"></div>
    </div>
  </ion-col>
</ion-row>

@if (expanded) {
<div (click)="collapse()" class="backdrop"></div>
}
