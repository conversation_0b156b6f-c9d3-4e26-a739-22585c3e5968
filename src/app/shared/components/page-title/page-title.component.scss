title {
  color: var(--color-background-secondary-white-600);
  font-size: var(--typefaces-size-3xl);
  font-style: normal;
  font-weight: 350;
  line-height: normal;
  display: block;
}

.padding-top {
  ion-col {
    padding-top: 16px;
    margin-bottom: 8px;
  }
}

.main {

  margin-top: 40px;

  ion-col {
    border-top: none;
    box-shadow: 0 0.25px 0 var(--color-background-primary-black-300);
    padding-bottom: 8px;
  }
}

ion-icon {
  cursor: pointer;
  font-size: 25px;
  color: var(--color-background-secondary-white-600);
}

ion-col {
  padding-top: 8px;
  margin-bottom: 16px;

  &.border {
    box-shadow: 0 -0.25px 0 var(--color-background-primary-black-300);
  }
}

