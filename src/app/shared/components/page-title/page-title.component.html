<ion-row [ngClass]="{ main: mainHeader, 'padding-top': paddingTop }">
  <ion-col
    [class.border]="border"
    class="flex-space-between"
    offset-lg="3"
    size="12"
    size-lg="6"
  >
    @if (backUrl) {
    <ion-icon [routerLink]="backUrl" name="chevron-back-outline"></ion-icon>
    }
    <title>{{ title | translate }}</title>
    <ng-content select="[header-actions]"></ng-content>
  </ion-col>
</ion-row>
