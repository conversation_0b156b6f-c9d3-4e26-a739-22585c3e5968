import {
  Component,
  EventEmitter,
  forwardRef,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { ModalService } from '../../services';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { IonInput } from '@ionic/angular';
import { debounceTime, Observable, Subject } from 'rxjs';
import { Page } from '../../models';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'mpg-entity-select',
  templateUrl: './entity-select.component.html',
  styleUrls: ['./entity-select.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => EntitySelectComponent),
      multi: true,
    },
  ],
})
export class EntitySelectComponent implements ControlValueAccessor, OnInit {
  @Input() label = 'Select entity';
  @Input() entities: any[] = [];
  @Input() entityIdentifierProp = 'id';
  @Input() entityDisplayProp: string;
  @Input() formControlName: string;
  @Input() initialSelectedValue: string;
  @Input() initialDisplayValue: string;
  @Input() entitySearchProps: string[] = [];
  @Input() formatter?: (entity: any) => string;
  @Input() formatterTranslateFallback = false;
  @Input() searchObservableGetter?: (search?: string) => Observable<Page<any>>;
  @Output() onNewEntity = new EventEmitter<any>();

  @ViewChild('ionInput') ionInput: IonInput;

  displayValue = '';
  private onChange: (value: any) => void;
  private onTouched: () => void;
  private onInputClicked$ = new Subject<void>();

  constructor(
    private modalService: ModalService,
    private translateService: TranslateService,
  ) {}

  ngOnInit() {
    this.onInputClicked$.pipe(debounceTime(200)).subscribe(() => {
      this.openModal();
    });
  }

  handleClick() {
    this.onInputClicked$.next();
  }

  handleKeyDown(event: KeyboardEvent) {
    event.preventDefault();
    return false;
  }

  registerOnChange(fn: (value: any) => void): void {
    this.onChange = fn;
    this.setInitialValue();
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
    this.setInitialValue();
  }

  setDisabledState(isDisabled: boolean): void {}

  writeValue(value: any): void {
    this.displayValue = value && this.translateService.instant(`${value}`);
  }

  openModal() {
    this.modalService
      .createEntitySelector({
        title: this.label,
        entities: this.entities,
        entityDisplayProp: this.entityDisplayProp,
        entitySearchProps: this.entitySearchProps,
        formatter: this.formatter,
        formatterTranslateFallback: this.formatterTranslateFallback,
        searchObservableGetter: this.searchObservableGetter,
        size: 'medium',
      })
      .subscribe((entity) => {
        const displayValue = this.formatter
          ? this.formatter(entity)
          : entity[this.entityDisplayProp];
        this.displayValue = this.translateService.instant(displayValue);
        this.onChange(this.getNestedValue(entity, this.entityIdentifierProp));
        this.onNewEntity.emit(entity);
      });
  }

  private setInitialValue() {
    if (!this.onTouched || !this.onChange || !this.initialSelectedValue) {
      return;
    }

    const initialEntity = this.entities.find(
      (e) =>
        this.getNestedValue(e, this.entityIdentifierProp) ===
        this.initialSelectedValue,
    );

    const displayValue = this.formatter
      ? this.formatter(initialEntity)
      : initialEntity[this.entityDisplayProp];

    this.displayValue = this.translateService.instant(displayValue);
    this.onChange(this.initialSelectedValue);
    this.onTouched();
  }

  private getNestedValue(entity: any, path: string): any {
    const keys = path.split('.');
    let current = entity;

    for (const key of keys) {
      if (current == null || typeof current !== 'object') {
        return undefined;
      }
      current = current[key];
    }

    return current;
  }
}
