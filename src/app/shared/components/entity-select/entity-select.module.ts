import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { EntitySelectComponent } from './entity-select.component';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';

@NgModule({
  declarations: [EntitySelectComponent],
  exports: [EntitySelectComponent],
  imports: [CommonModule, IonicModule, FormsModule, TranslateModule],
})
export class EntitySelectModule {}
