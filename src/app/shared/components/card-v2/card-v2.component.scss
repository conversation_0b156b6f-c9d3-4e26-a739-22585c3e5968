.container {
  border-radius: var(--radius-large);
  background: var(--color-background-primary-black-600);
  padding: 16px;

  .header {
    margin-bottom: 16px;

    ion-icon {
      color: var(--color-background-secondary-white-600);
      --color: var(--color-background-secondary-white-600);
    }

    ion-label.title {
      color: var(--color-background-secondary-white-600);
      font-size: var(--typefaces-size-2xl);
      font-weight: 300;
      margin-left: 9px;
    }

    ion-label.subtitle {
      color: var(--color-background-secondary-white-900);
      font-size: var(--typefaces-size-l);
      font-weight: 300;
      margin-left: 9px;
    }
  }

  .segments {

    ion-icon {
      font-size: 25px;
      cursor: pointer;

      color: var(--color-background-primary-black-200);

      &:not(:last-of-type) {
        margin-right: 8px;
      }

      &.selected {
        color: var(--color-background-secondary-white-600);
      }

      transition: color 0.5s;
    }
  }

  &.empty {

    .header {
      margin-bottom: 0;
    }
  }
}
