<div [ngClass]="{ empty: noContent }" class="container">
  <div class="header flex-space-between">
    <div class="title flex-centered">
      <ion-icon
        [name]="icon"
        [src]="iconSrc"
        [style]="{ 'font-size': iconSize + 'px' }"
      ></ion-icon>
      <ion-label class="title"
        >{{
          selectedSegment
            ? (selectedSegment.label | translate)
            : (title | translate)
        }}
      </ion-label>
      @if (subtitle) {
      <ion-label class="subtitle">{{ subtitle | translate }} </ion-label>
      }
    </div>
    <div class="header-actions flex-centered">
      @if (button) {
      <ion-icon
        (click)="button.handler()"
        [name]="button.icon"
        [src]="button.iconSrc"
        [style.color]="button.color"
      ></ion-icon>
      }
      <ng-content select="[header-actions]"></ng-content>
    </div>
    @if (segments?.length > 0) {
    <div class="segments">
      @for (segment of segments; track segment) {
      <ion-icon
        (click)="handleSegmentClick(segment)"
        [name]="segment.icon"
        [ngClass]="{ selected: segment.value === selectedSegment.value }"
        [src]="segment.iconSrc"
      ></ion-icon>
      }
    </div>
    }
  </div>
  <div class="content">
    <ng-content></ng-content>
  </div>
</div>
