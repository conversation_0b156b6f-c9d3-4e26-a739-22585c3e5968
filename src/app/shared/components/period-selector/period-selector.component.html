<div class="period-selector flex-space-between">
  <ion-icon
    (click)="onPrev.emit()"
    [ngClass]="{ disabled: isLoading }"
    name="chevron-back-outline"
  ></ion-icon>
  @if (!isLoading && startDate && endDate) {
    <ion-label
      >{{ startDate | date: "MMM d" : undefined : (locale$ | async) }}
      -
      {{ endDate | date: "MMM d, yy" : undefined : (locale$ | async) }}
    </ion-label>
  }
  @if (!isLoading && !startDate && endDate) {
    <ion-label>
      {{ endDate | date: "MMM, y" : undefined : (locale$ | async) }}
    </ion-label>
  }
  @if (isLoading) {
    <ion-spinner></ion-spinner>
  }
  <ion-icon
    (click)="onNext.emit()"
    [ngClass]="{ disabled: isLoading }"
    name="chevron-forward-outline"
  ></ion-icon>
</div>
