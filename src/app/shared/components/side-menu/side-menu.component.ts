import { Component, OnInit, ViewChild } from '@angular/core';
import { IonMenu } from '@ionic/angular';
import { Router } from '@angular/router';
import { UserService } from '../../../auth/services';

import { Locale, LocalizationService } from '../../services';
import { map, tap } from 'rxjs/operators';
import {
  TraineeService,
  WorkoutRecordService,
} from '../../../training/services';
import { TraineeInfo, WorkoutRecord } from '../../../training/models';
import { combineLatestWith, Observable } from 'rxjs';
import { UserRoles } from '../../../auth/models';
import { PushNotificationsService } from '../../../notifications/services';

interface PageLink {
  title: string;
  url: string;
  icon: string;
}

enum Pages {
  TRAINEE_PANEL_PAGES = 'TRAINEE_PANEL_PAGES',
  TRAINER_PANEL_PAGES = 'TRAINER_PANEL_PAGES',
  ADMIN_PANEL_PAGES = 'ADMIN_PANEL_PAGES',
}

@Component({
  selector: 'mpg-side-menu',
  templateUrl: './side-menu.component.html',
  styleUrls: ['./side-menu.component.scss'],
})
export class SideMenuComponent implements OnInit {
  private static readonly TRAINEE_PANEL_PAGES: PageLink[] = [
    {
      title: 'home',
      url: '/trainee/tabs',
      icon: 'home',
    },
    {
      title: 'training.training-schedule',
      url: '/trainee/training-schedule',
      icon: 'calendar',
    },
    {
      title: 'courses.label',
      url: '/trainee/courses',
      icon: 'school',
    },
    {
      title: 'nutrition.promotions',
      url: '/trainee/product-promotions',
      icon: 'pricetags',
    },
    {
      title: 'time-machine',
      url: '/trainee/time-machine',
      icon: 'hourglass',
    },
    {
      title: 'visual-progress.label',
      url: '/trainee/visual-progress/tabs',
      icon: 'body',
    },
    {
      title: 'creatine-tracker.label',
      url: '/trainee/creatine-tracker',
      icon: 'pulse',
    },
    {
      title: 'integrations.label',
      url: '/trainee/integrations',
      icon: 'construct',
    },
    {
      title: 'payments.subscription',
      url: '/trainee/subscription',
      icon: 'card',
    },
    {
      title: 'Legal',
      url: '/trainee/legal',
      icon: 'document-text',
    },
  ];
  private static readonly TRAINER_PANEL_PAGES: PageLink[] = [
    {
      title: 'home',
      url: '/trainer/tabs',
      icon: 'home',
    },
    {
      title: 'training.workouts',
      url: '/trainer/workouts-tabs',
      icon: 'document',
    },
    {
      title: 'invitations.label',
      url: '/trainer/invitations',
      icon: 'mail',
    },
    {
      title: 'courses.label',
      url: '/trainer/courses',
      icon: 'school',
    },
  ];
  private static readonly ADMIN_PANEL_PAGES: PageLink[] = [
    {
      title: 'Exercises',
      url: '/admin/exercises',
      icon: 'body',
    },
    {
      title: 'Notifications',
      url: '/admin/notifications',
      icon: 'notifications-circle',
    },
    {
      title: 'Subscription plans',
      url: '/admin/subscription-plans',
      icon: 'card',
    },
    {
      title: 'Invoices',
      url: '/admin/invoices',
      icon: 'document-text',
    },
    {
      title: 'Users',
      url: '/admin/users',
      icon: 'people',
    },
  ];

  Pages = Pages;
  userRoles$: Observable<UserRoles>;
  trainee$: Observable<TraineeInfo>;
  isOtherTrainee$: Observable<boolean>;
  appPages: PageLink[] = SideMenuComponent.TRAINEE_PANEL_PAGES;
  selectedAppPages = Pages.TRAINEE_PANEL_PAGES;
  selectedIndex = 0;
  isLocaleBG: boolean;
  activeWorkoutRecord: WorkoutRecord;

  @ViewChild('ionMenu') ionMenu: IonMenu;

  isDark: boolean;

  constructor(
    private router: Router,
    private userService: UserService,
    private localizationService: LocalizationService,
    private traineeService: TraineeService,
    private workoutRecordService: WorkoutRecordService,
    private pushNotificationsService: PushNotificationsService,
  ) {
    // const prefersDark = window.matchMedia('(prefers-color-scheme: dark)');
    //
    // this.toggleDarkTheme(prefersDark.matches);
    //
    // // Listen for changes to the prefers-color-scheme media query
    // prefersDark.addEventListener('change', this.toggleDarkTheme);
  }

  ngOnInit() {
    this.userRoles$ = this.userService.userRoles$;
    this.trainee$ = this.traineeService.trainee$;
    this.isOtherTrainee$ = this.traineeService.traineeId$.pipe(
      combineLatestWith(this.userService.loggedUserId$),
      map(([traineeId, userId]) => {
        return traineeId !== userId;
      }),
    );

    this.localizationService.locale$
      .pipe(tap((locale) => (this.isLocaleBG = locale === Locale.BG)))
      .subscribe();

    this.workoutRecordService.activeWorkoutRecord$.subscribe(
      (activeWorkoutRecord) => {
        this.activeWorkoutRecord = activeWorkoutRecord;
        this.addActiveWorkout();
        this.setSelectedIndex();
      },
    );
  }

  handleLogout() {
    this.ionMenu.close();
    this.userService.logout();
  }

  async handleSwitchToPanel(pages: Pages, navigateRequired = false) {
    if (
      [Pages.TRAINEE_PANEL_PAGES, Pages.TRAINER_PANEL_PAGES].includes(pages) &&
      navigateRequired
    ) {
      await this.userService.resetTrainee();
      await this.userService.resetTrainer();
    }

    this.appPages = SideMenuComponent[pages];
    this.selectedAppPages = pages;

    this.addActiveWorkout();
    this.setSelectedIndex();

    if (navigateRequired) {
      await this.router.navigateByUrl(this.appPages[0].url);
    }

    await this.ionMenu.close();
  }

  handleUrlChange(event: any) {
    const url = event.enteringView.url;

    if (url.startsWith('/trainee')) {
      this.handleSwitchToPanel(Pages.TRAINEE_PANEL_PAGES);
    } else if (url.startsWith('/trainer')) {
      this.handleSwitchToPanel(Pages.TRAINER_PANEL_PAGES);
    } else if (url.startsWith('/admin')) {
      this.handleSwitchToPanel(Pages.ADMIN_PANEL_PAGES);
    }
  }

  handleSwitchToDarkMode(value: any) {
    this.toggleDarkTheme(value.detail.checked);
  }

  toggleDarkTheme(shouldAdd) {
    document.body.classList.toggle('dark', shouldAdd);
  }

  handleSwitchLanguage(value: any) {
    if (value.detail.checked) {
      this.localizationService.setLang(Locale.BG);
    } else {
      this.localizationService.setLang(Locale.EN);
    }
  }

  handleTraineeReset() {
    this.handleSwitchToPanel(Pages.TRAINEE_PANEL_PAGES, true);
  }

  handleNotifications() {
    this.pushNotificationsService.requestPerms();
  }

  private addActiveWorkout() {
    if (
      this.activeWorkoutRecord &&
      this.selectedAppPages === Pages.TRAINEE_PANEL_PAGES &&
      !this.appPages.map((p) => p.url).includes('/trainee/active-workout')
    ) {
      this.appPages = [
        {
          title: 'training.active-workout',
          icon: 'barbell',
          url: '/trainee/active-workout',
        },
        ...this.appPages,
      ];
    }
  }

  private setSelectedIndex() {
    const selectionIndex = this.router.url.startsWith('/trainee/active-workout')
      ? 0
      : this.appPages
          .map((p) => p.url)
          .findIndex((u) => this.router.url.startsWith(u));

    this.selectedIndex = selectionIndex !== -1 ? selectionIndex : 0;
  }
}
