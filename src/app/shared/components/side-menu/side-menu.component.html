<ion-split-pane contentId="main-content" when="false">
  <ion-menu #ionMenu contentId="main-content" type="overlay">
    <ion-header>
      <ion-toolbar>
        <ion-thumbnail class="ion-margin" slot="start">
          <img alt="Logo" src="assets/icon/favicon.png" />
        </ion-thumbnail>
        <ion-text class="ion-text-wrap">
          <h4>My Progress Guru</h4>
        </ion-text>
      </ion-toolbar>
    </ion-header>
    <ion-content class="ion-no-padding">
      <ion-list class="ion-no-padding" id="inbox-list">
        @for (p of appPages; track p; let i = $index) {
          <ion-menu-toggle
            auto-hide="false"
            >
            <ion-item
              (click)="selectedIndex = i"
              [class.selected]="selectedIndex == i"
              [routerLink]="[p.url]"
              detail="false"
              lines="none"
              routerDirection="root"
              >
              <ion-icon
                [ios]="p.icon + '-outline'"
                [md]="p.icon + '-sharp'"
                slot="start"
              ></ion-icon>
              <ion-label>{{ p.title | translate }}</ion-label>
            </ion-item>
          </ion-menu-toggle>
        }
      </ion-list>

      <ion-toolbar class="bottom-toolbar">
        <!--        <ion-item detail="false" lines="none">-->
        <!--          <ion-icon ios="moon-outline" md="moon-sharp" slot="start"></ion-icon>-->
        <!--          <ion-label>Dark Mode</ion-label>-->
        <!--          <ion-toggle-->
        <!--            (ionChange)="handleSwitchToDarkMode($event)"-->
        <!--            slot="end"-->
      <!--          ></ion-toggle>-->
    <!--        </ion-item>-->
    @if (trainee$ | async; as trainee) {
      @if (
        selectedAppPages === Pages.TRAINEE_PANEL_PAGES &&
        (userRoles$ | async).trainer
        ) {
        <div
          class="flex-centered"
          >
          <div>
            <ion-title class="ion-text-center ion-margin-top" size="small"
              >Trainee:
            </ion-title>
            <ion-title class="ion-text-center" size="small"
              >{{ trainee.firstName }} {{ trainee.lastName }}
            </ion-title>
          </div>
          @if (isOtherTrainee$ | async) {
            <ion-fab-button
              (click)="handleTraineeReset()"
              size="small"
              >
              <ion-icon name="close-circle"></ion-icon>
            </ion-fab-button>
          }
        </div>
      }
    }
    <div class="language flex-centered">
      <ion-icon src="/assets/icon/en.svg"></ion-icon>
      <ion-toggle
        (ionChange)="handleSwitchLanguage($event)"
        [(ngModel)]="isLocaleBG"
        >
      </ion-toggle>
      <ion-icon src="/assets/icon/bg.svg"></ion-icon>
    </div>
    @if (
      (userRoles$ | async).trainer &&
      selectedAppPages !== Pages.TRAINER_PANEL_PAGES
      ) {
      <ion-button
        (click)="handleSwitchToPanel(Pages.TRAINER_PANEL_PAGES, true)"
        class="ion-margin-horizontal"
        color="medium"
        expand="block"
        >
        <ion-icon
          ios="log-in-outline"
          md="log-in-sharp"
          slot="start"
        ></ion-icon>
        <ion-label>Trainer Panel</ion-label>
      </ion-button>
    }
    @if (selectedAppPages != Pages.TRAINEE_PANEL_PAGES) {
      <ion-button
        (click)="handleSwitchToPanel(Pages.TRAINEE_PANEL_PAGES, true)"
        class="ion-margin-horizontal"
        color="medium"
        expand="block"
        >
        <ion-icon
          ios="log-in-outline"
          md="log-in-sharp"
          slot="start"
        ></ion-icon>
        <ion-label>Trainee Panel</ion-label>
      </ion-button>
    }
    @if (
      (userRoles$ | async).admin &&
      selectedAppPages != Pages.ADMIN_PANEL_PAGES
      ) {
      <ion-button
        (click)="handleSwitchToPanel(Pages.ADMIN_PANEL_PAGES, true)"
        class="ion-margin-horizontal"
        color="secondary"
        expand="block"
        >
        <ion-icon
          ios="log-in-outline"
          md="log-in-sharp"
          slot="start"
        ></ion-icon>
        <ion-label>Admin Panel</ion-label>
      </ion-button>
    }
    @if ((userRoles$ | async).trainer) {
      <ion-button
        (click)="handleNotifications()"
        class="ion-margin-horizontal"
        color="dark"
        expand="block"
        >
        <ion-icon
          ios="log-out-outline"
          md="log-out-sharp"
          slot="start"
        ></ion-icon>
        <ion-label>Notifications</ion-label>
      </ion-button>
    }
    <ion-button
      (click)="handleLogout()"
      class="ion-margin-horizontal"
      expand="block"
      >
      <ion-icon
        ios="log-out-outline"
        md="log-out-sharp"
        slot="start"
      ></ion-icon>
      <ion-label>{{ "buttons.logout" | translate }}</ion-label>
    </ion-button>
  </ion-toolbar>
</ion-content>
</ion-menu>
<ion-router-outlet
  (stackDidChange)="handleUrlChange($event)"
  id="main-content"
></ion-router-outlet>
</ion-split-pane>
