import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ModalService } from '../../services';
import { Button } from '../../models';

@Component({
  selector: 'mpg-modal-layout-v2',
  templateUrl: './modal-layout-v2.component.html',
  styleUrls: ['./modal-layout-v2.component.scss'],
})
export class ModalLayoutV2Component implements OnInit {
  @Input() title: string;
  @Input() subtitle: string;
  @Input() icon: string;
  @Input() iconSrc: string;
  @Input() isBreakpoint = false;
  @Input() button: Button;
  @Input() secondaryButton: Button;
  @Input() scrollY = true;
  @Input() footerBackground = 'var(--color-background-primary-black-700)';
  @Input() heroBlock = false;
  @Input() emitClose = false;
  @Input() showFooter = false;
  @Output() onContentClick = new EventEmitter<void>();

  constructor(private modalService: ModalService) {}

  ngOnInit() {}

  handleClose() {
    this.modalService.closeTopModal(this.emitClose);
  }
}
