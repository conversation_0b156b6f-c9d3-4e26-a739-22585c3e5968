import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ModalLayoutV2Component } from './modal-layout-v2.component';
import { IonicModule } from '@ionic/angular';
import { TranslateModule } from '@ngx-translate/core';
import { ButtonModule } from '../button/button.module';

@NgModule({
  declarations: [ModalLayoutV2Component],
  imports: [CommonModule, IonicModule, TranslateModule, ButtonModule],
  exports: [ModalLayoutV2Component],
})
export class ModalLayoutV2Module {}
