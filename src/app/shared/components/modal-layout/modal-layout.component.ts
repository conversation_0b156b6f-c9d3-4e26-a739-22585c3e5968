import { Component, Input, OnInit } from '@angular/core';
import { But<PERSON> } from '../../models';
import { PopoverService } from '../../services';

@Component({
  selector: 'mpg-modal-layout',
  templateUrl: './modal-layout.component.html',
  styleUrls: ['./modal-layout.component.scss'],
})
export class ModalLayoutComponent implements OnInit {
  @Input() title: string;
  @Input() subtitle: string;
  @Input() button: Button;
  @Input() iconButtons: Button[] = [];
  @Input() popoverMenuButtons: Button[] = [];
  @Input() popoverMenuIconButton: Button;
  @Input() scrollY = true;

  constructor(private popoverService: PopoverService) {}

  ngOnInit() {}

  async handlePopoverMenu(event: MouseEvent) {
    await this.popoverService.handlePopoverMenu(event, this.popoverMenuButtons);
  }
}
