import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ModalLayoutComponent } from './modal-layout.component';
import { IonicModule } from '@ionic/angular';
import { TranslateModule } from '@ngx-translate/core';
import { ScreenshotButtonModule } from '../screenshot-button/screenshot-button.module';

@NgModule({
  declarations: [ModalLayoutComponent],
  imports: [CommonModule, IonicModule, TranslateModule, ScreenshotButtonModule],
  exports: [ModalLayoutComponent],
})
export class ModalLayoutModule {}
