<div class="ion-page modal-layout">
  <mpg-screenshot-button></mpg-screenshot-button>
  <ion-header>
    <ion-toolbar
      [ngClass]="{
        subtitle: subtitle
      }"
      color="dark"
      >
      @if (popoverMenuButtons?.length > 0) {
        <ion-buttons
          class="screenshot-hidden"
          slot="primary"
          >
          <ion-button (click)="handlePopoverMenu($event)">
            <ion-icon
              ios="ellipsis-horizontal"
              md="ellipsis-vertical"
              slot="icon-only"
            ></ion-icon>
          </ion-button>
        </ion-buttons>
      }
      @if (popoverMenuIconButton) {
        <ion-buttons
          class="screenshot-hidden"
          slot="primary"
          >
          <ion-button (click)="popoverMenuIconButton.handler()">
            <ion-icon
              [name]="popoverMenuIconButton.icon"
              slot="icon-only"
            ></ion-icon>
          </ion-button>
        </ion-buttons>
      }
      <ion-title
        [ngClass]="{
          centered: popoverMenuButtons?.length > 0 || popoverMenuIconButton
        }"
        >{{ title | translate }}
      </ion-title>
      @if (subtitle) {
        <ion-chip
        [ngClass]="{
          'left-offset':
            popoverMenuButtons?.length > 0 || popoverMenuIconButton,
          longer: subtitle?.length > 36
        }"
          class="subtitle light"
          >{{ subtitle | translate }}
        </ion-chip>
      }
    </ion-toolbar>
  </ion-header>
  <ion-content [scrollY]="scrollY" class="light-background">
    <ng-content></ng-content>
  </ion-content>
  <ion-footer>
    @if (button) {
      <ion-toolbar>
        <div class="modal-buttons-container">
          <ion-button
            (click)="button.handler()"
            [disabled]="button.disabled && button.disabled()"
            class="ion-margin"
            color="primary"
            >
            {{ button.label | translate }}
          </ion-button>
        </div>
      </ion-toolbar>
    }
    @if (iconButtons.length > 0) {
      <ion-toolbar>
        <div class="flex-centered">
          @for (iconBtn of iconButtons; track iconBtn) {
            <ion-fab-button
              (click)="iconBtn.handler()"
              [class.invisible]="iconBtn.disabled && iconBtn.disabled()"
              [color]="iconBtn.color || 'primary'"
              class="ion-margin-start"
              size="small"
              >
              <ion-icon [name]="iconBtn.icon"></ion-icon>
            </ion-fab-button>
          }
        </div>
      </ion-toolbar>
    }
  </ion-footer>
</div>
