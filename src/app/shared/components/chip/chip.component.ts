import { Component, Input, OnInit } from '@angular/core';

@Component({
  selector: 'mpg-chip',
  templateUrl: './chip.component.html',
  styleUrls: ['./chip.component.scss'],
})
export class ChipComponent implements OnInit {
  @Input() color = 'var(--color-background-primary-black-400)';
  @Input() label: string;
  @Input() icon: string;
  @Input() iconSrc: string;
  @Input() transparent = false;
  @Input() key: any;
  @Input() value: any;

  defaultColor = 'var(--color-background-secondary-white-500)';

  constructor() {}

  ngOnInit() {}
}
