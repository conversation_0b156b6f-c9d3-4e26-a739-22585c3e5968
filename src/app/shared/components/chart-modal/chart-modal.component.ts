import {
  AfterViewInit,
  Component,
  ElementRef,
  Input,
  OnInit,
} from '@angular/core';
import { Button, ChartSeries } from '../../models';
import { ModalService } from '../../services';
import { ApexOptions } from 'ng-apexcharts';
import { ChartService } from '../../services/chart.service';

@Component({
  selector: 'mpg-chart-modal',
  templateUrl: './chart-modal.component.html',
  styleUrls: ['./chart-modal.component.scss'],
})
export class ChartModalComponent implements OnInit, AfterViewInit {
  @Input() title: string;
  @Input() series: ChartSeries[];
  @Input() useDisplayValue = false;
  @Input() pointsLimit = 11;

  limitedSeries: ChartSeries[];
  modalButton: Button;
  chartOptions: ApexOptions = {
    tooltip: {
      enabled: false,
    },
    chart: {
      height: 370,
      type: 'line',
      toolbar: {
        show: false,
      },
      fontFamily: 'Montserrat',
    },
    series: [],
    xaxis: {
      type: 'datetime',
      labels: {
        format: 'dd-MM',
      },
      tooltip: {
        enabled: false,
      },
    },
    grid: {
      padding: {
        left: 30,
        right: 40,
      },
    },
    dataLabels: {
      enabled: true,
    },
    colors: ['#ca3535'],
  };
  heightSet = false;

  constructor(
    private modalService: ModalService,
    private elementRef: ElementRef,
    private chartService: ChartService,
  ) {}

  ngOnInit() {
    this.modalButton = this.modalService.closeButton;

    if (this.useDisplayValue) {
      this.chartOptions.dataLabels = {
        enabled: true,
        formatter: (value, opts) => {
          return this.limitedSeries[opts.seriesIndex][opts.dataPointIndex]
            .displayValue;
        },
        distributed: true,
      };
    }

    this.limitedSeries = this.series.map((s) => {
      const totalPoints = s.length;

      if (totalPoints > this.pointsLimit) {
        const step = Math.floor(totalPoints / this.pointsLimit);
        return s
          .filter((_, index) => index % step === 0)
          .slice(0, this.pointsLimit);
      }

      return s;
    });

    const { min, max } = this.chartService.findMinMax(this.limitedSeries);

    this.chartOptions.series = this.limitedSeries.map((s) => {
      return { data: s };
    });

    this.chartOptions.yaxis = {
      labels: {
        show: false,
      },
      min,
      max,
    };
  }

  ngAfterViewInit(): void {
    this.chartService.setChartHeight(
      this.chartOptions,
      this.elementRef,
      130,
      () => {
        this.heightSet = true;
      },
    );
  }
}
