import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';

@Component({
  selector: 'mpg-circular-progress',
  templateUrl: './circular-progress.component.html',
  styleUrls: ['./circular-progress.component.scss'],
})
export class CircularProgressComponent implements OnChanges {
  @Input() progress = 0;
  @Input() showCheckmarkWhenComplete = true;
  @Input() size = 36;
  @Input() strokeWidth = 3;
  @Input() backgroundColor = '#0e4424';
  @Input() progressColor = 'var(--color-success-confirmation-green-600)';
  @Input() textColor = 'var(--color-success-confirmation-green-600)';
  @Input() animated = true;

  progressValue = 0;

  get styles() {
    return {
      '--progress-end': this.progressValue,
      '--size': `${this.size}px`,
      '--stroke-width': `${this.strokeWidth}px`,
      '--background-color': this.backgroundColor,
      '--progress-color': this.progressColor,
      '--text-color': this.textColor,
    };
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.progress) {
      this.progressValue = this.progress || 0;
    }
  }
}
