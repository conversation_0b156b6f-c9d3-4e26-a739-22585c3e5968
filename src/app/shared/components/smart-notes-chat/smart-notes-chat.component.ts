import {
  Component,
  ElementRef,
  HostBinding,
  Input,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core';

import { Observable, of } from 'rxjs';
import { catchError, switchMap } from 'rxjs/operators';
import {
  SmartNote,
  SmartNoteCreateRequest,
  StorageObject,
  StorageObjectInProgress,
  StorageObjectUploadUrlRequest,
} from '../../models';
import { SmartNoteType } from '../../enumerations';
import {
  FileService,
  ModalService,
  SmartNoteService,
  ToastService,
} from '../../services';
import WaveSurfer from 'wavesurfer.js';
import { UserService } from '../../../auth/services';

interface MessageGroup {
  creatorId: string;
  creatorName: string;
  timestamp: string;
  isCurrentUser: boolean;
  messages: SmartNote[];
}

@Component({
  selector: 'mpg-smart-notes-chat',
  templateUrl: './smart-notes-chat.component.html',
  styleUrls: ['./smart-notes-chat.component.scss'],
})
export class SmartNotesChatComponent implements OnInit, OnDestroy {
  @ViewChild('messagesArea', { static: false }) messagesArea!: ElementRef;
  @ViewChild('fileInput', { static: false }) fileInput!: ElementRef;

  // CSS custom properties binding
  @HostBinding('style.--base-input-height')
  get baseInputHeightCss(): string {
    return `${this.baseInputHeight}px`;
  }

  // Input properties
  @Input() smartNotes: SmartNote[] = [];
  @Input() placeholder: string = 'Type a message...';
  @Input() allowFileUpload: boolean = true;
  @Input() allowVoiceRecording: boolean = true;
  @Input() maxRecordingDuration: number = 300; // 5 minutes
  @Input() createNote!: (
    request: SmartNoteCreateRequest,
  ) => Observable<SmartNote>;
  @Input() getMediaUploadUrl!: (
    model: StorageObjectUploadUrlRequest,
  ) => Observable<StorageObject>;
  @Input() serviceBaseUrl!: string;
  @Input() baseInputHeight: number = 230; // Configurable base input height in pixels

  // Chat properties
  message = '';
  isLoading = false;
  isInputFocused = false;

  // Upload progress
  uploadProgress: StorageObjectInProgress | null = null;

  // Delete functionality
  deleteButtonVisible: { [messageId: string]: boolean } = {};
  deleteHoldTimer: any;

  // Voice recording properties
  mediaRecorder: MediaRecorder | null = null;
  audioChunks: Blob[] = [];
  recordingDuration = 0;
  recordingTimer: any;
  recordingCancelled = false;
  isRecording = false;

  // Real-time recording visualization
  realtimeSoundBars: number[] = Array(20).fill(2);
  audioContext: AudioContext | null = null;
  analyser: AnalyserNode | null = null;
  dataArray: Uint8Array | null = null;
  animationFrame: number | null = null;

  // Voice playback properties
  waveSurfers: { [key: string]: any } = {};
  isVoicePlaying: { [key: string]: boolean } = {};
  voiceProgress: { [key: string]: number } = {};
  voiceDuration: { [key: string]: number } = {};
  voiceCurrentTime: { [key: string]: number } = {};

  // Enums for template
  SmartNoteType = SmartNoteType;

  currentUserId: string;

  constructor(
    private fileService: FileService,
    private toastService: ToastService,
    private smartNoteService: SmartNoteService,
    private modalService: ModalService,
    private userService: UserService,
  ) {}

  // Grouped messages for Instagram-style display
  get groupedMessages(): MessageGroup[] {
    return this.groupMessagesBySender(this.smartNotes);
  }

  ngOnInit(): void {
    // Auto-scroll to bottom after initial load
    setTimeout(() => this.scrollToBottom(), 200);
    // Preload waveforms after a short delay
    setTimeout(() => this.preloadAudioWaveforms(), 300);

    this.userService.loggedUserId$.subscribe((userId) => {
      this.currentUserId = userId;
    });
  }

  ngOnDestroy(): void {
    // Clean up all WaveSurfer instances
    this.clearAllWaveSurfers();

    // Clear recording timer if active
    if (this.recordingTimer) {
      clearInterval(this.recordingTimer);
    }

    // Stop any ongoing recording
    if (this.isRecording && this.mediaRecorder) {
      this.mediaRecorder.stop();
    }

    // Stop real-time visualization
    this.stopRealtimeVisualization();
  }

  // Message handling
  handleSendMessage(): void {
    if (!this.message?.trim() || this.isLoading) {
      return;
    }

    this.isLoading = true;

    const createRequest: SmartNoteCreateRequest = {
      type: SmartNoteType.TEXT,
      content: this.message.trim(),
    };

    this.createNote(createRequest)
      .pipe(
        catchError((error) => {
          console.error('Failed to create text note:', error);
          this.toastService.showErrorToast('smart-notes.create-error');
          return of(null);
        }),
      )
      .subscribe((note) => {
        if (note) {
          this.smartNotes.push(note);
          // Auto-scroll to bottom
          setTimeout(() => this.scrollToBottom(), 100);
        }
        this.isLoading = false;
      });

    this.message = '';
  }

  // Focus handling
  onInputFocus(): void {
    this.isInputFocused = true;
  }

  onInputBlur(): void {
    this.isInputFocused = false;
  }

  // File upload handling
  triggerFileSelect(): void {
    if (this.allowFileUpload) {
      this.fileInput.nativeElement.click();
    }
  }

  handleFileSelect(event: any): void {
    const file = event.target.files[0];
    if (!file) return;

    // Reset file input
    event.target.value = '';

    this.uploadFile(file);
  }

  // Voice recording methods
  async handleStartRecording(): Promise<void> {
    if (!this.allowVoiceRecording) return;

    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

      this.mediaRecorder = new MediaRecorder(stream, {
        mimeType: this.getSupportedMimeType(),
      });

      // Event handlers
      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.audioChunks.push(event.data);
        }
      };

      this.mediaRecorder.onstop = () => {
        this.processRecording();
      };

      // Reset state
      this.audioChunks = [];
      this.recordingDuration = 0;
      this.recordingCancelled = false;
      this.isRecording = true;

      // Start recording
      this.mediaRecorder.start(1000);

      // Start timer
      this.startRecordingTimer();

      // Start real-time visualization
      this.startRealtimeVisualization(stream);
    } catch (error) {
      console.error('Error starting recording:', error);
      this.toastService.showErrorToast('smart-notes.recording-error');
    }
  }

  stopRecording(): void {
    if (this.mediaRecorder && this.isRecording) {
      this.mediaRecorder.stop();
      this.isRecording = false;
      this.clearRecordingTimer();
      this.stopRealtimeVisualization();
    }
  }

  cancelRecording(): void {
    if (this.mediaRecorder && this.isRecording) {
      // Set flag to prevent processing
      this.recordingCancelled = true;

      // Stop recording
      this.mediaRecorder.stop();
      this.isRecording = false;
      this.clearRecordingTimer();
      this.stopRealtimeVisualization();

      // Clear audio chunks to prevent upload
      this.audioChunks = [];
      this.recordingDuration = 0;

      // Stop microphone access
      const stream = this.mediaRecorder.stream;
      if (stream) {
        stream.getTracks().forEach((track) => track.stop());
      }

      this.toastService.showInfoToast('smart-notes.recording-cancelled');
    }
  }

  sendRecording(): void {
    this.stopRecording();
  }

  isCurrentUser(note: SmartNote): boolean {
    return note.creatorId === this.currentUserId;
  }

  formatDuration(seconds: number): string {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }

  // Voice playback methods
  toggleVoicePlayback(messageId: string, audioUrl: string): void {
    const waveSurfer = this.waveSurfers[messageId];

    if (!waveSurfer) {
      // Stop all other playing audio first
      this.stopAllOtherAudio(messageId);

      // Determine if this is own message for color styling
      const message = this.smartNotes.find((note) => note.id === messageId);
      const isOwnMessage = message ? this.isCurrentUser(message) : false;

      // Create new WaveSurfer instance if it doesn't exist
      this.createWaveSurfer(messageId, audioUrl, isOwnMessage);
    } else {
      // Toggle existing WaveSurfer
      if (this.isVoicePlaying[messageId]) {
        waveSurfer.pause();
        this.isVoicePlaying[messageId] = false;
      } else {
        // Stop all other playing audio first
        this.stopAllOtherAudio(messageId);
        waveSurfer.play();
        this.isVoicePlaying[messageId] = true;
      }
    }
  }

  getVoiceDurationDisplay(messageId: string): string {
    const currentTime = this.voiceCurrentTime[messageId] || 0;
    const duration = this.voiceDuration[messageId] || 0;

    if (this.isVoicePlaying[messageId] && currentTime > 0) {
      return this.formatDuration(Math.floor(currentTime));
    }

    return this.formatDuration(Math.floor(duration));
  }

  // Hold-to-delete functionality
  handleMessageHoldStart(messageId: string): void {
    this.deleteHoldTimer = setTimeout(() => {
      this.deleteButtonVisible[messageId] = true;
    }, 500); // Show delete button after 500ms hold
  }

  handleMessageHoldEnd(): void {
    if (this.deleteHoldTimer) {
      clearTimeout(this.deleteHoldTimer);
      this.deleteHoldTimer = null;
    }
  }

  handleDeleteMessage(messageId: string): void {
    this.smartNoteService.delete(this.serviceBaseUrl, messageId).subscribe({
      next: () => {
        // Remove from local array using functional approach
        this.smartNotes = this.smartNotes.filter(
          (note) => note.id !== messageId,
        );

        // Hide delete button
        delete this.deleteButtonVisible[messageId];

        this.toastService.showInfoToast('smart-notes.delete-success');
      },
      error: (error) => {
        console.error('Error deleting smart note:', error);
        this.toastService.showInfoToast('smart-notes.delete-error');
      },
    });
  }

  hideDeleteButton(messageId: string): void {
    delete this.deleteButtonVisible[messageId];
  }

  // Photo click handling
  handlePhotoClick(photoUrl: string): void {
    this.modalService.showPhoto(photoUrl);
  }

  // Image load retry logic
  handleImageLoad(event: any, imageUrl: string): void {
    const img = event.target as HTMLImageElement;

    // If image failed to load (404), retry after a delay
    if (img.naturalWidth === 0) {
      this.retryImageLoad(img, imageUrl, 0);
    }
  }

  handleImageError(event: any, imageUrl: string): void {
    const img = event.target as HTMLImageElement;
    this.retryImageLoad(img, imageUrl, 0);
  }

  private uploadFile(file: File): void {
    this.isLoading = true;

    // Create a fake file input event
    const fakeEvent = {
      target: {
        files: [file],
      },
    };

    this.fileService
      .uploadFile(fakeEvent, this.getMediaUploadUrl, this.serviceBaseUrl)
      .pipe(
        switchMap((storageObject) => {
          if (storageObject.progress < 100) {
            // Still uploading, return progress
            return of(storageObject);
          }

          // Upload complete, create smart note
          const createRequest: SmartNoteCreateRequest = {
            type: this.getFileType(file),
            content: file.name,
            mediaId: storageObject.id,
          };

          return this.createNote(createRequest);
        }),
        catchError((error) => {
          console.error('File upload failed:', error);
          this.toastService.showInfoToast('smart-notes.upload-error');
          return of(null);
        }),
      )
      .subscribe((result) => {
        if (result && 'progress' in result && result.progress < 100) {
          // Show upload progress
          this.uploadProgress = result;
          // Auto-scroll to bottom after progress is shown
          setTimeout(() => this.scrollToBottom(), 100);
        } else if (result && 'id' in result && 'type' in result) {
          // This is a SmartNote, upload is complete
          this.uploadProgress = null;
          this.smartNotes.push(result);
          // Auto-scroll to bottom after DOM update
          setTimeout(() => {
            this.scrollToBottom();
          }, 200);
          this.isLoading = false;
        }
      });
  }

  private getFileType(file: File): SmartNoteType {
    if (file.type.startsWith('image/')) {
      return SmartNoteType.PHOTO;
    } else if (file.type.startsWith('video/')) {
      return SmartNoteType.VIDEO;
    } else if (file.type.startsWith('audio/')) {
      return SmartNoteType.AUDIO;
    }
    return SmartNoteType.TEXT;
  }

  private scrollToBottom(): void {
    if (this.messagesArea?.nativeElement) {
      const element = this.messagesArea.nativeElement;

      // Force immediate scroll
      element.scrollTop = element.scrollHeight;

      // Also try with a small delay to ensure DOM is fully updated
      setTimeout(() => {
        element.scrollTop = element.scrollHeight;
      }, 50);

      // Final attempt with longer delay for complex content
      setTimeout(() => {
        element.scrollTop = element.scrollHeight;
      }, 150);
    }
  }

  // Recording timer methods
  private startRecordingTimer(): void {
    this.recordingTimer = setInterval(() => {
      this.recordingDuration++;
      if (this.recordingDuration >= this.maxRecordingDuration) {
        this.stopRecording();
        this.toastService.showInfoToast('smart-notes.recording-max-duration');
      }
    }, 1000);
  }

  private clearRecordingTimer(): void {
    if (this.recordingTimer) {
      clearInterval(this.recordingTimer);
      this.recordingTimer = null;
    }
  }

  // Real-time visualization methods
  private startRealtimeVisualization(stream: MediaStream): void {
    try {
      this.audioContext = new AudioContext();
      this.analyser = this.audioContext.createAnalyser();

      const source = this.audioContext.createMediaStreamSource(stream);
      source.connect(this.analyser);

      this.analyser.fftSize = 64;
      const bufferLength = this.analyser.frequencyBinCount;
      this.dataArray = new Uint8Array(bufferLength);

      this.animateWaveform();
    } catch (error) {
      console.error('Error setting up audio visualization:', error);
    }
  }

  private animateWaveform(): void {
    if (!this.analyser || !this.dataArray || !this.isRecording) {
      return;
    }

    this.analyser.getByteFrequencyData(this.dataArray);

    // Update sound bars based on frequency data
    for (let i = 0; i < this.realtimeSoundBars.length; i++) {
      const dataIndex = Math.floor(
        (i / this.realtimeSoundBars.length) * this.dataArray.length,
      );
      const amplitude = this.dataArray[dataIndex] || 0;

      // Convert amplitude (0-255) to bar height (2-40px)
      const barHeight = Math.max(2, Math.floor((amplitude / 255) * 38) + 2);
      this.realtimeSoundBars[i] = barHeight;
    }

    // Continue animation
    this.animationFrame = requestAnimationFrame(() => this.animateWaveform());
  }

  private stopRealtimeVisualization(): void {
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame);
      this.animationFrame = null;
    }

    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }

    this.analyser = null;
    this.dataArray = null;

    // Reset bars to minimum height
    this.realtimeSoundBars = Array(20).fill(2);
  }

  // Audio processing methods
  private processRecording(): void {
    if (this.recordingCancelled || this.audioChunks.length === 0) {
      // Recording was cancelled or empty, reset state and don't process
      this.recordingCancelled = false;
      this.audioChunks = [];
      this.recordingDuration = 0;
      return;
    }

    // Create audio blob
    const audioBlob = new Blob(this.audioChunks, {
      type: this.getSupportedMimeType(),
    });

    // Upload the audio file
    this.uploadAudioFile(audioBlob);
  }

  private uploadAudioFile(audioBlob: Blob): void {
    this.isLoading = true;

    // Create file from blob
    const audioFile = new File([audioBlob], `voice-${Date.now()}.webm`, {
      type: audioBlob.type,
    });

    // Create a fake file input event
    const fakeEvent = {
      target: {
        files: [audioFile],
      },
    };

    this.fileService
      .uploadFile(fakeEvent, this.getMediaUploadUrl, this.serviceBaseUrl)
      .pipe(
        switchMap((storageObject) => {
          if (storageObject.progress < 100) {
            // Still uploading, return progress
            return of(storageObject);
          }

          // Upload complete, create smart note
          const createRequest: SmartNoteCreateRequest = {
            type: SmartNoteType.AUDIO,
            content: `Voice message (${this.formatDuration(
              this.recordingDuration,
            )})`,
            mediaId: storageObject.id,
          };

          return this.createNote(createRequest);
        }),
        catchError((error) => {
          console.error('Voice upload failed:', error);
          this.toastService.showInfoToast('smart-notes.voice-upload-error');
          return of(null);
        }),
      )
      .subscribe((result) => {
        if (result && 'progress' in result && result.progress < 100) {
          // Show upload progress
          this.uploadProgress = result;
          setTimeout(() => this.scrollToBottom(), 100);
        } else if (result && 'id' in result && 'type' in result) {
          // This is a SmartNote, upload is complete
          this.uploadProgress = null;
          this.smartNotes.push(result);

          // Auto-scroll to bottom after DOM update
          setTimeout(() => {
            this.scrollToBottom();
          }, 200);

          // Create WaveSurfer for the new voice message
          setTimeout(() => {
            const isOwnMessage = this.isCurrentUser(result);
            this.createWaveSurfer(result.id, result.mediaUrl, isOwnMessage);
          }, 500);

          this.toastService.showInfoToast('smart-notes.voice-upload-success');

          this.isLoading = false;
          // Reset recording state
          this.audioChunks = [];
          this.recordingDuration = 0;
        }
      });
  }

  private getSupportedMimeType(): string {
    const types = [
      'audio/webm;codecs=opus',
      'audio/webm',
      'audio/mp4',
      'audio/mpeg',
    ];

    for (const type of types) {
      if (MediaRecorder.isTypeSupported(type)) {
        return type;
      }
    }

    return 'audio/webm';
  }

  // WaveSurfer methods
  private preloadAudioWaveforms(): void {
    this.smartNotes
      .filter((note) => note.type === SmartNoteType.AUDIO && note.mediaUrl)
      .forEach((audioNote) => {
        if (!this.waveSurfers[audioNote.id]) {
          const isOwnMessage = this.isCurrentUser(audioNote);
          // Delay creation to ensure DOM elements are rendered
          setTimeout(() => {
            this.createWaveSurfer(
              audioNote.id,
              audioNote.mediaUrl,
              isOwnMessage,
            );
          }, 300);
        }
      });
  }

  private clearAllWaveSurfers(): void {
    // Destroy all existing WaveSurfer instances
    Object.keys(this.waveSurfers).forEach((messageId) => {
      const waveSurfer = this.waveSurfers[messageId];
      if (waveSurfer) {
        try {
          waveSurfer.destroy();
        } catch (error) {
          console.warn('Error destroying WaveSurfer instance:', error);
        }
      }
    });

    // Clear the instances object
    this.waveSurfers = {};

    // Reset playback states
    this.isVoicePlaying = {};
    this.voiceProgress = {};
    this.voiceDuration = {};
    this.voiceCurrentTime = {};
  }

  private stopAllOtherAudio(currentMessageId: string): void {
    Object.keys(this.waveSurfers).forEach((messageId) => {
      if (messageId !== currentMessageId && this.isVoicePlaying[messageId]) {
        const waveSurfer = this.waveSurfers[messageId];
        if (waveSurfer) {
          waveSurfer.pause();
          this.isVoicePlaying[messageId] = false;
        }
      }
    });
  }

  private createWaveSurfer(
    messageId: string,
    audioUrl: string,
    isOwnMessage: boolean = false,
  ): void {
    const container = document.getElementById(`waveform-${messageId}`);
    if (!container) {
      console.warn(
        'Waveform container not found for message:',
        messageId,
        'retrying...',
      );
      // Retry after a longer delay if container doesn't exist
      setTimeout(() => {
        this.createWaveSurfer(messageId, audioUrl, isOwnMessage);
      }, 500);
      return;
    }

    // Check if WaveSurfer already exists for this message
    if (this.waveSurfers[messageId]) {
      console.warn('WaveSurfer already exists for message:', messageId);
      return;
    }

    // Instagram-style colors: black-400 for unplayed, black-600 for played
    const waveColor = isOwnMessage
      ? '#9CA3AF' // gray-400
      : 'rgba(255, 255, 255, 0.4)';
    const progressColor = isOwnMessage
      ? '#4B5563' // gray-600
      : 'rgba(255, 255, 255, 0.8)';

    const waveSurfer = WaveSurfer.create({
      container: container,
      waveColor: waveColor,
      progressColor: progressColor,
      cursorColor: 'transparent',
      barWidth: 3,
      barGap: 2,
      barRadius: 2,
      height: 32,
      normalize: true,
      interact: true,
      autoCenter: false,
      url: audioUrl,
    });

    // Store the instance
    this.waveSurfers[messageId] = waveSurfer;

    // Set up event listeners
    waveSurfer.on('ready', () => {
      this.voiceDuration[messageId] = waveSurfer.getDuration();
    });

    waveSurfer.on('audioprocess', () => {
      this.voiceCurrentTime[messageId] = waveSurfer.getCurrentTime();
      this.voiceProgress[messageId] =
        (waveSurfer.getCurrentTime() / waveSurfer.getDuration()) * 100;
    });

    waveSurfer.on('finish', () => {
      this.isVoicePlaying[messageId] = false;
      this.voiceProgress[messageId] = 0;
      this.voiceCurrentTime[messageId] = 0;
    });

    waveSurfer.on('play', () => {
      this.isVoicePlaying[messageId] = true;
    });

    waveSurfer.on('pause', () => {
      this.isVoicePlaying[messageId] = false;
    });
  }

  // Message grouping methods
  private groupMessagesBySender(messages: SmartNote[]): MessageGroup[] {
    if (!messages.length) return [];

    const groups: MessageGroup[] = [];
    let currentGroup: MessageGroup | null = null;

    // Sort messages by creation time
    const sortedMessages = [...messages].sort(
      (a, b) =>
        new Date(a.createdOn).getTime() - new Date(b.createdOn).getTime(),
    );

    sortedMessages.forEach((message) => {
      const isCurrentUser = this.isCurrentUser(message);

      // Check if we should start a new group
      const shouldStartNewGroup =
        !currentGroup ||
        currentGroup.creatorId !== message.creatorId ||
        this.shouldBreakGroup(currentGroup.timestamp, message.createdOn);

      if (shouldStartNewGroup) {
        // Start new group
        currentGroup = {
          creatorId: message.creatorId,
          creatorName: message.creatorName,
          timestamp: message.createdOn,
          isCurrentUser,
          messages: [message],
        };
        groups.push(currentGroup);
      } else {
        // Add to existing group
        currentGroup.messages.push(message);
      }
    });

    return groups;
  }

  private shouldBreakGroup(
    lastTimestamp: string,
    currentTimestamp: string,
  ): boolean {
    const lastTime = new Date(lastTimestamp);
    const currentTime = new Date(currentTimestamp);
    const timeDiff = currentTime.getTime() - lastTime.getTime();

    // Break group if more than 5 minutes apart
    return timeDiff > 5 * 60 * 1000;
  }

  private retryImageLoad(
    img: HTMLImageElement,
    imageUrl: string,
    attempt: number,
  ): void {
    const maxRetries = 5;
    const retryDelay = Math.min(1000 * Math.pow(2, attempt), 10000); // Exponential backoff, max 10s

    if (attempt < maxRetries) {
      setTimeout(() => {
        // Force reload by adding timestamp
        const urlWithTimestamp = `${imageUrl}?t=${Date.now()}`;
        img.src = urlWithTimestamp;

        // Check if image loaded after a short delay
        setTimeout(() => {
          if (img.naturalWidth === 0) {
            this.retryImageLoad(img, imageUrl, attempt + 1);
          }
        }, 500);
      }, retryDelay);
    }
  }
}
