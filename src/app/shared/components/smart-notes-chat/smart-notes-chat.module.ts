import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { TranslateModule } from '@ngx-translate/core';

import { SmartNotesChatComponent } from './smart-notes-chat.component';
import { VideoPlayerModule } from '../video-player/video-player.module';

@NgModule({
  declarations: [
    SmartNotesChatComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    TranslateModule,
    VideoPlayerModule
  ],
  exports: [
    SmartNotesChatComponent
  ]
})
export class SmartNotesChatModule { }
