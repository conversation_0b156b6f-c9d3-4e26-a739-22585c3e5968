import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { TranslateModule } from '@ngx-translate/core';

import { SmartNotesChatComponent } from './smart-notes-chat.component';
import { VideoPlayerDirectiveModule } from '../../directives/video-player/video-player-directive.module';

@NgModule({
  declarations: [
    SmartNotesChatComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    TranslateModule,
    VideoPlayerDirectiveModule
  ],
  exports: [
    SmartNotesChatComponent
  ]
})
export class SmartNotesChatModule { }
