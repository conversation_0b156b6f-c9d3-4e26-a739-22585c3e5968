.swipe {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 99999;
}

.path {
  width: 20px;
  height: 20px;
  position: absolute;
  background: rgba(var(--ion-color-secondary-rgb), 0.5);
  border-radius: 40px;
  top: 28px;
  left: 78px;
  visibility: hidden;
  animation: swipe-dot 2s 0.5s infinite; /* Apply animation directly */
}

.hand-icon {
  position: relative;
  background-image: url('https://i.postimg.cc/8556gm60/hand.png');
  background-repeat: no-repeat;
  background-position: center;
  width: 100px;
  height: 100px;
  animation: swipe-hand 2s infinite; /* Apply animation directly */

  transform-origin: 52% 62%;
}

@keyframes swipe-hand {
  25% {
    transform: translate(20px) rotate(30deg);
  }
  50% {
    transform: translate(-20px) rotate(-15deg);
  }
  100% {
    transform: translate(0px) rotate(0);
  }
}

@keyframes swipe-dot {
  12% {
    visibility: visible;
    width: 40px;
  }
  25% {
    visibility: visible;
    transform: translate(-65px);
    width: 20px;
  }
  26% {
    visibility: hidden;
  }
}
