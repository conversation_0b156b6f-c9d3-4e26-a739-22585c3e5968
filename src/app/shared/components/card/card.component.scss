@import "src/app/shared/styles/shared";

ion-title.left-offset {
  padding-left: 48px;
  padding-right: 0;

  &.centered {
    padding-left: 24px;
  }
}

ion-title {
  font-size: 1.4rem;
}

ion-toolbar.subtitle {
  padding-bottom: 1rem;
  padding-top: 1rem;
}

ion-toolbar {
  &.primary.gradient {
    --background: linear-gradient(to right, var(--ion-color-dark), var(--ion-color-primary), var(--ion-color-primary-shade));
    --color: var(--ion-color-light);
  }

  &.secondary.gradient {
    --background: linear-gradient(to right, #00BFDF, var(--ion-color-secondary-tint), var(--ion-color-secondary-shade));
    --color: var(--ion-color-light);
  }

  &.light.gradient {
    --background: linear-gradient(to right, var(--ion-color-light) 30%, #BEBFC2);
    --color: var(--ion-color-dark);
  }

  &.warning.gradient {
    --background: linear-gradient(to right, #d69429, var(--ion-color-warning-tint), var(--ion-color-warning-shade));
    --color: var(--ion-color-dark);
  }
}

ion-chip.subtitle {
  margin-top: 12px;
}

ion-chip.subtitle.left-offset {
  margin-left: 48px;

  &.centered {
    margin-left: 24px;
  }
}

ion-chip.subtitle.longer {

  @include max-width-md {
    margin-left: 30px;
  }
}

ion-fab-button {
  height: 45px;
  width: 45px;
}

ion-checkbox {
  margin-left: 1rem;
}

.title-wrapper {
  display: inline;

  &.centered {
    position: relative;
    top: 2px;
  }
}
