.button {
  font-size: var(--typefaces-size-2xl);
  color: var(--color-background-primary-black-900);
  border-radius: var(--radius-large);
  padding: 4px 32px;
  height: 36px;
  background-color: var(--color-background-secondary-white-600);
  overflow: hidden !important;

  &.dark {
    background-color: var(--color-background-primary-black-500);
    color: var(--color-background-secondary-white-600);
    box-shadow: 0 0 0 0.5px var(--color-background-primary-black-400);

    &:hover {
      background-color: var(--color-background-primary-black-400);
    }
  }

  &.icon-only {
    padding: 8px 16px;
    height: auto;
  }

  &.rounded {
    padding: 0;
    border-radius: 50%;
  }

  &:hover {
    background-color: var(--color-background-secondary-white-800);
  }

  &.border {
    box-shadow: 0 0 0 1px var(--color-background-primary-black-400);
  }

  &.disabled {
    opacity: 0.1;
    cursor: auto;
  }
}


