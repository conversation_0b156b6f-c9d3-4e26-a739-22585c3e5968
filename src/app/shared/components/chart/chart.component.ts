import {
  Component,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { ApexOptions } from 'ng-apexcharts';
import { StatsPeriod } from '../../enumerations';

@Component({
  selector: 'mpg-chart',
  templateUrl: './chart.component.html',
  styleUrls: ['./chart.component.scss'],
})
export class ChartComponent implements OnInit, OnChanges {
  @Input() chartOptions: ApexOptions;
  @Input() period: StatsPeriod;
  @Input() useDisplayValue = false;
  @Input() mergeOptions = true;

  defaultChartOptions: ApexOptions = {
    tooltip: {
      enabled: true,
    },
    chart: {
      height: 200,
      type: 'area',
      toolbar: {
        show: false,
      },
      fontFamily: 'Bahnschrift',
      zoom: {
        enabled: false,
      },
    },
    series: [],
    xaxis: {
      type: 'datetime',
      labels: {
        format: 'dd.MM',
      },
    },
    grid: {
      padding: {
        left: 30,
        right: 30,
      },
      borderColor: '#4D4D4D',
    },
    dataLabels: {
      enabled: false,
    },
    colors: ['#FF0F0F'],
    fill: {
      type: 'gradient',
      gradient: {
        shade: 'light',
        type: 'vertical',
        gradientToColors: ['rgba(202, 53, 53, 0.1)'],
        stops: [0, 100],
      },
    },
    stroke: {
      curve: 'smooth',
      width: 2,
    },
  };

  constructor() {}

  get mergedChartOptions(): ApexOptions {
    if (!this.mergeOptions) {
      return this.chartOptions;
    }

    return {
      ...this.defaultChartOptions,
      ...this.chartOptions,
    };
  }

  ngOnInit() {
    if (this.useDisplayValue) {
      this.defaultChartOptions.dataLabels = {
        background: {
          foreColor: '#4D4D4D',
          borderColor: undefined,
          padding: 5,
          borderRadius: 5,
        },
        offsetY: -10,
        style: {
          colors: ['#0A0A0A'],
          fontWeight: 'normal',
        },
        enabled: true,
        formatter: (value, opts) => {
          const chartSeries = this.chartOptions.series[opts.seriesIndex] as any;

          return chartSeries.data[opts.dataPointIndex].displayValue;
        },
        distributed: true,
      };
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.period && !changes.period.firstChange) {
      if (changes.period.currentValue === StatsPeriod.WEEK) {
        this.defaultChartOptions.xaxis.labels.format = 'ddd';
        return;
      }

      this.defaultChartOptions.xaxis.labels.format = 'dd.MM';
    }
  }
}
