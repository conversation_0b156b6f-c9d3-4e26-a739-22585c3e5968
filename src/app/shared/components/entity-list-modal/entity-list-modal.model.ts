import { Observable } from 'rxjs';
import { InputType } from '../simple-input-modal/simple-input-modal.model';
import { Button, StorageObjectInProgress } from '../../models';

export interface EntityListModalOptions {
  title: string;
  entities: any[];
  entityTextProp: string;
  createButtonLabel?: string;
  onCreate: (model: any) => Observable<any>;
  onDelete: (id: string) => Observable<void>;
  onEdit: (id: string, model: any) => Observable<void>;
  onUpdate?: (newEntities: any[]) => void;
  selectMode?: boolean;
  selectedEntity?: any;
  inputType?: InputType;
  chipLabel?: string;
  chipPredicate?: (entity: any) => boolean;
  popoverMenuButtonsSupplier?: (entity: any) => Button[];
  photoUrlProp?: string;
  onNewPhoto?: (id: string, event: any) => Observable<StorageObjectInProgress>;
}
