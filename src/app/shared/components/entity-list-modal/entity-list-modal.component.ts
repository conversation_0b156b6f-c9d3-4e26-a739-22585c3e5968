import { Component, Input, OnInit } from '@angular/core';
import { Observable, switchMap } from 'rxjs';
import { InputType } from '../simple-input-modal/simple-input-modal.model';
import { Button, StorageObjectInProgress } from '../../models';
import { ModalService } from '../../services';

@Component({
  selector: 'mpg-entity-list-modal',
  templateUrl: './entity-list-modal.component.html',
  styleUrls: ['./entity-list-modal.component.scss'],
})
export class EntityListModalComponent implements OnInit {
  @Input() title: string;
  @Input() entities: any[] = [];
  @Input() entityTextProp = 'name';
  @Input() createButtonLabel = 'buttons.create';
  @Input() onCreate: (model: any) => Observable<any>;
  @Input() onDelete: (id: string) => Observable<void>;
  @Input() onEdit: (id: string, model: any) => Observable<void>;
  @Input() onUpdate: (newEntities: any[]) => void;
  @Input() selectMode = false;
  @Input() selectedEntity: any;
  @Input() inputType: InputType = 'textarea';
  @Input() chipLabel: string;
  @Input() chipPredicate: (entity: any) => boolean;
  @Input() photoUrlProp = 'photoUrl';
  @Input() onNewPhoto: (
    id: string,
    event: any,
  ) => Observable<StorageObjectInProgress>;
  modalButton: Button;

  constructor(private modalService: ModalService) {}

  @Input() popoverMenuButtonsSupplier: (entity: any) => Button[] = () => [];

  ngOnInit(): void {
    this.modalButton = {
      label: this.createButtonLabel,
      handler: () => {
        this.modalService
          .createSimpleInput({
            title: this.createButtonLabel,
            type: this.inputType,
            label: this.inputType === 'text' ? 'name' : undefined,
          })
          .pipe(
            switchMap((content: string) => {
              return this.onCreate({ [this.entityTextProp]: content });
            }),
          )
          .subscribe((entity) => {
            this.entities = this.entities.concat(entity);

            if (this.onUpdate) {
              this.onUpdate(this.entities);
            }
          });
      },
    };
  }

  handleEntitySelect(entity: any) {
    this.modalService.closeTopModal(entity);
  }
}
