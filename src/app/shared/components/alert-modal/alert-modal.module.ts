import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AlertModalComponent } from './alert-modal.component';
import { IonicModule } from '@ionic/angular';
import { ModalLayoutModule } from '../modal-layout/modal-layout.module';
import { SelectAllDirectiveModule } from '../../directives/select-all/select-all-directive.module';
import { TranslateModule } from '@ngx-translate/core';
import { ReactiveFormsModule } from '@angular/forms';
import { NgApexchartsModule } from 'ng-apexcharts';
import { ButtonModule } from '../button/button.module';
import { ModalLayoutV2Module } from '../modal-layout-v2/modal-layout-v2.module';

@NgModule({
  imports: [
    CommonModule,
    IonicModule,
    ModalLayoutModule,
    SelectAllDirectiveModule,
    TranslateModule,
    ReactiveFormsModule,
    NgApexchartsModule,
    ButtonModule,
    ModalLayoutV2Module,
  ],
  declarations: [AlertModalComponent],
  exports: [AlertModalComponent],
})
export class AlertModalModule {}
