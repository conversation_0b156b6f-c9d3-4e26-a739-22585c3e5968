import { Component, Input, OnInit } from '@angular/core';
import { PopoverController } from '@ionic/angular';
import { Button } from '../../models';

@Component({
  selector: 'mpg-popover-menu',
  templateUrl: './popover-menu.component.html',
  styleUrls: ['./popover-menu.component.scss'],
})
export class PopoverMenuComponent implements OnInit {
  @Input() buttons: Button[];
  @Input() callback?: () => void;

  constructor(private popoverController: PopoverController) {}

  ngOnInit() {}

  async handleButtonClick(handler: () => void) {
    await this.popoverController.dismiss();
    await handler();

    if (this.callback) {
      await this.callback();
    }
  }
}
