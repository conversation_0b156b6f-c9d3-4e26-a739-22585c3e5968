export type InputType =
  | 'text'
  | 'number'
  | 'email'
  | 'textarea'
  | 'select'
  | 'options'
  | 'datetime';

export interface SimpleInputModalOptions {
  type: InputType;
  title: string;
  label?: string;
  value?: any;
  options?: any[];
  optionValueProp?: string;
  optionLabelProp?: string;
  optionFormatter?: (option: any) => string;
  selectMultiple?: boolean;
  isRequired?: boolean;
  datetimePresentation?: 'date' | 'time' | 'date-time';
  invalidDates?: string[];
  icon?: string;
  iconSrc?: string;
}
