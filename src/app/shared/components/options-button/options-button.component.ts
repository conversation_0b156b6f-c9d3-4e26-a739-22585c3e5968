import { Component, Input, OnInit } from '@angular/core';
import { Button } from '../../models';
import { PopoverService } from '../../services';

@Component({
  selector: 'mpg-options-button',
  templateUrl: './options-button.component.html',
  styleUrls: ['./options-button.component.scss'],
})
export class OptionsButtonComponent implements OnInit {
  @Input() options: Button[];

  constructor(private popoverService: PopoverService) {}

  ngOnInit() {}

  handleOptionsMenu() {
    this.popoverService.showOptionsModal(this.options);
  }
}
