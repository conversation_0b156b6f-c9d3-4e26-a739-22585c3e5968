import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { OptionsModalComponent } from './options-modal.component';
import { IonicModule } from '@ionic/angular';
import { ModalLayoutModule } from '../modal-layout/modal-layout.module';
import { SelectAllDirectiveModule } from '../../directives/select-all/select-all-directive.module';
import { TranslateModule } from '@ngx-translate/core';
import { ReactiveFormsModule } from '@angular/forms';
import { NgApexchartsModule } from 'ng-apexcharts';
import { ButtonModule } from '../button/button.module';

@NgModule({
  imports: [
    CommonModule,
    IonicModule,
    ModalLayoutModule,
    SelectAllDirectiveModule,
    TranslateModule,
    ReactiveFormsModule,
    NgApexchartsModule,
    ButtonModule,
  ],
  declarations: [OptionsModalComponent],
  exports: [OptionsModalComponent],
})
export class OptionsModalModule {}
