<div class="container">
  <h1 class="header">{{ "options" | translate }}</h1>
  @for (option of options; track option; let last = $last) {
    <div
      (click)="handleOptionClick(option)"
      [ngClass]="{ border: !last }"
      class="option ion-activatable overflow-hidden flex-start"
      >
      <ion-ripple-effect></ion-ripple-effect>
      <ion-icon
        [name]="option.icon"
      [ngStyle]="{
        color: option?.colorVar ? 'var(--' + option.colorVar + ')' : undefined
      }"
        [src]="option.iconSrc"
      ></ion-icon>
      <ion-label>{{ option.label | translate }}</ion-label>
    </div>
  }
</div>
