import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { ModalService } from '../../services';
import { IonSearchbar } from '@ionic/angular';
import { Observable } from 'rxjs';
import { Page } from '../../models';

@Component({
  selector: 'mpg-entity-selector-modal',
  templateUrl: './entity-selector-modal.component.html',
  styleUrls: ['./entity-selector-modal.component.scss'],
})
export class EntitySelectorModalComponent implements OnInit {
  @Input() title: string;
  @Input() entities: any[] = [];
  @Input() entityDisplayProp: string;
  @Input() entitySearchProps: string[] = [];
  @Input() formatter?: (entity: any) => string;
  @Input() formatterTranslateFallback = false;
  @Input() searchObservableGetter?: (search?: string) => Observable<Page<any>>;

  @ViewChild('searchbar') searchbar: IonSearchbar;

  filteredEntities: any[] = [];

  constructor(private modalService: ModalService) {}

  ngOnInit() {
    if (this.searchObservableGetter) {
      this.fetchEntities();
    }

    this.filteredEntities = this.entities.slice(0);

    setTimeout(() => {
      this.searchbar.setFocus();
    }, 100);
  }

  handleSearch(event: any) {
    const search = event.detail.value.toLowerCase();

    if (this.searchObservableGetter) {
      this.fetchEntities(search ? search : undefined);
      return;
    }

    if (search === '') {
      this.filteredEntities = this.entities.slice(0);
    }

    const searchProps: string[] =
      this.entitySearchProps.length > 0
        ? this.entitySearchProps
        : [this.entityDisplayProp];

    this.filteredEntities = this.entities.filter((entity) => {
      return searchProps.some(
        (prop) => entity[prop] && entity[prop].toLowerCase().includes(search),
      );
    });
  }

  handleSelect(entity: any) {
    this.modalService.closeTopModal(entity);
  }

  private fetchEntities(search?: string) {
    this.searchObservableGetter(search).subscribe((page) => {
      this.entities = page.content;
      this.filteredEntities = page.content;
    });
  }
}
