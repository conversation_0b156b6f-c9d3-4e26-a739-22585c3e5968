<input
  #imageInput
  (change)="handleImageSelect($event)"
  accept="image/*"
  class="invisible"
  type="file"
  />
<ion-list class="ion-no-margin ion-no-padding">
  @for (entity of entities; track entity; let i = $index; let last = $last) {
    @if (loadingEntityId === entity.id && loadingProgress > 0) {
      <ion-item>
        @if (loadingProgress < 100) {
          <ion-progress-bar
            [value]="loadingProgress / 100"
          ></ion-progress-bar>
        }
      </ion-item>
    }
    @if (loadingEntityId !== entity.id || !loadingProgress) {
      <ion-item-sliding
        #itemSliding
        [disabled]="!isEntityEditable(entity)"
        >
        <ion-item
          (click)="handleEntityClick(entity)"
          [button]="selectMode || entity[photoUrlProp]"
          [color]="selectMode && selectedEntity === entity ? 'dark' : undefined"
          [lines]="last ? 'none' : undefined"
          >
          @if (chipPredicate && chipPredicate(entity)) {
            <ion-chip
              [ngClass]="{ light: selectMode && selectedEntity === entity }"
              >{{ chipLabel | translate }}
            </ion-chip>
          }
          @if (entity[photoUrlProp]) {
            <ion-thumbnail slot="start">
              <ion-img [src]="entity[photoUrlProp]"></ion-img>
            </ion-thumbnail>
          }
          <ion-label class="ion-text-center">
            <ion-text>
              <pre>{{ entity[entityTextProp] }}</pre>
            </ion-text>
          </ion-label>
        </ion-item>
        <ion-item-options>
          @if (onNewPhoto) {
            <ion-item-option
          (click)="
            imageInput.click(); loadingEntityId = entity.id; itemSliding.close()
          "
              class="transparent"
              >
              <ion-icon color="dark" name="camera" slot="icon-only"></ion-icon>
            </ion-item-option>
          }
          <ion-item-option
            (click)="handleEntityPopoverMenu($event, itemSliding, entity)"
            class="transparent"
            >
            <ion-icon
              color="dark"
              name="ellipsis-vertical"
              slot="icon-only"
            ></ion-icon>
          </ion-item-option>
        </ion-item-options>
      </ion-item-sliding>
    }
  }
</ion-list>
