import { Injectable } from '@angular/core';
import { BehaviorSubject, lastValueFrom } from 'rxjs';
import { TraineeInfo } from '../../training/models';
import { TraineeService } from '../../training/services';
import { NavigationExtras, Router } from '@angular/router';
import { UserService } from '../../auth/services';
import { ModalService } from './modal.service';

@Injectable({
  providedIn: 'root',
})
export class RedirectionService {
  private readonly backUrlSubject$ = new BehaviorSubject<string>(null);
  readonly backUrl$ = this.backUrlSubject$.asObservable();

  constructor(
    private traineeService: TraineeService,
    private userService: UserService,
    private router: Router,
    private modalService: ModalService,
  ) {}

  async switchAndNavigate(
    to: string,
    trainee: TraineeInfo,
    extras?: NavigationExtras,
  ) {
    await this.traineeService.updateTraineeSubject(trainee);
    await this.navigate(to, extras);
  }

  async switchTrainerAndNavigate(
    to: string,
    trainer: TraineeInfo,
    extras?: NavigationExtras,
  ) {
    this.traineeService.updateTrainerSubject(trainer);
    await this.navigate(to, extras);
  }

  async fetchAndNavigate(
    to: string,
    traineeId?: string,
    extras?: NavigationExtras,
  ) {
    if (traineeId) {
      const traineeInfo = await lastValueFrom(
        this.traineeService.getById(traineeId),
      );
      await this.switchAndNavigate(to, traineeInfo, extras);
      return;
    }

    await this.navigate(to, extras);
  }

  async navigate(to: string, extras?: NavigationExtras) {
    this.backUrlSubject$.next(location.pathname + location.search);
    await this.router.navigate([to], extras);
    await this.modalService.hideTopModal();
  }

  async redirectBack() {
    await this.userService.resetTrainee();
    await this.router.navigateByUrl(this.backUrlSubject$.getValue());
    this.backUrlSubject$.next(null);
    this.modalService.showLastHiddenModal();
  }
}
