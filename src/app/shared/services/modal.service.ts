import { Injectable } from '@angular/core';
import {
  Animation,
  AnimationController,
  ModalController,
  ModalOptions,
} from '@ionic/angular';
import { filter, from, Observable, switchMap } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import {
  InputType,
  SimpleInputModalOptions,
} from '../components/simple-input-modal/simple-input-modal.model';
import { SimpleInputModalComponent } from '../components/simple-input-modal/simple-input-modal.component';
import { BreakpointModal, Button } from '../models';
import { ChartModalOptions } from '../components/chart-modal/chart-modal.model';
import { ChartModalComponent } from '../components/chart-modal/chart-modal.component';
import { PhotosModalComponent } from '../components/photos-modal/photos-modal.component';
import { EntitySelectorModalComponent } from '../components/entity-selector-modal/entity-selector-modal.component';
import { EntitySelectorModalOptions } from '../components/entity-selector-modal/entity-selector-modal.model';
import { EntityListModalOptions } from '../components/entity-list-modal/entity-list-modal.model';
import { EntityListModalComponent } from '../components/entity-list-modal/entity-list-modal.component';

export type ModalSize =
  | 'input-number'
  | 'input-text'
  | 'extra-small'
  | 'small'
  | 'below-medium'
  | 'medium'
  | 'large'
  | 'xl'
  | 'fullscreen';

@Injectable({
  providedIn: 'root',
})
export class ModalService {
  private static INPUT_TYPE_CONTENT_HEIGHT_MAP: {
    [key in InputType]?: number;
  } = {
    text: 240,
    number: 270,
    email: 270,
    textarea: 340,
    select: 270,
    datetime: 230,
  };

  private hiddenModals: HTMLIonModalElement[] = [];
  private enterAnimation: (baseEl: HTMLElement) => Animation;
  private leaveAnimation: (baseEl: HTMLElement) => Animation;

  constructor(
    private modalController: ModalController,
    private animationController: AnimationController,
  ) {
    this.enterAnimation = (baseEl: HTMLElement) => {
      const root = baseEl.shadowRoot;

      const backdropAnimation = this.animationController
        .create()
        .addElement(root.querySelector('ion-backdrop')!)
        .fromTo('opacity', '0.01', 'var(--backdrop-opacity)');

      const wrapperAnimation = this.animationController
        .create()
        .addElement(root.querySelector('.modal-wrapper')!)
        .keyframes([
          { offset: 0, opacity: '0', transform: 'scale(0)' },
          { offset: 1, opacity: '0.99', transform: 'scale(1)' },
        ]);

      return this.animationController
        .create()
        .addElement(baseEl)
        .easing('ease-out')
        .duration(300)
        .addAnimation([backdropAnimation, wrapperAnimation]);
    };

    this.leaveAnimation = (baseEl: HTMLElement) => {
      return this.enterAnimation(baseEl).direction('reverse');
    };
  }

  get closeButton(): Button {
    return {
      label: 'buttons.close',
      handler: () => {
        this.closeTopModal();
      },
    };
  }

  async closeTopModal(data?: any) {
    const modal = await this.modalController.getTop();

    await modal.dismiss(data);
  }

  async closeAllModals() {
    let topModal = await this.modalController.getTop();
    while (topModal) {
      topModal.classList.add('invisible');
      await topModal.dismiss();
      topModal = await this.modalController.getTop();
    }
  }

  async hideTopModal() {
    const modal = await this.modalController.getTop();
    if (!modal) {
      return;
    }

    if (modal.classList.contains('mpg-modal-fullscreen')) {
      return;
    }

    modal.classList.add('invisible');

    this.hiddenModals.push(modal);
  }

  createBreakpointModal<T = any>(
    opts: ModalOptions,
    defaultContentHeight?: number,
  ): Observable<T> {
    const component = opts.component as typeof BreakpointModal;
    const contentHeight =
      defaultContentHeight ?? component.contentHeight ?? 400;

    const breakpoint = contentHeight / window.innerHeight;

    return from(
      this.modalController.create({
        ...opts,
        breakpoints: [breakpoint],
        initialBreakpoint: breakpoint,
        cssClass: 'breakpoint-modal',
      }),
    ).pipe(
      switchMap((el) => from(el.present()).pipe(map(() => el))),
      switchMap((el) => {
        return from(el.onDidDismiss<T>()).pipe(
          filter((ev) => !!ev.data),
          map((ev) => ev.data),
        );
      }),
    );
  }

  create<T = any>(opts: ModalOptions): Observable<T> {
    return from(
      this.modalController.create({
        enterAnimation: opts.enterAnimation || this.enterAnimation,
        leaveAnimation: opts.leaveAnimation || this.leaveAnimation,
        ...opts,
        cssClass: this.getModalClass(opts.cssClass),
      }),
    ).pipe(
      switchMap((el) => {
        return from(this.hideTopModal()).pipe(map(() => el));
      }),
      switchMap((el) => from(el.present()).pipe(map(() => el))),
      switchMap((el) => {
        return from(el.onDidDismiss<T>()).pipe(
          tap(() => {
            this.showLastHiddenModal();
          }),
          filter((ev) => !!ev.data),
          map((ev) => ev.data),
        );
      }),
    );
  }

  createSimpleInput<T>(
    opts: SimpleInputModalOptions,
  ): Observable<string | string[] | T> {
    let contentHeight = ModalService.INPUT_TYPE_CONTENT_HEIGHT_MAP[opts.type];
    if (opts.type === 'options') {
      contentHeight = 120 + 56 * opts.options.length;
    }

    return this.createBreakpointModal<string>(
      {
        component: SimpleInputModalComponent,
        componentProps: opts,
      },
      contentHeight,
    );
  }

  createChart(opts: ChartModalOptions) {
    this.create({
      component: ChartModalComponent,
      componentProps: opts,
    }).subscribe();
  }

  createEntitySelector<T = any>(opts: EntitySelectorModalOptions) {
    return this.create<T>({
      component: EntitySelectorModalComponent,
      componentProps: {
        ...opts,
      },
    });
  }

  showPhoto(src: string) {
    this.create({
      component: PhotosModalComponent,
      componentProps: {
        src,
      },
    }).subscribe();
  }

  createEntityListModal<T = any>(opts: EntityListModalOptions): Observable<T> {
    return this.create<T>({
      component: EntityListModalComponent,
      componentProps: {
        ...opts,
      },
    });
  }

  showEntityListModal(opts: EntityListModalOptions) {
    this.createEntityListModal(opts).subscribe();
  }

  showLastHiddenModal() {
    const lastHiddenModal = this.hiddenModals.pop();
    if (lastHiddenModal) {
      lastHiddenModal.classList.remove('invisible');
    }
  }

  private getModalClass(otherClasses: string | string[]): string[] {
    return ['mpg-modal', `mpg-modal-fullscreen`].concat(otherClasses);
  }
}
