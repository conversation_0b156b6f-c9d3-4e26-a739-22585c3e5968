import { Injectable } from '@angular/core';
import { Howl } from 'howler';
import { timer } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class AudioService {
  constructor() {}

  play(src: string, onEndCallback?: () => void) {
    timer(100).subscribe(() => {
      const audio = new Howl({
        src: [src],
        html5: true,
        onend: (soundId) => {
          if (onEndCallback) {
            onEndCallback();
          }
        },
      });

      audio.play();
    });
  }
}
