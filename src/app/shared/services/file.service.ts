import { Injectable } from '@angular/core';
import {
  StorageObject,
  StorageObjectInProgress,
  StorageObjectUploadUrlRequest,
} from '../models';
import { filter, Observable, of, switchMap } from 'rxjs';
import {
  HttpClient,
  HttpContext,
  HttpEventType,
  HttpHeaders,
  HttpProgressEvent,
} from '@angular/common/http';
import { map, tap } from 'rxjs/operators';
import { REMOVE_AUTH_HEADER } from '../../core/interceptors';
import { FormControl } from '@angular/forms';
import html2canvas from 'html2canvas';

export type LogoPosition = 'left' | 'right' | 'none';

@Injectable({
  providedIn: 'root',
})
export class FileService {
  constructor(private http: HttpClient) {}

  uploadFile(
    fileSelectionEvent: any,
    uploadUrlGenerator: (
      model: StorageObjectUploadUrlRequest,
    ) => Observable<StorageObject>,
    serviceBaseUrl: string,
    formControl?: FormControl<string>,
  ): Observable<StorageObjectInProgress> {
    const input = fileSelectionEvent.target as HTMLInputElement;
    const file = input.files[0];
    const fileExtension = file.name.split('.').pop();

    return this.uploadFileWithProgress(
      uploadUrlGenerator,
      fileExtension,
      file,
      serviceBaseUrl,
    ).pipe(
      tap((storageObject) => {
        if (formControl && storageObject.progress === 100) {
          formControl.setValue(storageObject.id);
        }
      }),
    );
  }

  downloadFile(url: string, fileName: string) {
    const link = document.createElement('a');
    link.setAttribute('download', fileName);
    link.setAttribute('href', url);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  async shareOrDownloadHtmlElement(
    title: string,
    element: HTMLElement,
    logoPosition: LogoPosition = 'left',
  ) {
    const canvas = await html2canvas(element, {
      x: logoPosition === 'none' ? 0 : logoPosition === 'left' ? -25 : 0,
      y: logoPosition === 'none' ? 0 : -30,
      logging: false,
      width: element.clientWidth + 25,
      height: element.clientHeight + 30,
      backgroundColor: null,
      ignoreElements: (element) => {
        return element.classList.contains('screenshot-hidden');
      },
    });

    const ctx = canvas.getContext('2d');
    const logoImg = new Image();
    logoImg.src = 'assets/icon/favicon.png';

    logoImg.onload = async () => {
      if (logoPosition !== 'none') {
        const rect = element.getBoundingClientRect();
        const logoX = logoPosition === 'left' ? 0 : rect.right - 60;
        ctx.drawImage(logoImg, logoX, rect.top - 30, 60, 60);
      }

      const imageData = canvas.toDataURL('image/png');

      if (navigator.share) {
        try {
          const blob = await fetch(imageData).then((res) => res.blob());
          const file = new File([blob], `${title}.png`, { type: 'image/png' });
          await navigator.share({ files: [file], title });
        } catch (err) {
          console.error('Error sharing the image:', err);
          this.triggerDownload(imageData, title, 'png');
        }
      } else {
        this.triggerDownload(imageData, title, 'png');
      }
    };
  }

  private uploadFileWithProgress(
    uploadUrlGenerator: (
      model: StorageObjectUploadUrlRequest,
    ) => Observable<StorageObject>,
    fileExtension: string,
    file: File,
    serviceBaseUrl: string,
  ): Observable<StorageObjectInProgress> {
    return uploadUrlGenerator({ fileExtension }).pipe(
      switchMap((storageObject) => {
        return this.http
          .put<void>(storageObject.presignedUploadUrl, file, {
            reportProgress: true,
            observe: 'events',
            context: new HttpContext().set(REMOVE_AUTH_HEADER, true),
            headers: new HttpHeaders({ 'ngsw-bypass': '' }),
          })
          .pipe(map((event) => ({ event, storageObject })));
      }),
      filter(({ event }) => event.type === HttpEventType.UploadProgress),
      map(({ event, storageObject }) => {
        const progressEvent = event as HttpProgressEvent;
        const progress = Math.floor(
          (progressEvent.loaded /
            (progressEvent.total || progressEvent.loaded)) *
            100,
        );

        const result: StorageObjectInProgress = {
          ...storageObject,
          progress,
        };

        return result;
      }),
      switchMap((storageObject) => {
        if (storageObject.progress < 100) {
          return of(storageObject);
        }

        return this.http
          .patch<void>(
            `${serviceBaseUrl}/v1/storage-objects/${storageObject.id}`,
            {
              isUploaded: true,
            },
          )
          .pipe(map(() => storageObject));
      }),
    );
  }

  private triggerDownload(url: string, title: string, extension: string) {
    const link = document.createElement('a');
    link.setAttribute('download', `${title}.${extension}`);
    link.setAttribute('href', url);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}
