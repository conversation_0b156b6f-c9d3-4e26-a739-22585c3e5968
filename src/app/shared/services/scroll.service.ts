import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ScrollService {
  scrollToElementById(
    elementId: string,
    options: ScrollIntoViewOptions = { behavior: 'instant', block: 'start' },
  ): void {
    setTimeout(() => {
      const element = document.getElementById(elementId);
      if (element) {
        element.scrollIntoView(options);
      }
    }, 200);
  }
}
