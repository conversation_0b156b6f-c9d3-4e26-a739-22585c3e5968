import { Injectable } from '@angular/core';
import {
  <PERSON><PERSON>,
  Note,
  NotePhotoRequest,
  NoteRequest,
  StorageObject,
  StorageObjectInProgress,
  StorageObjectUploadUrlRequest,
} from '../models';
import { Observable, of, switchMap } from 'rxjs';
import { ModalService } from './modal.service';
import { HttpClient } from '@angular/common/http';
import { map } from 'rxjs/operators';
import { FileService } from './file.service';

@Injectable({
  providedIn: 'root',
})
export class NoteService {
  private static readonly BASE_PATH: string = '/v1/notes';

  constructor(
    private modalService: ModalService,
    private http: HttpClient,
    private fileService: FileService,
  ) {}

  edit(
    serviceBaseUrl: string,
    id: string,
    model: NoteRequest,
  ): Observable<void> {
    return this.http.put<void>(
      `${serviceBaseUrl}${NoteService.BASE_PATH}/${id}`,
      model,
    );
  }

  delete(serviceBaseUrl: string, id: string): Observable<void> {
    return this.http.delete<void>(
      `${serviceBaseUrl}${NoteService.BASE_PATH}/${id}`,
    );
  }

  getPhotoUploadUrl: (
    serviceBaseUrl: string,
    id: string,
  ) => (
    storageObjectUploadUrlRequest: StorageObjectUploadUrlRequest,
  ) => Observable<StorageObject> = (serviceBaseUrl, id) => {
    return (model) => {
      return this.http.post<StorageObject>(
        `${serviceBaseUrl}${NoteService.BASE_PATH}/${id}/upload-url`,
        model,
      );
    };
  };

  putPhoto(
    serviceBaseUrl: string,
    id: string,
    model: NotePhotoRequest,
  ): Observable<void> {
    return this.http.put<void>(
      `${serviceBaseUrl}${NoteService.BASE_PATH}/${id}/photo`,
      model,
    );
  }

  onNewPhoto(
    serviceBaseUrl: string,
    id: string,
    event: any,
  ): Observable<StorageObjectInProgress> {
    return this.fileService
      .uploadFile(
        event,
        this.getPhotoUploadUrl(serviceBaseUrl, id),
        serviceBaseUrl,
      )
      .pipe(
        switchMap((storageObject) => {
          if (storageObject.progress < 100) {
            return of(storageObject);
          }

          return this.putPhoto(serviceBaseUrl, id, {
            photoId: storageObject.id,
          }).pipe(map(() => storageObject));
        }),
      );
  }

  getNewNoteButton(
    onCreate: (model: NoteRequest) => Observable<Note>,
    successCallback: (note: Note) => void,
  ): Button {
    return {
      label: 'new-note',
      handler: () => {
        this.createNoteInputModal()
          .pipe(
            switchMap((content: string) => {
              return onCreate({ content });
            }),
          )
          .subscribe(successCallback);
      },
    };
  }

  private createNoteInputModal(content?: string) {
    return this.modalService.createSimpleInput({
      title: 'new-note',
      type: 'textarea',
      value: content,
    });
  }
}
