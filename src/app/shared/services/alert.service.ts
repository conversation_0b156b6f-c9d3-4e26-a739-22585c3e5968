import { Injectable } from '@angular/core';
import {
  AlertController,
  AlertOptions,
  Animation,
  AnimationController,
  Platform,
} from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { ModalService } from './modal.service';
import { AlertModalComponent } from '../components/alert-modal/alert-modal.component';

@Injectable({
  providedIn: 'root',
})
export class AlertService {
  private enterAnimation: (baseEl: HTMLElement) => Animation;
  private leaveAnimation: (baseEl: HTMLElement) => Animation;

  constructor(
    private alertController: AlertController,
    private translateService: TranslateService,
    private platform: Platform,
    private modalService: ModalService,
    private animationController: AnimationController,
  ) {
    this.enterAnimation = (baseEl: HTMLElement) => {
      const root = baseEl.shadowRoot;

      const backdropAnimation = this.animationController
        .create()
        .addElement(root.querySelector('ion-backdrop')!)
        .fromTo('opacity', '0.01', 'var(--backdrop-opacity)');

      const alertAnimation = this.animationController
        .create()
        .addElement(root.querySelector('.modal-wrapper')!)
        .beforeStyles({ opacity: '1' })
        .keyframes([
          {
            offset: 0,
            opacity: '0',
            transform: 'translateY(100%)',
          },
          {
            offset: 1,
            opacity: '1',
            transform: 'translateY(0)',
          },
        ]);

      return this.animationController
        .create()
        .addElement(baseEl)
        .easing('cubic-bezier(0.25, 0.8, 0.25, 1)')
        .duration(300)
        .addAnimation([backdropAnimation, alertAnimation]);
    };

    this.leaveAnimation = (baseEl: HTMLElement) => {
      return this.enterAnimation(baseEl).direction('reverse');
    };
  }

  async create(opts: AlertOptions): Promise<HTMLIonAlertElement> {
    const el = await this.alertController.create({
      ...opts,
      mode: this.platform.is('android') ? 'md' : 'ios',
    });

    await el.present();

    return el;
  }

  createDeleteAlert(deleteHandler: () => void) {
    return this.createConfirmAlert('alerts.confirm.delete', deleteHandler);
  }

  createCancelAlert(cancelHandler: () => void) {
    return this.createConfirmAlert('alerts.confirm.cancel', cancelHandler);
  }

  async createErrorAlert(error: any) {
    await this.create({
      header: this.translateService.instant('alerts.error'),
      message: error?.message || error?.error?.message || error,
      buttons: ['OK'],
    });
  }

  async createConfirmAlert(
    header: string,
    confirmHandler: () => void,
    translateParams?: object,
  ) {
    this.modalService
      .create({
        component: AlertModalComponent,
        componentProps: {
          header: this.translateService.instant(header, translateParams),
          confirmHandler,
        },
        cssClass: 'alert-modal',
        enterAnimation: this.enterAnimation,
        leaveAnimation: this.leaveAnimation,
      })
      .subscribe();
  }
}
