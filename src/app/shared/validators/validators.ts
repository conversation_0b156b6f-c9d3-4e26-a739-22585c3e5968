import {
  AbstractControl,
  FormGroup,
  ValidationErrors,
  ValidatorFn,
} from '@angular/forms';

export class MPGValidators {
  static atLeastOneTrue(group: AbstractControl): ValidationErrors | null {
    const values = Object.values((group as FormGroup).controls);
    const isAtLeastOneTrue = values.some((control) => control.value === true);
    return isAtLeastOneTrue
      ? null
      : { atLeastOneTrue: 'At least one value must be true' };
  }

  static atLeastOneWithValue(valueToCheck: any) {
    return (group: AbstractControl): ValidationErrors | null => {
      const values = Object.values((group as FormGroup).controls);
      const isAtLeastOneWithGivenValue = values.some(
        (control) => control.value === valueToCheck,
      );
      return isAtLeastOneWithGivenValue
        ? null
        : { atLeastOneWithValue: `At least one value must be ${valueToCheck}` };
    };
  }

  static atLeastTruthyValues(minTruthyValues: number) {
    return (group: AbstractControl): ValidationErrors | null => {
      const controls = Object.values((group as FormGroup).controls);
      const truthyCount = controls.filter((control) => !!control.value).length;

      return truthyCount >= minTruthyValues
        ? null
        : {
            atLeastTruthyValues: `At least ${minTruthyValues} truthy values are required`,
          };
    };
  }

  static atLeastOneTruthyInProvidedControls(controls: string[]) {
    return (group: AbstractControl): ValidationErrors | null => {
      const truthyCount = controls.filter(
        (controlName) => group.get(controlName)?.value,
      ).length;

      return truthyCount > 0
        ? null
        : {
            atLeastOneTruthyInProvidedControls:
              'At least one truthy value is required',
          };
    };
  }

  static endDateAfterStartDate(
    endDateControlName: string,
    startDateControlName: string,
  ) {
    return (group: AbstractControl): ValidationErrors | null => {
      const startControl = group.get(startDateControlName);
      const endControl = group.get(endDateControlName);

      if (startControl && endControl) {
        const startDate = startControl.value;
        const endDate = endControl.value;

        if (startDate && endDate) {
          const start = new Date(startDate);
          const end = new Date(endDate);

          if (end <= start) {
            return {
              dateAfterStartDate: 'End date must be after start date',
            };
          }
        }
      }

      return null;
    };
  }

  static endTimeAfterStartTime(
    endTimeControlName: string,
    startTimeControlName: string,
  ) {
    return (group: AbstractControl): ValidationErrors | null => {
      const startControl = group.get(startTimeControlName);
      const endControl = group.get(endTimeControlName);

      if (startControl && endControl) {
        const startTime = startControl.value;
        const endTime = endControl.value;

        if (startTime && endTime) {
          const startMinutes = this.timeStringToMinutes(startTime);
          const endMinutes = this.timeStringToMinutes(endTime);

          if (endMinutes <= startMinutes) {
            return {
              endTimeAfterStartTime: 'End time must be after start time',
            };
          }
        }
      }

      return null;
    };
  }

  static number(control: AbstractControl): ValidationErrors | null {
    const isValid = /^(\d+|\d+\.\d+|\d+,\d+)$/.test(control.value);
    return isValid ? null : { invalidNumber: { value: control.value } };
  }

  static validValue(invalidValues: any[]) {
    return (control: AbstractControl): ValidationErrors | null => {
      const isValid = !invalidValues.includes(control.value);
      return isValid ? null : { invalidValue: { value: control.value } };
    };
  }

  static notIn(getInvalidValues: () => any[]): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const invalidValues = getInvalidValues();
      const isValid = !invalidValues.includes(control.value);
      return isValid ? null : { invalidValue: { value: control.value } };
    };
  }

  private static timeStringToMinutes(time: string): number {
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
  }
}
