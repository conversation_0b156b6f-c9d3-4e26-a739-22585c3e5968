import { Directive, inject, Input } from '@angular/core';
import { UserService } from '../services';
import { UserRoles } from '../models';
import { NgIf } from '@angular/common';

@Directive({
  selector: '[mpgHasRole]',
  hostDirectives: [NgIf],
})
export class HasRoleDirective {
  private readonly ngIfDirective = inject(NgIf);

  constructor(private userService: UserService) {}

  @Input() set mpgHasRole(roles: (keyof UserRoles)[] | keyof UserRoles) {
    const rolesArr: (keyof UserRoles)[] = Array.isArray(roles)
      ? roles
      : [roles];
    this.userService.userRoles$.subscribe((userRoles) => {
      this.ngIfDirective.ngIf = rolesArr.some((role) => userRoles[role]);
    });
  }
}
