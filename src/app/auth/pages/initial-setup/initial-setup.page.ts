import { Component, OnInit } from '@angular/core';
import { CreateTraineeModalComponent } from '../../../training/components/create-trainee-modal/create-trainee-modal.component';
import { ModalService } from '../../../shared/services';

@Component({
  selector: 'mpg-initial-setup',
  templateUrl: './initial-setup.page.html',
  styleUrls: ['./initial-setup.page.scss'],
})
export class InitialSetupPage implements OnInit {
  constructor(private modalService: ModalService) {}

  ngOnInit() {
    this.handleCreateTrainee();
  }

  handleCreateTrainee() {
    this.modalService
      .create({
        component: CreateTraineeModalComponent,
        canDismiss: false,
        backdropDismiss: false,
        componentProps: {
          title: 'Create an admin',
          initialSetup: true,
        },
      })
      .subscribe();
  }
}
