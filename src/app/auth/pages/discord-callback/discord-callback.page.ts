import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { catchError, map } from 'rxjs/operators';
import { switchMap } from 'rxjs';
import { ToastService } from '../../../shared/services';
import { DiscordService } from '../../../payments/services';

@Component({
  selector: 'mpg-discord-callback',
  templateUrl: './discord-callback.page.html',
  styleUrls: ['./discord-callback.page.scss'],
})
export class DiscordCallbackPage implements OnInit {
  constructor(
    private route: ActivatedRoute,
    private discordService: DiscordService,
    private toastService: ToastService,
    private router: Router,
  ) {}

  ngOnInit() {
    this.route.queryParamMap
      .pipe(
        map((paramMap) => paramMap.get('code')),
        switchMap((code: string) => {
          return this.discordService.join({ code });
        }),
        catchError((err) => {
          this.router.navigate(['trainee/integrations']);
          return this.toastService.handleError(err);
        }),
      )
      .subscribe(() => {
        this.router.navigate(['trainee/integrations']);
        this.toastService.showInfoToast('integrations.discord-success');
      });
  }
}
