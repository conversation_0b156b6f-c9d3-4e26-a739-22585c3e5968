import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { InvitationPageRoutingModule } from './invitation-routing.module';

import { InvitationPage } from './invitation.page';
import { CreateTraineeModalModule } from '../../../training/components/create-trainee-modal/create-trainee-modal.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    InvitationPageRoutingModule,
    CreateTraineeModalModule,
  ],
  declarations: [InvitationPage],
})
export class InvitationPageModule {}
