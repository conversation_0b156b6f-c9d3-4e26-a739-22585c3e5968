import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { IntegrationService } from '../../../training/services';
import { catchError, map } from 'rxjs/operators';
import { switchMap } from 'rxjs';
import { ToastService } from '../../../shared/services';

@Component({
  selector: 'mpg-whoop-callback',
  templateUrl: './whoop-callback.page.html',
  styleUrls: ['./whoop-callback.page.scss'],
})
export class WhoopCallbackPage implements OnInit {
  constructor(
    private route: ActivatedRoute,
    private integrationService: IntegrationService,
    private toastService: ToastService,
    private router: Router,
  ) {}

  ngOnInit() {
    this.route.queryParamMap
      .pipe(
        map((paramMap) => paramMap.get('code')),
        switchMap((code: string) => {
          return this.integrationService.createWhoopIntegration({ code });
        }),
        catchError((err) => {
          this.router.navigate(['trainee/integrations']);
          return this.toastService.handleError(err);
        }),
      )
      .subscribe(() => {
        this.router.navigate(['trainee/integrations']);
        this.toastService.showInfoToast('integrations.whoop-success');
      });
  }
}
