@import "src/app/shared/styles/shared";

.header {
  padding: 2rem;
  text-align: center;


  img {
    width: 25%;
    max-width: 150px;
    margin: 0 auto;
    display: block;
  }

  h1 {
    font-size: 2.1rem;
  }

  h2 {
    font-size: 1.8rem;
  }
}

section {
  padding: 1rem;

  h1 {
    font-size: 2.5rem;
    font-weight: bolder;

    @include max-width-md {
      font-size: 2rem;
    }
  }

  p {
    font-size: 1.4rem;
    margin: 1rem 0;
  }
}

.cta {
  background: linear-gradient(to left, var(--ion-color-dark), var(--ion-color-primary), var(--ion-color-primary-shade));
  width: 300px;
  padding: 12px 0 14px;
  text-align: center;
  margin: 1rem auto;
  font-size: 1.5rem;
  border-radius: 20px;
  cursor: pointer;

  &.mt {
    margin-top: 2rem;
    margin-bottom: 0;
  }

  &:hover {
    background: linear-gradient(to right, var(--ion-color-dark), var(--ion-color-primary), var(--ion-color-primary-shade));
  }
}

.images {
  display: flex;
  flex-wrap: wrap;

  img {
    margin: 1rem 0;
    width: 250px;
    border-radius: 20px;
  }

  @include min-width-lg {
    justify-content: space-around;
    align-items: center;
  }

  @include max-width-md {
    flex-direction: column;
    align-items: center;
    justify-content: space-around;
  }
}

.video-container {
  border-radius: 20px;
  overflow: hidden;
}

.background {
  background: rgba(255, 255, 255, 0.5);
  height: 100vh;
}

.card {
  border-radius: 20px;
  overflow: hidden;
}

.prices {
  background: rgba(255, 255, 255, 0.7);
}

ion-content {
  --background: black;
}

h1, h2, h3, h4, h5, h6, .cta, footer, p {
  color: white;
  text-align: center;
}

footer {
  font-size: 1.2rem;
  margin-top: 1rem;
  padding: 1rem;
}

.overlay-content {
  --background: rgba(0, 0, 0, 0.8);
}

.overlay {
  display: flex;
  height: 100%;
  padding: 1rem;

  @include min-width-lg {
    justify-content: space-around;
    align-items: center;
  }

  @include max-width-md {
    flex-direction: column;
    align-items: center;
    justify-content: space-around;
  }
}
