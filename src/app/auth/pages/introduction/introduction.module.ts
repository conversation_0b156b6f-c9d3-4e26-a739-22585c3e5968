import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { IntroductionPageRoutingModule } from './introduction-routing.module';

import { IntroductionPage } from './introduction.page';
import { VideoPlayerDirectiveModule } from '../../../shared/directives/video-player/video-player-directive.module';
import { CardComponentModule } from '../../../shared/components/card/card.module';
import { LoadingSpinnerComponentModule } from '../../../shared/components/loading-spinner/loading-spinner.module';
import { SubscriptionPlanCardModule } from '../../../payments/components/subscription-plan-card/subscription-plan-card.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    IntroductionPageRoutingModule,
    VideoPlayerDirectiveModule,
    CardComponentModule,
    LoadingSpinnerComponentModule,
    SubscriptionPlanCardModule,
  ],
  declarations: [IntroductionPage],
})
export class IntroductionPageModule {}
