export const environment = {
  production: true,
  TRAINING_SERVICE_API_URL: 'https://training.myprogressguru.com/api',
  NUTRITION_SERVICE_API_URL: 'https://nutrition.myprogressguru.com/api',
  NOTIFICATION_SERVICE_API_URL: 'https://notifications.myprogressguru.com/api',
  PAYMENTS_SERVICE_API_URL: 'https://payments.myprogressguru.com/api',
  WEBSOCKET_URL: 'wss://rtc.myprogressguru.com',
  KEYCLOAK_URL: 'https://auth.myprogressguru.com/',
  BASE_URL: 'https://myprogressguru.com',
  FCM_PUBLIC_KEY:
    'BCIDzpm3shlX5sc5lk5NeZaYw4gFV7iVKWqSI4uHnmoBPoVce_ufVnoShiN1z-AOJcP15kJvCffdbSTy4oVczvs',
  FIREBASE: {
    apiKey: 'AIzaSyDYrU7P5p0Akf2fHspcfvYDTl7dXsIFKqU',
    authDomain: 'myprogressguru-71c9f.firebaseapp.com',
    projectId: 'myprogressguru-71c9f',
    storageBucket: 'myprogressguru-71c9f.appspot.com',
    messagingSenderId: '541341109602',
    appId: '1:541341109602:web:3e4d17ad2a00f511d6f03b',
    measurementId: 'G-KQN4RS062Z',
  },
  MYPOS: {
    SID: '776886',
    WalletNumber: '40849900358',
    KeyIndex: 2,
    URL_Notify: 'https://mypos.myprogressguru.com/notify',
    isSandbox: false,
  },
  INTEGRATIONS: {
    WHOOP: {
      CLIENT_ID: '8a9f4cb5-df19-484e-b7fa-2ab725a632b8',
      CALLBACK_URL: 'https://myprogressguru.com/whoop-callback',
    },
    DISCORD: {
      CLIENT_ID: '1296759594395107360',
      CALLBACK_URL: 'https://myprogressguru.com/discord-callback',
    },
  },
};
