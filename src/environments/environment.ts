// This file can be replaced during build by using the `fileReplacements` array.
// `ng build --prod` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
  production: false,
  TRAINING_SERVICE_API_URL: '/training-api',
  NUTRITION_SERVICE_API_URL: '/nutrition-api',
  NOTIFICATION_SERVICE_API_URL: '/notifications-api',
  PAYMENTS_SERVICE_API_URL: '/payments-api',
  WEBSOCKET_URL: '/',
  KEYCLOAK_URL: 'http://localhost:8080/',
  BASE_URL: 'http://localhost:8100',
  FCM_PUBLIC_KEY:
    'BCIDzpm3shlX5sc5lk5NeZaYw4gFV7iVKWqSI4uHnmoBPoVce_ufVnoShiN1z-AOJcP15kJvCffdbSTy4oVczvs',
  FIREBASE: {
    apiKey: 'AIzaSyDYrU7P5p0Akf2fHspcfvYDTl7dXsIFKqU',
    authDomain: 'myprogressguru-71c9f.firebaseapp.com',
    projectId: 'myprogressguru-71c9f',
    storageBucket: 'myprogressguru-71c9f.appspot.com',
    messagingSenderId: '541341109602',
    appId: '1:541341109602:web:3e4d17ad2a00f511d6f03b',
    measurementId: 'G-KQN4RS062Z',
  },
  MYPOS: {
    SID: '000000000000010',
    WalletNumber: '61938166610',
    KeyIndex: 1,
    URL_Notify: 'https://mypos.dev.myprogressguru.com/notify',
    isSandbox: true
  },
  INTEGRATIONS: {
    WHOOP: {
      CLIENT_ID: 'be1979cb-b12a-4487-8a56-24adefd70360',
      CALLBACK_URL: 'http://localhost:8100/whoop-callback',
    },
    DISCORD: {
      CLIENT_ID: '1296759594395107360',
      CALLBACK_URL: 'http://localhost:8100/discord-callback',
    }
  }
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.
