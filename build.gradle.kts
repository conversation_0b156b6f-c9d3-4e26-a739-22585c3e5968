import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

plugins {
    id("org.springframework.boot") version "3.3.0"
    id("io.spring.dependency-management") version "1.1.5"
    id("org.jetbrains.kotlin.plugin.jpa") version "1.9.21"
    kotlin("jvm") version "1.9.21"
    kotlin("plugin.spring") version "1.9.21"
    kotlin("kapt") version "1.9.21"
}

group = "com.myprogressguru"
version = "0.0.1"

java {
    sourceCompatibility = JavaVersion.VERSION_21
}

extra["springCloudVersion"] = "2023.0.1"

configurations {
    compileOnly {
        extendsFrom(configurations.annotationProcessor.get())
    }
}

val gitLabPrivateToken: String = System.getenv().getOrElse("GITLAB_PRIVATE_TOKEN") {
    projectDir.resolve(".gitlab-private-token").readText().trim()
}


repositories {
    mavenCentral()
    maven {
        url = uri("https://gitlab.com/api/v4/groups/13592976/-/packages/maven")
        name = "GitLab"
        credentials(HttpHeaderCredentials::class) {
            name = "Private-Token"
            value = gitLabPrivateToken
        }
        authentication {
            create<HttpHeaderAuthentication>("header")
        }
    }
}


dependencies {
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springframework.boot:spring-boot-starter-data-jpa")
    implementation("org.springframework.boot:spring-boot-starter-security")
    implementation("org.springframework.boot:spring-boot-starter-oauth2-resource-server")
    implementation("org.springframework.boot:spring-boot-starter-oauth2-client")
    implementation("org.springframework.boot:spring-boot-starter-validation")
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springframework.boot:spring-boot-starter-actuator")
    implementation("org.springframework.boot:spring-boot-starter-data-redis")
    implementation("org.springframework.boot:spring-boot-starter-cache")
    implementation("io.awspring.cloud:spring-cloud-aws-autoconfigure:3.0.2")
    implementation("io.awspring.cloud:spring-cloud-aws-sqs:3.0.2")
    implementation(platform("software.amazon.awssdk:bom:2.30.11"))
    implementation("software.amazon.awssdk:s3")
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin")
    implementation("org.jetbrains.kotlin:kotlin-reflect")
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin:2.14.2")
    implementation("com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.14.2")
    implementation("org.flywaydb:flyway-core:9.21.1")
    implementation("org.jetbrains.kotlin:kotlin-stdlib-jdk8")
    implementation("org.hibernate:hibernate-validator:8.0.0.Final")
    implementation("org.freemarker:freemarker:2.3.31")
    implementation("io.github.oshai:kotlin-logging-jvm:5.1.0")
    implementation("org.springdoc:springdoc-openapi-ui:1.6.15")
    implementation("com.querydsl:querydsl-jpa:5.0.0:jakarta")
    implementation("com.myprogressguru:exception-handler:0.0.30")
    implementation("com.myprogressguru:keycloak-utils:0.0.87")
    implementation("com.myprogressguru:common-utils:0.0.23")
    implementation("com.myprogressguru:cloud-utils:0.0.12")
    implementation("com.myprogressguru:notifications-utils:0.0.11")
    implementation("com.auth0:java-jwt:4.3.0")
    implementation("io.hypersistence:hypersistence-utils-hibernate-62:3.5.2")
    implementation("net.javacrumbs.shedlock:shedlock-spring:5.13.0")
    implementation("net.javacrumbs.shedlock:shedlock-provider-jdbc-template:5.13.0")
    compileOnly("org.mapstruct:mapstruct:1.5.3.Final")
    runtimeOnly("org.postgresql:postgresql:42.5.4")
    runtimeOnly("io.micrometer:micrometer-registry-prometheus:1.10.5")
    annotationProcessor("org.springframework.boot:spring-boot-configuration-processor")
    annotationProcessor("com.querydsl:querydsl-apt:5.0.0:jakarta")
    kapt("org.mapstruct:mapstruct-processor:1.5.3.Final")
    testImplementation("org.springframework.boot:spring-boot-starter-test")
    testImplementation("org.springframework.security:spring-security-test")
    testImplementation("org.mockito.kotlin:mockito-kotlin:5.3.1")
}

tasks.withType<KotlinCompile> {
    kotlinOptions {
        freeCompilerArgs += "-Xjsr305=strict"
        jvmTarget = "21"
    }
}

tasks.withType<Test> {
    useJUnitPlatform()
}
